<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qiyi.vip</groupId>
        <artifactId>viptrade-pay-info</artifactId>
        <version>1.3.51</version>
    </parent>

    <artifactId>viptrade-pay-info-app</artifactId>
    <packaging>jar</packaging>
    <name>viptrade-pay-info-app</name>

    <properties>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-component-catchlog</artifactId>
        </dependency>
        <!-- JSR 303 Validation -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>javax.el</artifactId>
        </dependency>
        <!-- JSR 303 Validation End-->
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
