package com.qiyi.vip.duttype;

import com.qiyi.vip.agreement.query.AgreementQryExe;
import com.qiyi.vip.api.PaymentDutTypeServiceI;
import com.qiyi.vip.catchlog.CatchAndLog;
import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.*;
import com.qiyi.vip.dto.req.SavePaymentDutTypeReq;
import com.qiyi.vip.duttype.query.PaymentDutTypeQryExe;
import com.qiyi.vip.exception.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/8 7:33 PM
 */
@Service
@CatchAndLog
public class PaymentDutTypeImpl implements PaymentDutTypeServiceI {

    @Resource
    private PaymentDutTypeQryExe paymentDutTypeQryExe;

    @Resource
    private AgreementQryExe agreementQryExe;

    /**
     * @param vipType
     * @param amount
     * @param actCode
     * @param dutTypes
     * @return
     * @TODO 获取签约价格，是否要使用有效期。支付渠道规则：旧的要失效，如果这个做兜底的话，旧的还不能失效（需要确认）。
     * @TODO 这个sql还不能加有效期判断
     */
    @Override
    public SingleResponse<PaymentDutTypeDTO> getRenewPrice(Long vipType, Integer amount,
                                                           String actCode, List<Integer> dutTypes) {
        Assert.notNull(vipType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(dutTypes, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(amount, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getRenewPrice(vipType, amount, actCode, dutTypes);
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypes(PaymentDutTypeReqDTO dutTypeReqDTO) {
        Assert.notNull(dutTypeReqDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutTypeInfos(dutTypeReqDTO);
    }

    @Override
    public SingleResponse<Integer> getWechatAmountByDutType(Integer dutType) {
        Assert.notNull(dutType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getWechatAmountByDutType(dutType);
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypeExcludeActCode(Long vipType, Integer amount, String partnerId) {
        Assert.notNull(vipType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(amount, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutTypeExcludeActCode(vipType, amount, partnerId);
    }

    @Override
    public SingleResponse<PaymentDutTypeDTO> getDutTypeByActCode(String actCode) {
        Assert.notNull(actCode, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutTypeByActCode(actCode);
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        Assert.notNull(payChannel, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getPayTypeByPayChannel(payChannel, vipType);
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypeByPayType(Integer payTypee) {
        Assert.notNull(payTypee, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutTypeByPayType(payTypee);
    }

    @Override
    public SingleResponse<Integer> getDutType(PaymentDutTypeReqDTO dutTypeReqDTO) {
        Assert.notNull(dutTypeReqDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(dutTypeReqDTO.getPayType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(dutTypeReqDTO.getAmount(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutType(dutTypeReqDTO);
    }

    @Override
    public MultiResponse<Integer> batchGetDutType(List<PaymentDutTypeReqDTO> dutTypeReqDTOs) {
        if (CollectionUtils.isEmpty(dutTypeReqDTOs)) {
            return MultiResponse.of(Collections.emptyList());
        }
        return paymentDutTypeQryExe.batchGetDutType(dutTypeReqDTOs);
    }

    @Override
    public SingleResponse<AgreementInfoDTO> getAgreementInfo(PaymentDutTypeReqDTO dutTypeReqDTO) {
        Assert.notNull(dutTypeReqDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
//        Assert.notNull(dutTypeReqDTO.getPayType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(dutTypeReqDTO.getAmount(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
//        Assert.notNull(dutTypeReqDTO.getVipType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getAgreementInfo(dutTypeReqDTO);
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypes(Long payType, Integer vipType, Integer amount) {
        Assert.notNull(payType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(amount, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentDutTypeQryExe.getDutTypes(payType, vipType, amount);
    }

    @Override
    public MultiResponse addPaymentDutType(PaymentDutTypeAdminDTO addReqDTO) {
        Integer payType = addReqDTO.getPayType();
        Integer dutType = addReqDTO.getDutType();
        Integer agreementNo = addReqDTO.getAgreementNo();
        Integer amount = addReqDTO.getAmount();
        Long vipType = addReqDTO.getVipType();
        Integer renewPrice = addReqDTO.getRenewPrice();
        String actCode = addReqDTO.getActCode();
        String agreementActCode = addReqDTO.getAgreementActCode();
        Long validStartTime = addReqDTO.getValidStartTime();
        Long validEndTime = addReqDTO.getValidEndTime();
        Assert.notNull(payType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(amount, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(vipType, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(renewPrice, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(validStartTime, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(validEndTime, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addReqDTO.getPayChannel(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        MultiResponse<PaymentDutTypeDTO> uniquePaymentDutType = getUniquePaymentDutType(payType, dutType, agreementNo, amount, vipType, renewPrice, actCode, agreementActCode);
        if (CollectionUtils.isNotEmpty(uniquePaymentDutType.getData())) {
            MultiResponse multiResponse = MultiResponse.buildFailure(ErrorCodeEnum.DUPLICATE_ERR.getCode(), ErrorCodeEnum.DUPLICATE_ERR.getName());
            multiResponse.setData(uniquePaymentDutType.getData());
            return multiResponse;
        }
        boolean flag = paymentDutTypeQryExe.addPaymentDutType(addReqDTO);
        return flag ? MultiResponse.buildSuccess() :
                MultiResponse.buildFailure(ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
    }


    private MultiResponse<PaymentDutTypeDTO> getUniquePaymentDutType(Integer payType, Integer dutType, Integer agreementNo, Integer amount, Long vipType, Integer renewPrice, String actCode, String agreementActCode) {
        return paymentDutTypeQryExe.getUniquePaymentDutType(payType, dutType, agreementNo, amount, vipType, renewPrice, actCode, agreementActCode);
    }

    @Override
    public Response savePaymentDutType(SavePaymentDutTypeReq addReqDTO) {
        Integer agreementNo = addReqDTO.getAgreementNo();
        String agreementActCode = addReqDTO.getPriceActCode();
        Integer type = addReqDTO.getType();
        Long validStartTime = addReqDTO.getValidStartTime();
        Long validEndTime = addReqDTO.getValidEndTime();

        Assert.notNull(agreementNo, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(agreementActCode, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(type, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(validStartTime, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(validEndTime, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());

        SingleResponse<AgreementNoInfoDTO> agreementNoInfo = agreementQryExe.getAgreementNoById(agreementNo);
        if (agreementNoInfo.getData() == null) {
            return Response.buildFailure(ErrorCodeEnum.SAVE_ERR.getCode(), ErrorCodeEnum.SAVE_ERR.getName());
        }

        PaymentDutTypeAdminDTO adminDTO = new PaymentDutTypeAdminDTO();
        AgreementNoInfoDTO data = agreementNoInfo.getData();
        adminDTO.setDutType(data.getDutType());
        adminDTO.setPayChannel(data.getPayChannel());
        adminDTO.setAmount(data.getAmount());
        adminDTO.setSourceVipType(data.getSourceVipType());
        adminDTO.setVipType(data.getVipType());
        adminDTO.setPartnerId(data.getPartnerId());

        adminDTO.setAgreementNo(agreementNo);
        adminDTO.setAgreementActCode(agreementActCode);
        adminDTO.setType(type);
        adminDTO.setValidStartTime(validStartTime);
        adminDTO.setValidEndTime(validEndTime);

        adminDTO.setPayType(null);
        adminDTO.setServiceCode(null);
        adminDTO.setActCode(null);
        adminDTO.setRenewPrice(null);

        // 根据agreementActCode查询payment_dut_type表是否存在对应的记录，若存在，更新，否则就插入
        List<PaymentDutType> paymentDutTypes = paymentDutTypeQryExe.getDutTypeByAgreementActCode(agreementActCode);
        int count = paymentDutTypes == null ? 0 : paymentDutTypes.size();
        boolean flag;

        if (count == 1) {
            // update
            flag = paymentDutTypeQryExe.updatePaymentDutType(adminDTO);
        } else if (count == 0) {
            // insert
            flag = paymentDutTypeQryExe.addPaymentDutType(adminDTO);
        } else {
            return Response.buildFailure(ErrorCodeEnum.SAVE_ERR.getCode(), ErrorCodeEnum.SAVE_ERR.getName());
        }
        return flag? Response.buildSuccess() : Response.buildFailure(ErrorCodeEnum.SAVE_ERR.getCode(), ErrorCodeEnum.SAVE_ERR.getName());
    }
}
