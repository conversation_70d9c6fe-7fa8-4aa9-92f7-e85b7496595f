package com.qiyi.vip.duttype.query;

import com.qiyi.vip.domain.duttype.AgreementInfo;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/8 7:36 PM
 */
@Component
public class PaymentDutTypeQryExe {

    private static final int EMPTY_RESULT = -1;

    public SingleResponse<PaymentDutTypeDTO> getRenewPrice(Long vipType, Integer amount,
                                                           String actCode, List<Integer> dutTypes) {
        PaymentDutType paymentDutType = PaymentDutType.of().getRenewPrice(vipType, amount, actCode, dutTypes);
        if (Objects.isNull(paymentDutType)) {
            return SingleResponse.buildSuccess();
        }
        PaymentDutTypeDTO paymentDutTypeDTO = new PaymentDutTypeDTO();
        BeanUtils.copyProperties(paymentDutType, paymentDutTypeDTO);
        return SingleResponse.of(paymentDutTypeDTO);
    }

    public MultiResponse<PaymentDutTypeDTO> getDutTypeInfos(PaymentDutTypeReqDTO paymentDutTypeReqDTO) {
        List<PaymentDutType> paymentDutTypeList = PaymentDutType.of().getDutTypeInfo(paymentDutTypeReqDTO.getVipType(), paymentDutTypeReqDTO.getAmount(),
            paymentDutTypeReqDTO.getActCode(), paymentDutTypeReqDTO.getDutTypes());
        if (CollectionUtils.isEmpty(paymentDutTypeList)) {
            return MultiResponse.buildSuccess();
        }
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();

        transfer(paymentDutTypeList, paymentDutTypeDTOS);

        return MultiResponse.of(paymentDutTypeDTOS);
    }

    public SingleResponse<Integer> getWechatAmountByDutType(Integer dutType) {
        Integer amount = PaymentDutType.of().getWechatAmountByDutType(dutType);
        return SingleResponse.of(amount);
    }

    public MultiResponse<PaymentDutTypeDTO> getDutTypeExcludeActCode(Long vipType, Integer amount, String partnerId) {
        List<PaymentDutType> paymentDutTypeList = PaymentDutType.of().getDutTypeExcludeActCode(vipType, amount, partnerId);
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();

        transfer(paymentDutTypeList, paymentDutTypeDTOS);
        return MultiResponse.of(paymentDutTypeDTOS);
    }


    public SingleResponse<PaymentDutTypeDTO> getDutTypeByActCode(String actCode) {
        PaymentDutType paymentDutType = PaymentDutType.of().getDutTypeByActCode(actCode);
        if (null == paymentDutType) {
            return SingleResponse.buildSuccess();
        }
        PaymentDutTypeDTO paymentDutTypeDTO = new PaymentDutTypeDTO();
        BeanUtils.copyProperties(paymentDutType, paymentDutTypeDTO);
        return SingleResponse.of(paymentDutTypeDTO);
    }

    public MultiResponse<PaymentDutTypeDTO> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        List<PaymentDutType> paymentDutTypeList = PaymentDutType.of().getPayTypeByPayChannel(payChannel, vipType);
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();

        transfer(paymentDutTypeList, paymentDutTypeDTOS);
        return MultiResponse.of(paymentDutTypeDTOS);
    }

    public MultiResponse<PaymentDutTypeDTO> getDutTypeByPayType(Integer payType) {
        List<PaymentDutType> paymentDutTypeList = PaymentDutType.of().getDutTypeByPayType(payType);
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();
        transfer(paymentDutTypeList, paymentDutTypeDTOS);
        return MultiResponse.of(paymentDutTypeDTOS);
    }

    private void transfer(List<PaymentDutType> paymentDutTypeList, List<PaymentDutTypeDTO> paymentDutTypeDTOS) {
        if (null == paymentDutTypeList) {
            return;
        }
        for (PaymentDutType paymentDutType : paymentDutTypeList) {
            PaymentDutTypeDTO paymentDutTypeDTO = new PaymentDutTypeDTO();
            BeanUtils.copyProperties(paymentDutType, paymentDutTypeDTO);
            paymentDutTypeDTOS.add(paymentDutTypeDTO);
        }
    }

    public SingleResponse<Integer> getDutType(PaymentDutTypeReqDTO dutTypeReqDTO) {
        Integer dutType = PaymentDutType.of()
                .getDutType(
                        dutTypeReqDTO.getPayChannel(),
                        dutTypeReqDTO.getPayType(),
                        dutTypeReqDTO.getSourceVipType(),
                        dutTypeReqDTO.getVipType(),
                        dutTypeReqDTO.getAmount(),
                        dutTypeReqDTO.getActCode(),
                        dutTypeReqDTO.getAgreementActCode(),
                        dutTypeReqDTO.getRenewPrice(),
                        dutTypeReqDTO.getPartnerId());
        return SingleResponse.of(dutType);
    }

    public MultiResponse<Integer> batchGetDutType(List<PaymentDutTypeReqDTO> dutTypeReqDTOs) {
        List<Integer> result = new ArrayList<>();
        for (PaymentDutTypeReqDTO dutTypeReqDTO : dutTypeReqDTOs) {
            if (dutTypeReqDTO.getPayType() == null || dutTypeReqDTO.getAmount() == null || dutTypeReqDTO.getVipType() == null) {
                result.add(EMPTY_RESULT);
                continue;
            }
            AgreementInfo agreementInfo = PaymentDutType.of().getAgreementInfo(
                    dutTypeReqDTO.getPayChannel(),
                    dutTypeReqDTO.getPayType(),
                    dutTypeReqDTO.getSourceVipType(),
                    dutTypeReqDTO.getVipType(),
                    dutTypeReqDTO.getAmount(),
                    dutTypeReqDTO.getActCode(),
                    dutTypeReqDTO.getAgreementActCode(),
                    dutTypeReqDTO.getRenewPrice(),
                    dutTypeReqDTO.getPartnerId());
            int dutType = agreementInfo != null && agreementInfo.getDutType() != null ? agreementInfo.getDutType() : EMPTY_RESULT;
            result.add(dutType);
        }
        return MultiResponse.of(result);
    }

    public SingleResponse<AgreementInfoDTO> getAgreementInfo(PaymentDutTypeReqDTO dutTypeReqDTO) {
        //优化通过支付渠道和sku进行精确路由
        AgreementInfo agreementInfo = null;
        if (StringUtils.isNotBlank(dutTypeReqDTO.getSkuId())) {
            agreementInfo = PaymentDutType.of()
                .getAgreementInfo(dutTypeReqDTO.getPayChannel(),
                    Objects.nonNull(dutTypeReqDTO.getPayType()) ? dutTypeReqDTO.getPayType().intValue() : null, dutTypeReqDTO.getSkuId());
        }
        //路由不到再通过其他维度进行路由
        if (Objects.isNull(agreementInfo)) {
            agreementInfo = PaymentDutType.of()
                .getAgreementInfo(
                    dutTypeReqDTO.getPayChannel(),
                    dutTypeReqDTO.getPayType(),
                    dutTypeReqDTO.getSourceVipType(),
                    dutTypeReqDTO.getVipType(),
                    dutTypeReqDTO.getAmount(),
                    dutTypeReqDTO.getActCode(),
                    dutTypeReqDTO.getAgreementActCode(),
                    dutTypeReqDTO.getRenewPrice(),
                    dutTypeReqDTO.getPartnerId());
        }
        AgreementInfoDTO agreementInfoDTO =
            agreementInfo != null ? new AgreementInfoDTO(agreementInfo.getDutType(), agreementInfo.getAgreementNo()) : null;
        return SingleResponse.of(agreementInfoDTO);
    }

    public MultiResponse<PaymentDutTypeDTO> getDutTypes(Long payType, Integer vipType, Integer amount) {
        List<PaymentDutType> paymentDutTypes = PaymentDutType.of().getDutTypes(payType, vipType, amount);
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();

        transfer(paymentDutTypes, paymentDutTypeDTOS);
        return MultiResponse.of(paymentDutTypeDTOS);
    }

    public MultiResponse<PaymentDutTypeDTO> getUniquePaymentDutType(Integer payType, Integer dutType, Integer agreementNo, Integer amount, Long vipType, Integer renewPrice, String actCode, String agreementActCode) {
        List<PaymentDutType> uniquePaymentDutTypes = PaymentDutType.of()
            .getUniquePaymentDutType(payType, dutType, agreementNo, amount, vipType, renewPrice, actCode, agreementActCode);
        List<PaymentDutTypeDTO> paymentDutTypeDTOS = new ArrayList<>();

        transfer(uniquePaymentDutTypes, paymentDutTypeDTOS);
        return MultiResponse.of(paymentDutTypeDTOS);
    }

    public List<PaymentDutType> getDutTypeByAgreementActCode(String agreementActCode) {
        return PaymentDutType.of().getDutTypeByAgreementActCode(agreementActCode);
    }

    public boolean addPaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        return PaymentDutType.of().getPaymentDutTypeGateway().addPaymentDutType(paymentDutTypeAdminDTO);
    }

    public boolean updatePaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        return PaymentDutType.of().getPaymentDutTypeGateway().updatePaymentDutType(paymentDutTypeAdminDTO);
    }
}