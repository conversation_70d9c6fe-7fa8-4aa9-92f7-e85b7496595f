package com.qiyi.vip.paytype.query;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import com.qiyi.vip.constant.PayTypeEnum;
import com.qiyi.vip.domain.domainservice.TransformService;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import com.qiyi.vip.paytype.assembler.PaymentTypeDTOAssembler;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:34 2021/3/8
 */
@Component
public class PaymentTypeQryExe {

    @Resource
    private PaymentTypeDTOAssembler paymentTypeDTOAssembler;

    @Resource
    private TransformService transformGateway;

    public MultiResponse<PaymentTypeDTO> getPayTypes(List<Long> payTypes) {
        List<PaymentType> paymentTypes = PaymentType.of().getPaymentTypeGateway().getByIds(payTypes);
        if (Objects.nonNull(paymentTypes)) {
            return MultiResponse.of(paymentTypeDTOAssembler.toPaymentTypes(paymentTypes));
        }
        return MultiResponse.buildFailure("INVALID_PAYTYPE", "无相关的payType信息!");
    }

    public SingleResponse<PaymentTypeDTO> getPayType(Long payType) {
        PaymentType paymentType = PaymentType.of().getPaymentTypeGateway().getById(payType);
        if (Objects.nonNull(paymentType)) {
            return SingleResponse.of(PaymentTypeDTOAssembler.toPaymentType(paymentType));
        }
        return SingleResponse.buildFailure("INVALID_PAYTYPE", "无相关的payType信息!");
    }

    public SingleResponse<Long> routePayType(String userAgent, Integer autoRenew) {
        Long payType = PayTypeEnum.routePayType(userAgent, autoRenew);
        return SingleResponse.of(payType);
    }

    public MultiResponse<PaymentTypeDTO> getPasswordFreeSignPayTypes(List<Long> payTypes) {
        List<PaymentType> paymentTypes = PaymentType.of().getPaymentTypeGateway()
                .getPasswordFreeSignPayTypesByIds(payTypes);
        if (Objects.nonNull(paymentTypes)) {
            return MultiResponse.of(paymentTypeDTOAssembler.toPaymentTypes(paymentTypes));
        }
        return MultiResponse.buildFailure("INVALID_PAYTYPE", "无相关的payType信息!");
    }

    public SingleResponse<TransformResDTO> transform(PayTypeTransforReqDTO payTypeTransforReqDTO) {
        TransformResDTO transformResDTO = transformGateway.transform(payTypeTransforReqDTO);
        return SingleResponse.of(transformResDTO);
    }

    public boolean addPayType(AddPayTypeDTO addPayTypeDTO) {
        return PaymentType.of().getPaymentTypeGateway().add(addPayTypeDTO);
    }

    public Boolean updatePayType(UpdatePayTypeDTO updatePayTypeDTO) {
        return PaymentType.of().getPaymentTypeGateway().update(updatePayTypeDTO);
    }

    public MultiResponse<PaymentTypeDTO> getPayTypes(QueryPayTypeInfoDTO queryPayTypeInfoDTO) {
        List<PaymentType> paymentTypes = PaymentType.of().getPaymentTypeGateway().getByProperties(queryPayTypeInfoDTO);
        if (Objects.nonNull(paymentTypes)) {
            return MultiResponse.of(paymentTypeDTOAssembler.toPaymentTypes(paymentTypes));
        }
        return MultiResponse.buildFailure("INVALID_PAYTYPE", "无相关的payType信息!");
    }

    public PageResponse<PaymentTypeDTO> getAdminPayTypes(QueryAdminPayTypeInfoDTO queryInfo) {
        List<PaymentType> adminPayTypes = PaymentType.of().getPaymentTypeGateway().getAdminPayTypesByCondition(queryInfo);
        if (!CollectionUtils.isEmpty(adminPayTypes)) {
            Integer totalCount = PaymentType.of().getPaymentTypeGateway().countAdminPayTypesByCondition(queryInfo);
            return PageResponse.of(paymentTypeDTOAssembler.toPaymentTypes(adminPayTypes), totalCount, queryInfo.getPageSize(), queryInfo.getPageNo());
        }
        return PageResponse.of(queryInfo.getPageSize(), queryInfo.getPageSize());
    }
}
