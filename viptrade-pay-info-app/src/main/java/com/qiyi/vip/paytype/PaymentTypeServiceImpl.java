package com.qiyi.vip.paytype;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import com.qiyi.vip.api.PaymentTypeServiceI;
import com.qiyi.vip.catchlog.CatchAndLog;
import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import com.qiyi.vip.exception.Assert;
import com.qiyi.vip.paytype.query.PaymentTypeQryExe;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:34 2021/3/8
 */
@Service
@CatchAndLog
public class PaymentTypeServiceImpl implements PaymentTypeServiceI {
    @Resource
    PaymentTypeQryExe paymentTypeQryExe;

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes() {
        return paymentTypeQryExe.getPayTypes(Collections.emptyList());
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getAdminPaymentTypes(List<Long> payTypes) {
        return paymentTypeQryExe.getPayTypes(payTypes);
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes(List<Long> payTypes) {
        return paymentTypeQryExe.getPayTypes(payTypes);
    }

    @Override
    public SingleResponse<Long> routePayType(String userAgent, Integer autoRenew) {
        Assert.notNull(userAgent, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(autoRenew, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentTypeQryExe.routePayType(userAgent, autoRenew);
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPasswordFreeSignPayTypes(List<Long> payTypes) {
        Assert.notEmpty(payTypes, ErrorCodeEnum.PARAMETER_ERR.getCode(),ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentTypeQryExe.getPasswordFreeSignPayTypes(payTypes);
    }


    @Override
    public SingleResponse<TransformResDTO> transform(PayTypeTransforReqDTO transformReqDTO) {
        Assert.notNull(transformReqDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(transformReqDTO.getAutoRenew(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(transformReqDTO.getUserId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(transformReqDTO.getPayType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return paymentTypeQryExe.transform(transformReqDTO);
    }

    @Override
    public Response addPayType(AddPayTypeDTO addPayTypeDTO) {
        Assert.notNull(addPayTypeDTO.getPayChannel(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getIsChargeauto(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getName(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getIsBackground(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getIsSupportSign(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getDescription(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(addPayTypeDTO.getStatus(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Boolean flag = paymentTypeQryExe.addPayType(addPayTypeDTO);
        return Boolean.TRUE.equals(flag) ? Response.buildSuccess() :
                Response.buildFailure(ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
    }

    @Override
    public Response updatePayType(UpdatePayTypeDTO updatePayTypeDTO) {
        Assert.notNull(updatePayTypeDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(updatePayTypeDTO.getId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Boolean flag = paymentTypeQryExe.updatePayType(updatePayTypeDTO);
        return flag ? Response.buildSuccess() :
                Response.buildFailure(ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes(QueryPayTypeInfoDTO queryPayTypeInfoDTO) {
        return paymentTypeQryExe.getPayTypes(queryPayTypeInfoDTO);
    }

    @Override
    public PageResponse<PaymentTypeDTO> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO conditionDTO) {
        return paymentTypeQryExe.getAdminPayTypes(conditionDTO);
    }
}
