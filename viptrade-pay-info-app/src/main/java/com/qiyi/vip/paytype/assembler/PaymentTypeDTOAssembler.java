package com.qiyi.vip.paytype.assembler;

import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.domain.paytype.PaymentTypeItem;
import com.qiyi.vip.domain.paytype.PaymentTypeTransform;
import com.qiyi.vip.dto.data.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Component
public class PaymentTypeDTOAssembler {
    /**
     * 转换为支付方式对应的参数实休
     */
    public List<PaymentTypeDTO> toPaymentTypes(List<PaymentType> paymentTypes) {
        if (CollectionUtils.isEmpty(paymentTypes)) {
            return null;
        }
        List<PaymentTypeDTO> paymentTypeDTOS = new ArrayList<>(paymentTypes.size());
        for (PaymentType paymentType : paymentTypes) {
            if (paymentType == null) {
                continue;
            }
            paymentTypeDTOS.add(toPaymentType(paymentType));
        }
        return paymentTypeDTOS;
    }

    public static PaymentTypeDTO toPaymentType(PaymentType paymentType) {
        if (paymentType == null) {
            return null;
        }
        PaymentTypeDTO paymentTypeDTO = PaymentTypeDTO.of();
        BeanUtils.copyProperties(paymentType, paymentTypeDTO);

        if (Objects.nonNull(paymentType.getPaymentTypeItem())) {
            PaymentTypeItemDTO paymentTypeItemDTO = PaymentTypeItemDTO.of();
            PaymentTypeItem paymentTypeItem = paymentType.getPaymentTypeItem();
            BeanUtils.copyProperties(paymentTypeItem, paymentTypeItemDTO);
            paymentTypeDTO.setPaymentTypeItemDTO(paymentTypeItemDTO);
        }

        if (Objects.nonNull(paymentType.getPaymentTypeExtend())) {
            PaymentTypeExtendsDTO paymentTypeExtendsDTO = PaymentTypeExtendsDTO.of();
            BeanUtils.copyProperties(paymentType.getPaymentTypeExtend(), paymentTypeExtendsDTO);
            paymentTypeDTO.setPaymentTypeExtendsDTO(paymentTypeExtendsDTO);
        }

        if (Objects.nonNull(paymentType.getPayChannelItem())) {
            PayChannelDTONew payChannelDTO = PayChannelDTONew.of();
            BeanUtils.copyProperties(paymentType.getPayChannelItem(), payChannelDTO);
            paymentTypeDTO.setPayChannelDTO(payChannelDTO);
        }

        PaymentTypeTransform paymentTypeTransform = paymentType.getPayTypeTransform();
        paymentTypeDTO.setBasicPayTypeId(paymentTypeTransform.getBasicPayTypeId());
        paymentTypeDTO.setSignPayTypeId(paymentTypeTransform.getSignPayTypeId());
        paymentTypeDTO.setPureSigningPayTypeId(paymentTypeTransform.getPureSigningPayTypeId());
        paymentTypeDTO.setIsSupportSign(paymentTypeTransform.getIsSupportSign());
        paymentTypeDTO.setIsSupportPureSign(paymentTypeTransform.getIsSupportPureSign());
        paymentTypeDTO.setIsSupportPasswordFreeSign(paymentTypeTransform.getIsSupportPasswordFreeSign());
        paymentTypeDTO.setPasswordFreePayType(paymentTypeTransform.getPasswordFreePayType());
        return paymentTypeDTO;
    }

}
