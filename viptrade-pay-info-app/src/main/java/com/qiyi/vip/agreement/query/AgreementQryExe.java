package com.qiyi.vip.agreement.query;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.qiyi.vip.constant.AgreementConstants;
import com.qiyi.vip.domain.agreement.AgreementMaterial;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.agreement.AgreementTemplate;
import com.qiyi.vip.domain.domainservice.AgreementAssembleService;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.VipType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementInfoReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.utils.JacksonUtils;

/**
 * @auther: guojing
 * @date: 2023/3/10 10:25 AM
 * @description:
 */
@Component
public class AgreementQryExe {

    @Resource
    private AgreementAssembleService agreementAssembleService;

    /**
     * 新增ios协议
     * @param dutType
     * @param commodity
     */
    public SingleResponse<Integer> addIos(Integer dutType, Commodity commodity, VipType vipTypeObj) {
        Integer agreementNo = agreementAssembleService.addIos(dutType, commodity, vipTypeObj);
        return SingleResponse.of(agreementNo);
    }

    public SingleResponse<Integer> getDefaultAgreementNoByDutType(Integer dutType, Integer amount, Long vipType) {
        Integer agreementNo = AgreementNoInfo.of().getDefaultAgreementNoByDutType(dutType, amount, vipType);
        return SingleResponse.of(agreementNo);
    }

    public SingleResponse<AgreementNoInfoDTO> getAgreementNoByCodeAndPayChannel(QueryAgreementNoByCodeReqDTO param, Integer payChannel) {
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of()
            .getAgreementNoByCodeAndPayChannel(param.getTemplateCode(), payChannel, param.getPartnerId(), param.getDutType());
        AgreementNoInfoDTO agreementNoInfoDTO = transfer(agreementNoInfo);
        AgreementTemplate agreementTemplate = AgreementTemplate.of().getByCode(param.getTemplateCode());
        if (agreementTemplate != null && agreementNoInfoDTO != null) {
            Map<String, Object> attributesMap = JacksonUtils.parseMap(agreementTemplate.getAttributes());
            Integer firstRenewDays = MapUtils.getInteger(attributesMap, AgreementTemplate.FIRST_RENEW_DAYS);
            agreementNoInfoDTO.setFirstRenewDays(firstRenewDays);
        }
        return SingleResponse.of(agreementNoInfoDTO);
    }

    public MultiResponse<AgreementNoInfoDTO> getAgreementListByDutType(Integer dutType) {
        List<AgreementNoInfo> agreementNoInfoList = AgreementNoInfo.of().getAgreementListByDutType(dutType);
        return MultiResponse.of(transfer(agreementNoInfoList));
    }

    public SingleResponse<AgreementNoInfoDTO> getAgreementNoById(Integer id) {
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of().getAgreementNoById(id);
        return SingleResponse.of(transfer(agreementNoInfo));
    }

    public SingleResponse<AgreementInfoResDTO> getAgreement(QueryAgreementInfoReqDTO param) {
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of().getAgreementNoById(param.getAgreementNo());
        AgreementMaterial agreementMaterial = AgreementMaterial.of().getAgreementGateway().getMaterial(param.getAgreementNo());

        AgreementInfoResDTO agreementInfo = AgreementInfoResDTO.builder()
            .agreementNo(param.getAgreementNo())
            .agreementType(Objects.nonNull(agreementNoInfo) ? agreementNoInfo.getType() : null)
            .description(Objects.nonNull(agreementMaterial) ? agreementMaterial.getDescription() : null)
            .detailUrl(Objects.nonNull(agreementMaterial) ? agreementMaterial.getDetailUrl() : null)
            .build();

        return SingleResponse.of(agreementInfo);
    }


    public MultiResponse<AgreementNoInfoDTO> getAgreementNoInfosByType(int type) {
        List<AgreementNoInfo> agreementNoInfos = AgreementNoInfo.of().getAgreementNoInfosByType(type);
        return MultiResponse.of(transfer(agreementNoInfos));
    }

    public MultiResponse<AgreementTemplateDTO> getAgreementTemplateBySkuIdentifier(Integer type, Integer discountType, Long vipType) {
        int defaultNo = discountType == AgreementConstants.DISCOUNT_TYPE_FIRST_X_PERIODS ? 0 : 1;
        List<String> templateCodes = AgreementNoInfo.of().getMaxPriorityTemplateCodeByVipType(type, vipType, defaultNo);
        List<AgreementTemplate> templates = AgreementTemplate.of().batchGetByCode(templateCodes);
        return MultiResponse.of(transferAgreementTemplate(templates));
    }

    public SingleResponse<AgreementTemplateDTO> getTemplateByCode(String templateCode) {
        AgreementTemplate agreementTemplate = AgreementTemplate.of().getByCode(templateCode);
        return SingleResponse.of(transfer(agreementTemplate));
    }

    public SingleResponse<AgreementTemplateDTO> getTemplateByAgreementNo(Integer agreementNo) {
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of().getAgreementNoById(agreementNo);
        if (agreementNoInfo == null) {
            return SingleResponse.of(null);
        }
        AgreementTemplate agreementTemplate = AgreementTemplate.of().getByCode(agreementNoInfo.getTemplateCode());
        return SingleResponse.of(transfer(agreementTemplate));
    }

    private AgreementNoInfoDTO transfer(AgreementNoInfo agreementNoInfo) {
        if (null == agreementNoInfo) {
            return null;
        }
        AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
        BeanUtils.copyProperties(agreementNoInfo, agreementNoInfoDTO);
        return agreementNoInfoDTO;
    }

    private AgreementTemplateDTO transfer(AgreementTemplate agreementTemplate) {
        if (null == agreementTemplate) {
            return null;
        }
        AgreementTemplateDTO agreementTemplateDTO = new AgreementTemplateDTO();
        BeanUtils.copyProperties(agreementTemplate, agreementTemplateDTO);
        return agreementTemplateDTO;
    }

    private List<AgreementNoInfoDTO> transfer(List<AgreementNoInfo> agreementNoInfos) {
        if (null == agreementNoInfos) {
            return null;
        }

        List<AgreementNoInfoDTO> agreementNoInfoDTOS = new ArrayList<>();
        for (AgreementNoInfo agreementNoInfo : agreementNoInfos) {
            AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
            BeanUtils.copyProperties(agreementNoInfo, agreementNoInfoDTO);
            agreementNoInfoDTOS.add(agreementNoInfoDTO);
        }

        return agreementNoInfoDTOS;
    }

    private List<AgreementTemplateDTO> transferAgreementTemplate(List<AgreementTemplate> agreementTemplates) {
        if (null == agreementTemplates) {
            return null;
        }

        List<AgreementTemplateDTO> agreementTemplateDTOS = new ArrayList<>();
        for (AgreementTemplate agreementTemplate : agreementTemplates) {
            AgreementTemplateDTO agreementTemplateDTO = new AgreementTemplateDTO();
            BeanUtils.copyProperties(agreementTemplate, agreementTemplateDTO);
            agreementTemplateDTOS.add(agreementTemplateDTO);
        }

        return agreementTemplateDTOS;
    }

}
