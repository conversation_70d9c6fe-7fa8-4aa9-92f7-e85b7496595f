package com.qiyi.vip.agreement;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.qiyi.vip.agreement.query.AgreementQryExe;
import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.catchlog.CatchAndLog;
import com.qiyi.vip.commons.component.AccountApi;
import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.domain.remote.BasicDataGateway;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.CommodityCenterGateway;
import com.qiyi.vip.domain.remote.VipType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementInfoReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.req.AgreementNoInfoReq;
import com.qiyi.vip.dto.req.IosAgreementCreateReq;
import com.qiyi.vip.duttype.query.PaymentDutTypeQryExe;
import com.qiyi.vip.exception.Assert;
import com.qiyi.vip.exception.BizException;
import com.qiyi.vip.paytype.query.PaymentTypeQryExe;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;

/**
 * @auther: guojing
 * @date: 2023/3/9 5:46 PM
 * @description:
 */
@Service
@CatchAndLog
public class AgreementServiceImpl implements AgreementServiceI {

    @ConfigJsonValue("${skuIdentifier.to.agreementType.map:{\"1\":[1],\"4\":[1,0],\"2\":[2],\"3\":[1,1],\"5\":[1]}}")
    private Map<Integer, Integer[]> skuIdentifierToAgreementTypeMap;

    @Resource
    private AgreementQryExe agreementQryExe;

    @Resource
    private PaymentDutTypeQryExe paymentDutTypeQryExe;

    @Resource
    private AccountApi accountApi;
    @Resource
    private CommodityCenterGateway commodityCenterGateway;
    @Resource
    private BasicDataGateway basicDataGateway;
    @Resource
    private PaymentTypeQryExe paymentTypeQryExe;

    @Override
    public SingleResponse<Integer> getDefaultAgreementNoByDutType(QueryAgreementNoReqDTO param) {
        Assert.notNull(param.getDutType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return agreementQryExe.getDefaultAgreementNoByDutType(param.getDutType(), param.getAmount(), param.getVipType());
    }

    @Override
    public SingleResponse<AgreementNoInfoDTO> getAgreementNoByCodeAndPayType(QueryAgreementNoByCodeReqDTO param) {
        Assert.notNull(param.getTemplateCode(), ErrorCodeEnum.PARAMETER_ERR.getCode(), "templateCode不能为空");
        Assert.notNull(param.getPayType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), "payType不能为空");
        SingleResponse<PaymentTypeDTO> paymentTypeResp = paymentTypeQryExe.getPayType(param.getPayType());
        if (paymentTypeResp.returnFailed()) {
            throw new BizException(ErrorCodeEnum.PARAMETER_ERR.getCode(), "支付方式不存在");
        }
        return agreementQryExe.getAgreementNoByCodeAndPayChannel(param, paymentTypeResp.getData().getPayChannel());
    }

    @Override
    public MultiResponse<AgreementNoInfoDTO> getAgreementListByDutType(QueryAgreementNoReqDTO param) {
        Assert.notNull(param.getDutType(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return agreementQryExe.getAgreementListByDutType(param.getDutType());
    }

    @Override
    public MultiResponse<AgreementNoInfoDTO> getAgreementInfosByCondition(AgreementNoInfoReq req) {
        Integer type = req.getType();
        String priceActCode = req.getPriceActCode();

        if (isTypeInvalid(type) && isPriceActCodeInvalid(priceActCode)) {
            // 两个查询条件不能同时无效
            throw new BizException(ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        }

        // 根据协议type查询
        if (!isTypeInvalid(type)) {
            return agreementQryExe.getAgreementNoInfosByType(type);
        }

        // 根据价格活动code查询
        // 先去payment_dut_type表查找agreement_no，再根据agreement_no去agreement_no_info表查
        List<PaymentDutType> paymentDutTypes = paymentDutTypeQryExe.getDutTypeByAgreementActCode(priceActCode);
        if (CollectionUtils.isEmpty(paymentDutTypes)) {
            return MultiResponse.of(null);
        }

        SingleResponse<AgreementNoInfoDTO> agreementNoById = agreementQryExe.getAgreementNoById(paymentDutTypes.get(0).getAgreementNo());
        return MultiResponse.of(Collections.singleton(agreementNoById.getData()));
    }

    public boolean isTypeInvalid(Integer value) {
        return value == null || value == 0;
    }

    private boolean isPriceActCodeInvalid(String value) {
        return StringUtils.isEmpty(value);
    }

    @Override
    public SingleResponse<TransformResDTO> getDutTypeAndIsFirstSignById(Long userId, Integer agreementNo) {
        Assert.notNull(userId, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(agreementNo, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());

        SingleResponse<AgreementNoInfoDTO> agreementNoInfo = agreementQryExe.getAgreementNoById(agreementNo);
        if (agreementNoInfo == null || agreementNoInfo.getData() == null) {
            return SingleResponse.of(null);
        }
        Integer dutType = agreementNoInfo.getData().getDutType();
        TransformResDTO transformResDTO = new TransformResDTO();
        transformResDTO.setDutType(dutType);

        boolean isBind = accountApi.isBind(userId, dutType);
        if (isBind) {
            throw new BizException(BizException.HAS_SIGN, "用户已签约~");
        }

        transformResDTO.setIsFirstSign(true);
        return SingleResponse.of(transformResDTO);
    }

    @Override
    public SingleResponse<AgreementInfoResDTO> getAgreementInfo(QueryAgreementInfoReqDTO param) {
        Assert.notNull(param.getAgreementNo(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        SingleResponse<AgreementInfoResDTO> agreementInfo = agreementQryExe.getAgreement(param);
        if (agreementInfo == null || agreementInfo.getData() == null) {
            return SingleResponse.of(null);
        }
        return agreementInfo;
    }

    @Override
    public SingleResponse<Integer> addIos(IosAgreementCreateReq createParam) {
        Commodity commodity = commodityCenterGateway.query(createParam.getSkuId());
        if (commodity == null) {
            throw BizException.newException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "未查询到商品信息");
        }
        VipType vipTypeObj = basicDataGateway.getVipTypeById(commodity.getVipType());
        if (vipTypeObj == null) {
            throw BizException.newException(ErrorCodeEnum.DATA_NOT_FOUND.getCode(), "未查询到会员类型信息");
        }
        return agreementQryExe.addIos(createParam.getDutType(), commodity, vipTypeObj);
    }

    /*
        skuIdentifier(商品标识):
            1: 常规连包
            2: 芝麻购
            3: 首X期优惠
            4: 首单非标卡
            5：纯签约
        type(协议类型):
            1: 自动续费
            2: 芝麻GO
        discountType(促销类型):
            0: 正价协议
            1: 变价协议
    */
    @Override
    public MultiResponse<AgreementTemplateDTO> queryBySkuIdentifier(Integer skuIdentifier, Long vipType) {
        Assert.notNull(skuIdentifier, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        if (!skuIdentifierToAgreementTypeMap.containsKey(skuIdentifier)) {
            throw new BizException(ErrorCodeEnum.PARAMETER_ERR.getCode(), "skuIdentifier参数有误");
        }
        Integer[] agreementTypeArray = skuIdentifierToAgreementTypeMap.get(skuIdentifier);
        if (agreementTypeArray == null || agreementTypeArray.length == 0) {
            throw new BizException(ErrorCodeEnum.PARAMETER_ERR.getCode(), "skuIdentifier对应的协议类型配置错误");
        }
        Integer type = agreementTypeArray[0];
        int discountType = agreementTypeArray.length == 1 ? 0 : agreementTypeArray[1];

        // 判断是否为纯签约
        boolean isPureSign = skuIdentifier == 5;
        return agreementQryExe.getAgreementTemplateBySkuIdentifier(type, discountType, vipType, isPureSign);
    }

    @Override
    public SingleResponse<AgreementTemplateDTO> getTemplateByAgreementNo(Integer agreementNo) {
        Assert.notNull(agreementNo, ErrorCodeEnum.PARAMETER_ERR.getCode(), "agreementNo不能为空");
        return agreementQryExe.getTemplateByAgreementNo(agreementNo);
    }

    @Override
    public SingleResponse<AgreementTemplateDTO> getTemplateByCode(String templateCode) {
        Assert.notNull(templateCode, ErrorCodeEnum.PARAMETER_ERR.getCode(), "templateCode不能为空");
        return agreementQryExe.getTemplateByCode(templateCode);
    }
}
