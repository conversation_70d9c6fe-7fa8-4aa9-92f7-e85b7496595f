package com.qiyi.vip.paychannel;

import com.qiyi.vip.api.PayChannelServiceI;
import com.qiyi.vip.catchlog.CatchAndLog;
import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.*;
import com.qiyi.vip.exception.Assert;
import com.qiyi.vip.paychannel.query.PayInfoQryExe;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 9:23 PM
 */
@Service
@CatchAndLog
public class PayChannelServiceImpl implements PayChannelServiceI {
    @Resource
    PayInfoQryExe payInfoQryExe;

    @Override
    public SingleResponse<PayInfoResDTO> payInfo(PayInfoReqDTO payInfoReqDTO) {
        Assert.notNull(payInfoReqDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notEmpty(payInfoReqDTO.getProducts(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        return payInfoQryExe.payInfo(payInfoReqDTO);
    }

    @Override
    public SingleResponse<PayInfoResDTO> smartPayInfo(SmartPayInfoReqDTO smartPayInfoReqDTO) {
        if (smartPayInfoReqDTO == null) {
            return SingleResponse.buildFailure(ErrorCodeEnum.PARAMETER_ERR.getCode(), "smartPayInfoReqDTO不能为空");
        }
        if (CollectionUtils.isEmpty(smartPayInfoReqDTO.getProducts())) {
            return SingleResponse.buildFailure(ErrorCodeEnum.PARAMETER_ERR.getCode(), "products不能为空");
        }

        return payInfoQryExe.smartPayInfo(smartPayInfoReqDTO);
    }

    @Override
    public SingleResponse<RoutePaymentInfoResult> routePaymentInfo(RoutePaymentInfoDTO routePaymentInfo) {
        Assert.notNull(routePaymentInfo, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(routePaymentInfo.getPayPlatform(), ErrorCodeEnum.PARAMETER_ERR.getCode(), "支付平台不能为空~");
        Assert.notNull(routePaymentInfo.getClientVersion(), ErrorCodeEnum.PARAMETER_ERR.getCode(), "客户端版本不能为空~");
        return payInfoQryExe.routePaymentInfo(routePaymentInfo);
    }

    @Override
    public MultiResponse<PayChannelDTO> list() {
        return payInfoQryExe.list();
    }

    @Override
    public PageResponse<PayChannelDTONew> list(ListByConditionsDTO listByConditionsDTO) {
        Assert.notNull(listByConditionsDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        if (Objects.isNull(listByConditionsDTO.getPageNo())) {
            listByConditionsDTO.setPageNo(1);
        }
        if (Objects.isNull(listByConditionsDTO.getPageSize())) {
            listByConditionsDTO.setPageSize(10);
        }
        if (Strings.isNotBlank(listByConditionsDTO.getName())) {
            listByConditionsDTO.setName(URLDecoder.decode(listByConditionsDTO.getName()));
        }
        return payInfoQryExe.list(listByConditionsDTO);
    }

    @Override
    public MultiResponse<PayChannelDTONew> listTopPayChannels() {
        return payInfoQryExe.listTopPayChannels();
    }

    @Override
    public Response addPayChannel(AddPayChannelDTO addPayChannelDTO) {
        Assert.notNull(addPayChannelDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notEmpty(addPayChannelDTO.getCode(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notEmpty(addPayChannelDTO.getDescription(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Boolean flag = payInfoQryExe.addPayChannel(addPayChannelDTO);
        return Boolean.TRUE.equals(flag) ? Response.buildSuccess() :
                Response.buildFailure(ErrorCodeEnum.INSERT_ERR.getCode(), ErrorCodeEnum.INSERT_ERR.getName());
    }

    @Override
    public Response updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO) {
        Assert.notNull(updatePayChannelDTO, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notNull(updatePayChannelDTO.getId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notEmpty(updatePayChannelDTO.getDescription(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        Assert.notEmpty(updatePayChannelDTO.getCode(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        if (updatePayChannelDTO.getIsTop() == 0) {
            Assert.notEmpty(updatePayChannelDTO.getCategoryCode(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
        }
        Boolean flag = payInfoQryExe.updatePayChannel(updatePayChannelDTO);
        return Boolean.TRUE.equals(flag) ? Response.buildSuccess() :
                Response.buildFailure(ErrorCodeEnum.UPDATE_ERR.getCode(), ErrorCodeEnum.UPDATE_ERR.getName());
    }

    @Override
    public MultiResponse<PayChannelDTONew> getPayChannelsByBusiness(String business) {
        return payInfoQryExe.getPayChannelsByBusiness(business);
    }

}
