package com.qiyi.vip.paychannel.query;

import com.qiyi.vip.domain.domainservice.PayInfoService;
import com.qiyi.vip.domain.paychannel.BusinessChannel;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paychannel.assembler.PayChannelAssembler;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 9:25 PM
 */
@Component
public class PayInfoQryExe {
    @Autowired
    private PayInfoService payInfoService;


    /**
     * 根据业务编码获取支付渠道列表
     */
    public MultiResponse<PayChannelDTONew> getPayChannelsByBusiness(String business) {
        List<BusinessChannel> businessChannels = PayChannel.of().getPayChannelsByBusiness(business);
        if (CollectionUtils.isEmpty(businessChannels)) {
            return MultiResponse.buildSuccess();
        }

        List<PayChannel> allPayChannels = PayChannel.of().getAll();
        if (CollectionUtils.isEmpty(allPayChannels)) {
            return MultiResponse.buildSuccess();
        }

        // 获取业务渠道对应的支付渠道ID列表
        List<Integer> businessPayChannelIds = businessChannels.stream()
            .map(BusinessChannel::getPayChannel)
            .collect(Collectors.toList());

        // 筛选出业务配置的支付渠道
        List<PayChannelDTONew> payChannelDTOS = allPayChannels.stream()
            .filter(payChannel -> businessPayChannelIds.contains(payChannel.getId().intValue()))
            .map(PayChannelAssembler.INSTANCE::toPayChannel)
            .collect(Collectors.toList());

        return MultiResponse.ofAdmin(payChannelDTOS);
    }

    public SingleResponse<PayInfoResDTO> payInfo(PayInfoReqDTO payInfoReqDTO) {
        PayInfoResDTO payInfoResDTO = payInfoService.payInfo(payInfoReqDTO);
        return SingleResponse.of(payInfoResDTO);
    }

    public SingleResponse<PayInfoResDTO> smartPayInfo(SmartPayInfoReqDTO smartPayInfoReqDTO) {
        PayInfoResDTO payInfoResDTO = payInfoService.smartPayInfo(smartPayInfoReqDTO);
        return SingleResponse.of(payInfoResDTO);
    }

    public MultiResponse<PayChannelDTO> list() {
        List<PayChannel> payChannelList = PayChannel.of().getAll();
        if (CollectionUtils.isNotEmpty(payChannelList)) {
            List<PayChannelDTO> payChannelDTOS = payChannelList.stream().map(payChannel -> {
                PayChannelDTO payChannelDTO = new PayChannelDTO();
                BeanUtils.copyProperties(payChannel, payChannelDTO);
                return payChannelDTO;
            }).collect(Collectors.toList());
            return MultiResponse.of(payChannelDTOS);
        } else {
            return MultiResponse.buildSuccess();
        }
    }

    public PageResponse<PayChannelDTONew> list(ListByConditionsDTO listByConditionsDTO) {
        List<PayChannel> payChannels = PayChannel.of().getPayChannelByIdOrNameOrCode(listByConditionsDTO.getId(), listByConditionsDTO.getName(),
                listByConditionsDTO.getCode(), listByConditionsDTO.getPageNo(), listByConditionsDTO.getPageSize());

        if (CollectionUtils.isEmpty(payChannels)) {
            return PageResponse.of(listByConditionsDTO.getPageSize(), listByConditionsDTO.getPageNo());
        } else {
            List<PayChannelDTONew> payChannelDTOS = payChannels.stream().map(payChannel -> {
                PayChannelDTONew payChannelDTO = new PayChannelDTONew();
                BeanUtils.copyProperties(payChannel, payChannelDTO);
                return payChannelDTO;
            }).collect(Collectors.toList());
            Integer totalCount = PayChannel.of().getCountByIdOrNameOrCode(listByConditionsDTO.getId(),
                    listByConditionsDTO.getName(), listByConditionsDTO.getCode());
            return PageResponse.of(payChannelDTOS, totalCount, listByConditionsDTO.getPageSize(), listByConditionsDTO.getPageNo());
        }
    }

    public MultiResponse<PayChannelDTONew> listTopPayChannels() {
        List<PayChannel> payChannelList = PayChannel.of().getTopPayChannels();
        if (CollectionUtils.isNotEmpty(payChannelList)) {
            List<PayChannelDTONew> payChannelDTOS = payChannelList.stream().map(payChannel -> {
                PayChannelDTONew payChannelDTO = new PayChannelDTONew();
                BeanUtils.copyProperties(payChannel, payChannelDTO);
                return payChannelDTO;
            }).collect(Collectors.toList());
            return MultiResponse.of(payChannelDTOS);
        } else {
            return MultiResponse.buildSuccess();
        }
    }

    public Boolean addPayChannel(AddPayChannelDTO addPayChannelDTO) {
        return PayChannel.of().addPayChannel(addPayChannelDTO);
    }

    public Boolean updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO) {
        return PayChannel.of().updatePayChannel(updatePayChannelDTO);
    }

    public SingleResponse<RoutePaymentInfoResult> routePaymentInfo(RoutePaymentInfoDTO routePaymentInfo) {
        return SingleResponse.of(payInfoService.routePaymentInfo(routePaymentInfo));
    }
}
