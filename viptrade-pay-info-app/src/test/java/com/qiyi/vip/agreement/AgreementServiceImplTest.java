package com.qiyi.vip.agreement;

import com.qiyi.vip.agreement.query.AgreementQryExe;
import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.commons.component.AccountApi;
import com.qiyi.vip.domain.remote.BasicDataGateway;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.CommodityCenterGateway;
import com.qiyi.vip.domain.remote.VipType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.req.AgreementNoInfoReq;
import com.qiyi.vip.dto.req.IosAgreementCreateReq;
import com.qiyi.vip.duttype.query.PaymentDutTypeQryExe;
import com.qiyi.vip.exception.BizException;
import com.qiyi.vip.paytype.query.PaymentTypeQryExe;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AgreementServiceImpl单元测试
 * 重点测试queryBySkuIdentifier方法的isPureSign逻辑
 */
@RunWith(MockitoJUnitRunner.class)
public class AgreementServiceImplTest {

    @InjectMocks
    private AgreementServiceImpl agreementService;

    @Mock
    private AgreementQryExe agreementQryExe;

    @Mock
    private PaymentDutTypeQryExe paymentDutTypeQryExe;

    @Mock
    private AccountApi accountApi;

    @Mock
    private CommodityCenterGateway commodityCenterGateway;

    @Mock
    private BasicDataGateway basicDataGateway;

    @Mock
    private PaymentTypeQryExe paymentTypeQryExe;

    private Map<Integer, Integer[]> skuIdentifierToAgreementTypeMap;

    @Before
    public void setUp() {
        // 初始化配置映射
        skuIdentifierToAgreementTypeMap = new HashMap<>();
        skuIdentifierToAgreementTypeMap.put(1, new Integer[]{1});           // 常规连包
        skuIdentifierToAgreementTypeMap.put(2, new Integer[]{2});           // 芝麻购
        skuIdentifierToAgreementTypeMap.put(3, new Integer[]{1, 1});        // 首X期优惠
        skuIdentifierToAgreementTypeMap.put(4, new Integer[]{1, 0});        // 首单非标卡
        skuIdentifierToAgreementTypeMap.put(5, new Integer[]{1});           // 纯签约
        
        ReflectionTestUtils.setField(agreementService, "skuIdentifierToAgreementTypeMap", skuIdentifierToAgreementTypeMap);
    }

    /**
     * 测试queryBySkuIdentifier - 常规连包场景
     * skuIdentifier=1, 应该调用isPureSign=false
     */
    @Test
    public void testQueryBySkuIdentifier_RegularPackage() {
        // Given
        Integer skuIdentifier = 1;
        Long vipType = 1L;
        
        AgreementTemplateDTO templateDTO = new AgreementTemplateDTO();
        templateDTO.setId(1);
        templateDTO.setCode("TEST_001");
        
        MultiResponse<AgreementTemplateDTO> expectedResponse = MultiResponse.of(Arrays.asList(templateDTO));
        
        when(agreementQryExe.getAgreementTemplateBySkuIdentifier(eq(1), eq(0), eq(vipType), eq(false)))
            .thenReturn(expectedResponse);

        // When
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals("TEST_001", result.getData().get(0).getCode());
        
        // 验证调用参数
        verify(agreementQryExe).getAgreementTemplateBySkuIdentifier(1, 0, vipType, false);
    }

    /**
     * 测试queryBySkuIdentifier - 纯签约场景
     * skuIdentifier=5, 应该调用isPureSign=true
     */
    @Test
    public void testQueryBySkuIdentifier_PureSign() {
        // Given
        Integer skuIdentifier = 5;
        Long vipType = 2L;
        
        AgreementTemplateDTO templateDTO = new AgreementTemplateDTO();
        templateDTO.setId(2);
        templateDTO.setCode("PURE_SIGN_001");
        
        MultiResponse<AgreementTemplateDTO> expectedResponse = MultiResponse.of(Arrays.asList(templateDTO));
        
        when(agreementQryExe.getAgreementTemplateBySkuIdentifier(eq(1), eq(0), eq(vipType), eq(true)))
            .thenReturn(expectedResponse);

        // When
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals("PURE_SIGN_001", result.getData().get(0).getCode());
        
        // 验证调用参数 - 关键验证isPureSign=true
        verify(agreementQryExe).getAgreementTemplateBySkuIdentifier(1, 0, vipType, true);
    }

    /**
     * 测试queryBySkuIdentifier - 芝麻购场景
     * skuIdentifier=2, 应该调用isPureSign=false
     */
    @Test
    public void testQueryBySkuIdentifier_ZhimaGo() {
        // Given
        Integer skuIdentifier = 2;
        Long vipType = 1L;
        
        AgreementTemplateDTO templateDTO = new AgreementTemplateDTO();
        templateDTO.setId(3);
        templateDTO.setCode("ZHIMA_GO_001");
        
        MultiResponse<AgreementTemplateDTO> expectedResponse = MultiResponse.of(Arrays.asList(templateDTO));
        
        when(agreementQryExe.getAgreementTemplateBySkuIdentifier(eq(2), eq(0), eq(vipType), eq(false)))
            .thenReturn(expectedResponse);

        // When
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals("ZHIMA_GO_001", result.getData().get(0).getCode());
        
        // 验证调用参数
        verify(agreementQryExe).getAgreementTemplateBySkuIdentifier(2, 0, vipType, false);
    }

    /**
     * 测试queryBySkuIdentifier - 首X期优惠场景
     * skuIdentifier=3, agreementTypeArray=[1,1], discountType=1
     */
    @Test
    public void testQueryBySkuIdentifier_FirstXPeriodsDiscount() {
        // Given
        Integer skuIdentifier = 3;
        Long vipType = 1L;
        
        AgreementTemplateDTO templateDTO = new AgreementTemplateDTO();
        templateDTO.setId(4);
        templateDTO.setCode("FIRST_X_001");
        
        MultiResponse<AgreementTemplateDTO> expectedResponse = MultiResponse.of(Arrays.asList(templateDTO));
        
        when(agreementQryExe.getAgreementTemplateBySkuIdentifier(eq(1), eq(1), eq(vipType), eq(false)))
            .thenReturn(expectedResponse);

        // When
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals("FIRST_X_001", result.getData().get(0).getCode());
        
        // 验证调用参数 - discountType=1
        verify(agreementQryExe).getAgreementTemplateBySkuIdentifier(1, 1, vipType, false);
    }

    /**
     * 测试queryBySkuIdentifier - 首单非标卡场景
     * skuIdentifier=4, agreementTypeArray=[1,0], discountType=0
     */
    @Test
    public void testQueryBySkuIdentifier_FirstOrderNonStandardCard() {
        // Given
        Integer skuIdentifier = 4;
        Long vipType = 1L;
        
        AgreementTemplateDTO templateDTO = new AgreementTemplateDTO();
        templateDTO.setId(5);
        templateDTO.setCode("FIRST_ORDER_001");
        
        MultiResponse<AgreementTemplateDTO> expectedResponse = MultiResponse.of(Arrays.asList(templateDTO));
        
        when(agreementQryExe.getAgreementTemplateBySkuIdentifier(eq(1), eq(0), eq(vipType), eq(false)))
            .thenReturn(expectedResponse);

        // When
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals("FIRST_ORDER_001", result.getData().get(0).getCode());
        
        // 验证调用参数 - discountType=0
        verify(agreementQryExe).getAgreementTemplateBySkuIdentifier(1, 0, vipType, false);
    }

    /**
     * 测试queryBySkuIdentifier - 参数为空异常
     */
    @Test(expected = BizException.class)
    public void testQueryBySkuIdentifier_NullSkuIdentifier() {
        // When & Then
        agreementService.queryBySkuIdentifier(null, 1L);
    }

    /**
     * 测试queryBySkuIdentifier - 不支持的skuIdentifier
     */
    @Test(expected = BizException.class)
    public void testQueryBySkuIdentifier_UnsupportedSkuIdentifier() {
        // When & Then
        agreementService.queryBySkuIdentifier(999, 1L);
    }

    /**
     * 测试queryBySkuIdentifier - 协议类型配置错误
     */
    @Test(expected = BizException.class)
    public void testQueryBySkuIdentifier_EmptyAgreementTypeArray() {
        // Given
        skuIdentifierToAgreementTypeMap.put(6, new Integer[]{});
        ReflectionTestUtils.setField(agreementService, "skuIdentifierToAgreementTypeMap", skuIdentifierToAgreementTypeMap);
        
        // When & Then
        agreementService.queryBySkuIdentifier(6, 1L);
    }

    /**
     * 测试getAgreementNoByCodeAndPayType - 正常场景
     */
    @Test
    public void testGetAgreementNoByCodeAndPayType_Success() {
        // Given
        QueryAgreementNoByCodeReqDTO param = new QueryAgreementNoByCodeReqDTO();
        param.setTemplateCode("TEST_CODE");
        param.setPayType(1L);
        
        PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setPayChannel(1);
        
        AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
        agreementNoInfoDTO.setId(1);
        
        when(paymentTypeQryExe.getPayType(1L)).thenReturn(SingleResponse.of(paymentTypeDTO));
        when(agreementQryExe.getAgreementNoByCodeAndPayChannel(param, 1)).thenReturn(SingleResponse.of(agreementNoInfoDTO));

        // When
        SingleResponse<AgreementNoInfoDTO> result = agreementService.getAgreementNoByCodeAndPayType(param);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(1), result.getData().getId());
        
        verify(paymentTypeQryExe).getPayType(1L);
        verify(agreementQryExe).getAgreementNoByCodeAndPayChannel(param, 1);
    }

    /**
     * 测试getDutTypeAndIsFirstSignById - 用户已签约场景
     */
    @Test(expected = BizException.class)
    public void testGetDutTypeAndIsFirstSignById_UserAlreadySigned() {
        // Given
        Long userId = 12345L;
        Integer agreementNo = 1;
        
        AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
        agreementNoInfoDTO.setDutType(1);
        
        when(agreementQryExe.getAgreementNoById(agreementNo)).thenReturn(SingleResponse.of(agreementNoInfoDTO));
        when(accountApi.isBind(userId, 1)).thenReturn(true);

        // When & Then
        agreementService.getDutTypeAndIsFirstSignById(userId, agreementNo);
    }

    /**
     * 测试getDutTypeAndIsFirstSignById - 用户未签约场景
     */
    @Test
    public void testGetDutTypeAndIsFirstSignById_UserNotSigned() {
        // Given
        Long userId = 12345L;
        Integer agreementNo = 1;
        
        AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
        agreementNoInfoDTO.setDutType(1);
        
        when(agreementQryExe.getAgreementNoById(agreementNo)).thenReturn(SingleResponse.of(agreementNoInfoDTO));
        when(accountApi.isBind(userId, 1)).thenReturn(false);

        // When
        SingleResponse<TransformResDTO> result = agreementService.getDutTypeAndIsFirstSignById(userId, agreementNo);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(Integer.valueOf(1), result.getData().getDutType());
        assertTrue(result.getData().getIsFirstSign());
        
        verify(agreementQryExe).getAgreementNoById(agreementNo);
        verify(accountApi).isBind(userId, 1);
    }



    /**
     * 测试getAgreementInfosByCondition - 根据type查询
     */
    @Test
    public void testGetAgreementInfosByCondition_ByType() {
        // Given
        AgreementNoInfoReq req = new AgreementNoInfoReq();
        req.setType(1);
        
        AgreementNoInfoDTO agreementNoInfoDTO = new AgreementNoInfoDTO();
        agreementNoInfoDTO.setId(1);
        
        when(agreementQryExe.getAgreementNoInfosByType(1)).thenReturn(MultiResponse.of(Arrays.asList(agreementNoInfoDTO)));

        // When
        MultiResponse<AgreementNoInfoDTO> result = agreementService.getAgreementInfosByCondition(req);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals(Integer.valueOf(1), result.getData().get(0).getId());
        
        verify(agreementQryExe).getAgreementNoInfosByType(1);
    }
}