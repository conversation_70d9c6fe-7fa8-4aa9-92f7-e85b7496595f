package com.qiyi.vip.agreement.query;

import com.qiyi.vip.constant.AgreementConstants;
import com.qiyi.vip.domain.agreement.AgreementMaterial;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.agreement.AgreementTemplate;
import com.qiyi.vip.domain.domainservice.AgreementAssembleService;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * AgreementQryExe单元测试
 * 重点测试getAgreementTemplateBySkuIdentifier方法的isPureSign参数
 */
@RunWith(MockitoJUnitRunner.class)
public class AgreementQryExeTest {

    @InjectMocks
    private AgreementQryExe agreementQryExe;

    @Mock
    private AgreementAssembleService agreementAssembleService;

    /**
     * 测试getAgreementTemplateBySkuIdentifier - 非纯签约场景
     * isPureSign=false, 应该调用数据库查询
     */
    @Test
    public void testGetAgreementTemplateBySkuIdentifier_NotPureSign() {
        // Given
        Integer type = 1;
        Integer discountType = 0;
        Long vipType = 1L;
        Boolean isPureSign = false;
        
        List<String> templateCodes = Arrays.asList("TEMPLATE_001", "TEMPLATE_002");
        AgreementTemplate template1 = createMockAgreementTemplate("TEMPLATE_001", "测试模板1");
        AgreementTemplate template2 = createMockAgreementTemplate("TEMPLATE_002", "测试模板2");
        List<AgreementTemplate> templates = Arrays.asList(template1, template2);

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class);
             MockedStatic<AgreementTemplate> mockedAgreementTemplate = mockStatic(AgreementTemplate.class)) {
            
            AgreementNoInfo mockAgreementNoInfo = mock(AgreementNoInfo.class);
            AgreementTemplate mockAgreementTemplateStatic = mock(AgreementTemplate.class);
            
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfo);
            mockedAgreementTemplate.when(AgreementTemplate::of).thenReturn(mockAgreementTemplateStatic);
            
            when(mockAgreementNoInfo.getMaxPriorityTemplateCodeByVipType(type, vipType, 1, isPureSign))
                .thenReturn(templateCodes);
            when(mockAgreementTemplateStatic.batchGetByCode(templateCodes)).thenReturn(templates);

            // When
            MultiResponse<AgreementTemplateDTO> result = agreementQryExe.getAgreementTemplateBySkuIdentifier(
                type, discountType, vipType, isPureSign);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(2, result.getData().size());
            assertEquals("TEMPLATE_001", result.getData().get(0).getCode());
            assertEquals("TEMPLATE_002", result.getData().get(1).getCode());
            
            // 验证数据库查询被调用，且isPureSign参数正确传递
            verify(mockAgreementNoInfo).getMaxPriorityTemplateCodeByVipType(type, vipType, 1, false);
            verify(mockAgreementTemplateStatic).batchGetByCode(templateCodes);
        }
    }

    /**
     * 测试getAgreementTemplateBySkuIdentifier - 纯签约场景
     * isPureSign=true, 应该调用数据库查询
     */
    @Test
    public void testGetAgreementTemplateBySkuIdentifier_PureSign() {
        // Given
        Integer type = 1;
        Integer discountType = 0;
        Long vipType = 2L;
        Boolean isPureSign = true;
        
        List<String> templateCodes = Arrays.asList("PURE_SIGN_001");
        AgreementTemplate template = createMockAgreementTemplate("PURE_SIGN_001", "纯签约模板");
        List<AgreementTemplate> templates = Arrays.asList(template);

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class);
             MockedStatic<AgreementTemplate> mockedAgreementTemplate = mockStatic(AgreementTemplate.class)) {
            
            AgreementNoInfo mockAgreementNoInfo = mock(AgreementNoInfo.class);
            AgreementTemplate mockAgreementTemplateStatic = mock(AgreementTemplate.class);
            
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfo);
            mockedAgreementTemplate.when(AgreementTemplate::of).thenReturn(mockAgreementTemplateStatic);
            
            when(mockAgreementNoInfo.getMaxPriorityTemplateCodeByVipType(type, vipType, 1, isPureSign))
                .thenReturn(templateCodes);
            when(mockAgreementTemplateStatic.batchGetByCode(templateCodes)).thenReturn(templates);

            // When
            MultiResponse<AgreementTemplateDTO> result = agreementQryExe.getAgreementTemplateBySkuIdentifier(
                type, discountType, vipType, isPureSign);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(1, result.getData().size());
            assertEquals("PURE_SIGN_001", result.getData().get(0).getCode());
            
            // 验证数据库查询被调用，且isPureSign=true参数正确传递
            verify(mockAgreementNoInfo).getMaxPriorityTemplateCodeByVipType(type, vipType, 1, true);
            verify(mockAgreementTemplateStatic).batchGetByCode(templateCodes);
        }
    }

    /**
     * 测试getAgreementTemplateBySkuIdentifier - 首X期优惠场景
     * discountType=1 (首X期优惠), defaultNo应该为0
     */
    @Test
    public void testGetAgreementTemplateBySkuIdentifier_FirstXPeriodsDiscount() {
        // Given
        Integer type = 1;
        Integer discountType = AgreementConstants.DISCOUNT_TYPE_FIRST_X_PERIODS; // 1
        Long vipType = 1L;
        Boolean isPureSign = false;
        
        List<String> templateCodes = Arrays.asList("FIRST_X_001");
        AgreementTemplate template = createMockAgreementTemplate("FIRST_X_001", "首X期优惠模板");
        List<AgreementTemplate> templates = Arrays.asList(template);

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class);
             MockedStatic<AgreementTemplate> mockedAgreementTemplate = mockStatic(AgreementTemplate.class)) {
            
            AgreementNoInfo mockAgreementNoInfo = mock(AgreementNoInfo.class);
            AgreementTemplate mockAgreementTemplateStatic = mock(AgreementTemplate.class);
            
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfo);
            mockedAgreementTemplate.when(AgreementTemplate::of).thenReturn(mockAgreementTemplateStatic);
            
            // 首X期优惠时，defaultNo应该为0
            when(mockAgreementNoInfo.getMaxPriorityTemplateCodeByVipType(type, vipType, 0, isPureSign))
                .thenReturn(templateCodes);
            when(mockAgreementTemplateStatic.batchGetByCode(templateCodes)).thenReturn(templates);

            // When
            MultiResponse<AgreementTemplateDTO> result = agreementQryExe.getAgreementTemplateBySkuIdentifier(
                type, discountType, vipType, isPureSign);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(1, result.getData().size());
            assertEquals("FIRST_X_001", result.getData().get(0).getCode());
            
            // 验证defaultNo=0被正确传递
            verify(mockAgreementNoInfo).getMaxPriorityTemplateCodeByVipType(type, vipType, 0, isPureSign);
            verify(mockAgreementTemplateStatic).batchGetByCode(templateCodes);
        }
    }

    /**
     * 测试getAgreementNoByCodeAndPayChannel - 正常场景
     */
    @Test
    public void testGetAgreementNoByCodeAndPayChannel() {
        // Given
        QueryAgreementNoByCodeReqDTO param = new QueryAgreementNoByCodeReqDTO();
        param.setTemplateCode("TEST_CODE");
        param.setPartnerId("PARTNER_001");
        param.setDutType(1);
        Integer payChannel = 1;
        
        AgreementNoInfo mockAgreementNoInfo = createMockAgreementNoInfo(1, "TEST_CODE", 1);
        AgreementTemplate mockTemplate = createMockAgreementTemplate("TEST_CODE", "测试模板");
        mockTemplate.setAttributes("{\"firstRenewDays\":30}");

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class);
             MockedStatic<AgreementTemplate> mockedAgreementTemplate = mockStatic(AgreementTemplate.class)) {
            
            AgreementNoInfo mockAgreementNoInfoStatic = mock(AgreementNoInfo.class);
            AgreementTemplate mockAgreementTemplateStatic = mock(AgreementTemplate.class);
            
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfoStatic);
            mockedAgreementTemplate.when(AgreementTemplate::of).thenReturn(mockAgreementTemplateStatic);
            
            when(mockAgreementNoInfoStatic.getAgreementNoByCodeAndPayChannel("TEST_CODE", payChannel, "PARTNER_001", 1))
                .thenReturn(mockAgreementNoInfo);
            when(mockAgreementTemplateStatic.getByCode("TEST_CODE")).thenReturn(mockTemplate);

            // When
            SingleResponse<AgreementNoInfoDTO> result = agreementQryExe.getAgreementNoByCodeAndPayChannel(param, payChannel);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertEquals(Integer.valueOf(1), result.getData().getId());
            assertEquals(Integer.valueOf(30), result.getData().getFirstRenewDays());
            
            verify(mockAgreementNoInfoStatic).getAgreementNoByCodeAndPayChannel("TEST_CODE", payChannel, "PARTNER_001", 1);
            verify(mockAgreementTemplateStatic).getByCode("TEST_CODE");
        }
    }



    /**
     * 测试getDefaultAgreementNoByDutType - 验证数据库查询
     */
    @Test
    public void testGetDefaultAgreementNoByDutType() {
        // Given
        Integer dutType = 1;
        Integer amount = 100;
        Long vipType = 1L;

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class)) {
            AgreementNoInfo mockAgreementNoInfoStatic = mock(AgreementNoInfo.class);
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfoStatic);
            
            when(mockAgreementNoInfoStatic.getDefaultAgreementNoByDutType(dutType, amount, vipType))
                .thenReturn(789);

            // When
            SingleResponse<Integer> result = agreementQryExe.getDefaultAgreementNoByDutType(dutType, amount, vipType);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(Integer.valueOf(789), result.getData());
            
            // 验证数据库查询被调用
            verify(mockAgreementNoInfoStatic).getDefaultAgreementNoByDutType(dutType, amount, vipType);
        }
    }

    /**
     * 测试getAgreementNoInfosByType - 验证数据库查询
     */
    @Test
    public void testGetAgreementNoInfosByType() {
        // Given
        int type = 1;
        List<AgreementNoInfo> mockList = Arrays.asList(
            createMockAgreementNoInfo(1, "CODE_001", type),
            createMockAgreementNoInfo(2, "CODE_002", type)
        );

        try (MockedStatic<AgreementNoInfo> mockedAgreementNoInfo = mockStatic(AgreementNoInfo.class)) {
            AgreementNoInfo mockAgreementNoInfoStatic = mock(AgreementNoInfo.class);
            mockedAgreementNoInfo.when(AgreementNoInfo::of).thenReturn(mockAgreementNoInfoStatic);
            
            when(mockAgreementNoInfoStatic.getAgreementNoInfosByType(type)).thenReturn(mockList);

            // When
            MultiResponse<AgreementNoInfoDTO> result = agreementQryExe.getAgreementNoInfosByType(type);

            // Then
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(2, result.getData().size());
            assertEquals(Integer.valueOf(1), result.getData().get(0).getId());
            assertEquals(Integer.valueOf(2), result.getData().get(1).getId());
            
            // 验证数据库查询被调用
            verify(mockAgreementNoInfoStatic).getAgreementNoInfosByType(type);
        }
    }

    // Helper methods for creating mock objects
    private AgreementTemplate createMockAgreementTemplate(String templateCode, String templateName) {
        AgreementTemplate template = mock(AgreementTemplate.class);
        when(template.getCode()).thenReturn(templateCode);
        when(template.getName()).thenReturn(templateName);
        when(template.getAttributes()).thenReturn("{}");
        return template;
    }

    private AgreementNoInfo createMockAgreementNoInfo(Integer id, String templateCode, Integer type) {
        AgreementNoInfo info = mock(AgreementNoInfo.class);
        when(info.getId()).thenReturn(id);
        when(info.getTemplateCode()).thenReturn(templateCode);
        when(info.getType()).thenReturn(type);
        when(info.getDutType()).thenReturn(1);
        return info;
    }

    private AgreementMaterial createMockAgreementMaterial(Integer agreementNo, String description, String detailUrl) {
        AgreementMaterial material = mock(AgreementMaterial.class);
        when(material.getAgreementNo()).thenReturn(agreementNo);
        when(material.getDescription()).thenReturn(description);
        when(material.getDetailUrl()).thenReturn(detailUrl);
        return material;
    }
}