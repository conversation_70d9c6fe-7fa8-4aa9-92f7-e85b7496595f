package com.qiyi.vip.agreement;

import com.qiyi.vip.config.TestConfiguration;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * AgreementService集成测试
 * 测试真实的数据库查询操作
 *
 * 注意：这个测试需要：
 * 1. 数据库连接配置正确（通过 dev profile）
 * 2. 相关的配置文件能被正确加载（skuIdentifierToAgreementTypeMap 等）
 * 3. 所有依赖的 Bean 都能被正确注入
 *
 * 使用 TestConfiguration 确保测试环境与生产环境配置一致
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestConfiguration.class)
public class AgreementServiceIntegrationTest {

    @Autowired
    private AgreementServiceImpl agreementService;

    /**
     * 测试配置是否正确加载
     * 验证 skuIdentifierToAgreementTypeMap 配置是否包含纯签约配置
     */
    @Test
    public void testConfiguration_SkuIdentifierMapping() {
        // Given & When - 测试纯签约配置是否存在
        try {
            MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(5, 1L);
            // 如果没有抛出 "skuIdentifier参数有误" 异常，说明配置加载成功
            assertNotNull("纯签约配置应该存在，查询结果不应为空", result);
            System.out.println("✓ 纯签约配置加载成功");
        } catch (Exception e) {
            if (e.getMessage().contains("skuIdentifier参数有误")) {
                fail("纯签约配置未正确加载：skuIdentifier=5 应该映射到 [1]");
            }
            // 其他异常（如数据库连接问题）可以忽略，我们只关心配置是否加载
            System.out.println("配置加载成功，但可能存在其他问题：" + e.getMessage());
        }
    }

    /**
     * 集成测试 - queryBySkuIdentifier纯签约场景
     * 验证isPureSign=true时的数据库查询
     */
    @Test
    public void testQueryBySkuIdentifier_PureSign_DatabaseQuery() {
        // Given
        Integer skuIdentifier = 5; // 纯签约
        Long vipType = 1L;

        // When - 执行真实的数据库查询
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull("查询结果不能为空", result);
        assertTrue("查询应该成功", result.isSuccess());
        
        // 验证返回的数据结构
        if (result.getData() != null && !result.getData().isEmpty()) {
            for (AgreementTemplateDTO template : result.getData()) {
                assertNotNull("模板代码不能为空", template.getCode());
                System.out.println("纯签约模板: " + template.getCode());
            }
        }
        
        System.out.println("纯签约查询结果数量: " + (result.getData() != null ? result.getData().size() : 0));
    }

    /**
     * 集成测试 - queryBySkuIdentifier常规连包场景
     * 验证isPureSign=false时的数据库查询
     */
    @Test
    public void testQueryBySkuIdentifier_RegularPackage_DatabaseQuery() {
        // Given
        Integer skuIdentifier = 1; // 常规连包
        Long vipType = 1L;

        // When - 执行真实的数据库查询
        MultiResponse<AgreementTemplateDTO> result = agreementService.queryBySkuIdentifier(skuIdentifier, vipType);

        // Then
        assertNotNull("查询结果不能为空", result);
        assertTrue("查询应该成功", result.isSuccess());
        
        // 验证返回的数据结构
        if (result.getData() != null && !result.getData().isEmpty()) {
            for (AgreementTemplateDTO template : result.getData()) {
                assertNotNull("模板代码不能为空", template.getCode());
                System.out.println("常规连包模板: " + template.getCode());
            }
        }
        
        System.out.println("常规连包查询结果数量: " + (result.getData() != null ? result.getData().size() : 0));
    }

    /**
     * 集成测试 - 对比纯签约和非纯签约的查询结果
     * 验证isPureSign参数对查询结果的影响
     */
    @Test
    public void testCompare_PureSign_vs_RegularPackage() {
        // Given
        Long vipType = 1L;

        // When - 查询纯签约
        MultiResponse<AgreementTemplateDTO> pureSignResult = agreementService.queryBySkuIdentifier(5, vipType);
        
        // When - 查询常规连包
        MultiResponse<AgreementTemplateDTO> regularResult = agreementService.queryBySkuIdentifier(1, vipType);

        // Then
        assertNotNull("纯签约查询结果不能为空", pureSignResult);
        assertNotNull("常规连包查询结果不能为空", regularResult);
        assertTrue("纯签约查询应该成功", pureSignResult.isSuccess());
        assertTrue("常规连包查询应该成功", regularResult.isSuccess());
        
        System.out.println("纯签约查询结果数量: " + (pureSignResult.getData() != null ? pureSignResult.getData().size() : 0));
        System.out.println("常规连包查询结果数量: " + (regularResult.getData() != null ? regularResult.getData().size() : 0));
    }
}