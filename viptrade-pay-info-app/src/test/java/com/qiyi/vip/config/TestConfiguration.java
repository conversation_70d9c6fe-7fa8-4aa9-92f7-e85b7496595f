package com.qiyi.vip.config;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import com.qiyi.vip.commons.component.UserTagApi;

/**
 * 集成测试专用的 Spring Boot 配置类
 * 模仿主应用的配置，确保测试环境能正确加载所有必要的组件和配置
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(
    basePackages = {"com.qiyi.vip"},
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {UserTagApi.class})
    }
)
public class TestConfiguration {
    // 测试配置类，用于集成测试
}
