<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qiyi.vip</groupId>
        <artifactId>viptrade-pay-info</artifactId>
        <version>1.3.50</version>
    </parent>

    <artifactId>viptrade-pay-info-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>viptrade-pay-info-infrastructure</name>

    <properties>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.qiyi.vip.commons</groupId>
            <artifactId>vip-commons-component</artifactId>
            <version>1.0.34-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.qiyi.usercloud</groupId>
                    <artifactId>enigma-uid</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iqiyi.config</groupId>
            <artifactId>config-client</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.7</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-starter-redis-lettuce</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
        </dependency>
        <!-- 簇点链路功能 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
        </dependency>
        <!-- 配置中心动态规则管理 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-apollo</artifactId>
        </dependency>
        <!-- 对接全链路平台Prometheus指标监控 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-metric-prometheus</artifactId>
        </dependency>
        <!-- sentinel 热点参数限流必须引入 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-parameter-flow-control</artifactId>
        </dependency>
        <!-- sentinel切面，配合@SentinelResource注解使用 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-annotation-aspectj</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-apache-httpclient-adapter</artifactId>
        </dependency>
        <!-- Spring WebMvc jar引入 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-spring-webmvc-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.db</groupId>
            <artifactId>mysql-dal-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <configurationFile>src/main/resources/mybatis/mybatis-generator.xml</configurationFile>
                    <includeCompileDependencies>true</includeCompileDependencies>
                </configuration>
                <!-- 自动生成 -->
                <executions>
                    <execution>
                        <id>Generate MyBatis Artifacts</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!--    <build>-->
    <!--        <plugins>-->

    <!--            <plugin>-->
    <!--                &lt;!&ndash;Mybatis-generator插件,用于自动生成Mapper和POJO&ndash;&gt;-->
    <!--                <groupId>org.mybatis.generator</groupId>-->
    <!--                <artifactId>mybatis-generator-maven-plugin</artifactId>-->
    <!--                <version>1.3.2</version>-->
    <!--                <configuration>-->
    <!--                    &lt;!&ndash;配置文件的位置&ndash;&gt;-->
    <!--                    <configurationFile>src/main/resources/mybatis/mybatis-generator.xml</configurationFile>-->
    <!--                    <verbose>true</verbose>-->
    <!--                    <overwrite>true</overwrite>-->
    <!--                </configuration>-->
    <!--                <executions>-->
    <!--                    <execution>-->
    <!--                        <id>Generate MyBatis Artifacts</id>-->
    <!--                        <goals>-->
    <!--                            <goal>generate</goal>-->
    <!--                        </goals>-->
    <!--                    </execution>-->
    <!--                </executions>-->
    <!--                <dependencies>-->
    <!--                    <dependency>-->
    <!--                        <groupId>org.mybatis.generator</groupId>-->
    <!--                        <artifactId>mybatis-generator-core</artifactId>-->
    <!--                        <version>1.3.2</version>-->
    <!--                    </dependency>-->
    <!--                </dependencies>-->
    <!--            </plugin>-->


    <!--        </plugins>-->
    <!--    </build>-->
</project>
