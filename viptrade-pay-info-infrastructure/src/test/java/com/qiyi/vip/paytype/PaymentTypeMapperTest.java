package com.qiyi.vip.paytype;

import java.util.List;

import com.qiyi.vip.Mybatis3Utils;
import org.apache.ibatis.session.SqlSession;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 20:31 2021/3/8
 */
public class PaymentTypeMapperTest {
    SqlSession sqlSession;
    private PaymentTypeMapper paymentTypeMapper;

    @Before
    public void before() {
        sqlSession = Mybatis3Utils.getCurrentSqlSession();
        paymentTypeMapper = sqlSession.getMapper(PaymentTypeMapper.class);
    }

    @Test
    public void getPaymentTypeById() {
        PaymentTypeDO paymentTypeDO = paymentTypeMapper.getPaymentTypeById(65L);
        Assert.assertNotNull(paymentTypeDO);
    }

    @Test
    public void getPaymentTypeByIds() {
    }

    @Test
    public void getPasswordFreeSignPayTypesByIds() {
    }

    @Test
    public void getPayTypeByChannel() {
        List<PaymentTypeDO> paymentTypeDOS = paymentTypeMapper.getPayTypeByChannel(2, null, "1.0");
        Assert.assertNotNull(paymentTypeDOS);
    }

    @Test
    public void getPaymentTypeByProperties() {
        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        paymentTypeDO.setPayCenterCode("ALIPAY");
        List<PaymentTypeDO> paymentTypeDOS = paymentTypeMapper.getPaymentTypeByProperties(paymentTypeDO);
        Assert.assertNotNull(paymentTypeDOS);
    }
}