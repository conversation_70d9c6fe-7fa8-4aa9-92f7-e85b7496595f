package com.qiyi.vip.duttype;

import java.util.List;
import java.util.SortedSet;

import com.qiyi.vip.Mybatis3Utils;
import org.apache.ibatis.session.SqlSession;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 19:50 2021/3/9
 */
public class PaymentDutTypeMapperTest {
    SqlSession sqlSession;
    private PaymentDutTypeMapper paymentDutTypeMapper;

    @Before
    public void before() {
        sqlSession = Mybatis3Utils.getCurrentSqlSession();
        paymentDutTypeMapper = sqlSession.getMapper(PaymentDutTypeMapper.class);
    }

    @Test
    public void getPaymentDutTypeList() {
    }

    @Test
    public void getPaymentDutTypesByDutType() {
    }

    @Test
    public void getDutTypeByViptypeAndAmountExcludeActCode() {
    }

    @Test
    public void getDutTypeByActCode() {
    }

    @Test
    public void getPayTypeByPayChannel() {
    }

    @Test
    public void getDutTypeByPayType() {
    }

    @Test
    public void getDutTypeWithPayType() {
    }

    @Test
    public void getDutTypeWithChannel() {
        SortedSet<Integer> paytypes = paymentDutTypeMapper.getDutTypeWithChannel(
                9, null, 6L, 1, "iqiyi_vip_iphone_video_autorenew_tw", null,
                24000, null);
        Assert.assertNotNull(paytypes);
        Assert.assertTrue(paytypes.size() > 0);
    }

    @Test
    public void getDutTypes() {
        List<PaymentDutTypeDO> paytypes = paymentDutTypeMapper.getDutTypes(301L, 1, 1);
        Assert.assertNotNull(paytypes);
        Assert.assertTrue(paytypes.size() > 0);
    }
}