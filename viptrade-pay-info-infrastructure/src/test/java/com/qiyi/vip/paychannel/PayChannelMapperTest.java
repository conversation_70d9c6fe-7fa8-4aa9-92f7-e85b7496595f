package com.qiyi.vip.paychannel;

import com.qiyi.vip.Mybatis3Utils;
import org.apache.ibatis.session.SqlSession;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
public class PayChannelMapperTest {
    SqlSession sqlSession;
    private PayChannelMapper payChannelMapper;

    @Before
    public void before() {
        sqlSession = Mybatis3Utils.getCurrentSqlSession();
        payChannelMapper = sqlSession.getMapper(PayChannelMapper.class);
    }

    @Test
    public void getById() {
        PayChannelDO payChannelDO = payChannelMapper.getById(1);
        Assert.assertNotNull(payChannelDO);
    }
}