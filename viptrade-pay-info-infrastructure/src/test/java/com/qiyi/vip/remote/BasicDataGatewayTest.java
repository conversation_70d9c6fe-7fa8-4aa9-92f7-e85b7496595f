package com.qiyi.vip.remote;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import com.qiyi.vip.domain.remote.VipType;

/**
 * @author: guojing
 * @date: 2024/6/25 18:57
 */
@SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@ImportResource(locations = "classpath:mybatis-config-test.xml")
public class BasicDataGatewayTest {

    @Resource
    BasicDataGatewayImpl basicDataGateway;

    @Test
    public void test() {
        VipType vipTypeObj = basicDataGateway.getVipTypeById(1L);
        System.out.println(vipTypeObj);
        Assert.assertNotNull(vipTypeObj);
    }
}
