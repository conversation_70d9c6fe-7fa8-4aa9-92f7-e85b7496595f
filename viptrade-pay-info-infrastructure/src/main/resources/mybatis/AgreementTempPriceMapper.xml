<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.agreement.AgreementTempPriceMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.agreement.AgreementTempPriceDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
<!--        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>-->
        <result column="period_no" jdbcType="INTEGER" property="periodNo"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="original_price" jdbcType="INTEGER" property="originalPrice"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, agreement_no, period_no, price, original_price, status, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.agreement.AgreementTempPriceDO" useGeneratedKeys="true" keyProperty="id">
        insert into agreement_temp_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="agreementNo != null">
                agreement_no,
            </if>
<!--            <if test="payChannel != null">-->
<!--                pay_channel,-->
<!--            </if>-->
            <if test="periodNo != null">
                period_no,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="agreementNo != null">
                #{agreementNo,jdbcType=INTEGER},
            </if>
<!--            <if test="payChannel != null">-->
<!--                #{payChannel,jdbcType=INTEGER},-->
<!--            </if>-->
            <if test="periodNo != null">
                #{periodNo,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=INTEGER},
            </if>
            <if test="originalPrice != null">
                #{originalPrice,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.qiyi.vip.agreement.AgreementTempPriceDO">
        update agreement_temp_price
        <set>
            <if test="agreementNo != null">
                agreement_no = #{agreementNo,jdbcType=INTEGER},
            </if>
<!--            <if test="payChannel != null">-->
<!--                pay_channel = #{payChannel,jdbcType=INTEGER},-->
<!--            </if>-->
            <if test="periodNo != null">
                period_no = #{periodNo,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByAgreementNo" parameterType="com.qiyi.vip.agreement.AgreementTempPriceDO">
        update agreement_temp_price
        <set>
<!--            <if test="payChannel != null">-->
<!--                pay_channel = #{payChannel,jdbcType=INTEGER},-->
<!--            </if>-->
            <if test="periodNo != null">
                period_no = #{periodNo,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where agreement_no = #{agreementNo,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_temp_price
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByAgreementNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_temp_price
        where agreement_no = #{agreementNo,jdbcType=INTEGER}
        order by period_no
    </select>

    <select id="selectByTemplateCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_temp_price
        where status = 1 and period_no = 0 and agreement_code = #{templateCode}
    </select>
</mapper>