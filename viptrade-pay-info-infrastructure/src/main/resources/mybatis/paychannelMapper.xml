<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.paychannel.PayChannelMapper">

    <resultMap type="com.qiyi.vip.paychannel.PayChannelDO" id="payChannelMap">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result property="code" column="code"/>
        <result property="categoryCode" column="category_code"/>
        <result property="description" column="description"/>
        <result property="dutAgreementName" column="dut_agreement_name"/>
        <result property="dutAgreementUrl" column="dut_agreement_url"/>
        <result property="promotionText" column="promotion_text"/>
        <result property="iconUrl" column="icon_url"/>
        <result property="properties" column="properties"/>
    </resultMap>

    <insert id="insert" parameterType="com.qiyi.vip.paychannel.PayChannelDO">
        insert into payment_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">
                code,
            </if>
            <if test="categoryCode != null">
                category_code,
            </if>
            <if test="description != null and description != ''">
                description,
            </if>
            <if test="dutAgreementName != null">
                dut_agreement_name,
            </if>
            <if test="dutAgreementUrl != null">
                dut_agreement_url,
            </if>
            <if test="promotionText != null">
                promotion_text,
            </if>
            <if test="iconUrl != null">
                icon_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">
                #{code},
            </if>
            <if test="categoryCode != null">
                #{categoryCode},
            </if>
            <if test="description != null and description != ''">
                #{description},
            </if>
            <if test="dutAgreementName != null">
                #{dutAgreementName},
            </if>
            <if test="dutAgreementUrl != null">
                #{dutAgreementUrl},
            </if>
            <if test="promotionText != null">
                #{promotionText},
            </if>
            <if test="iconUrl != null">
                #{iconUrl},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.qiyi.vip.paychannel.PayChannelDO">
        update payment_channel
        <set>
            <if test="dutAgreementName != null">
                dut_agreement_name = #{dutAgreementName},
            </if>
            <if test="dutAgreementUrl != null">
                dut_agreement_url = #{dutAgreementUrl},
            </if>
            <if test="promotionText != null">
                promotion_text = #{promotionText},
            </if>
            <if test="iconUrl != null">
                icon_url = #{iconUrl},
            </if>
        </set>
        where id = #{id} and code = #{code} and description = #{description}
        <if test="categoryCode != null and categoryCode != ''">
            and category_code = #{categoryCode}
        </if>
    </update>

    <select id="getById" resultType="com.qiyi.vip.paychannel.PayChannelDO" resultMap="payChannelMap">
        select *
        from payment_channel
        where id = #{id}
    </select>

    <select id="getByCode" resultType="com.qiyi.vip.paychannel.PayChannelDO" resultMap="payChannelMap">
        select *
        from payment_channel
        where code = #{code}
    </select>

    <select id="findAll" resultMap="payChannelMap">
        select *
        from payment_channel
    </select>

    <select id="getByIdOrNameOrCode" resultMap="payChannelMap">
        select *
        from payment_channel
        where 1 = 1
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="code != null and code != ''">
            and code like concat('%', #{code}, '%')
        </if>
        <if test="name != null and name != ''">
            and description like concat('%', #{name}, '%')
        </if>
        order by id desc
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="getTopPayChannels" resultMap="payChannelMap">
        select *
        from payment_channel
        where category_code is null
    </select>

    <select id="getCountByIdOrNameOrCode" resultType="java.lang.Integer">
        select count(*) from payment_channel
        where 1 = 1
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="code != null and code != ''">
            and code like concat('%', #{code}, '%')
        </if>
        <if test="name != null and name != ''">
            and description like concat('%', #{name}, '%')
        </if>
    </select>
</mapper>
