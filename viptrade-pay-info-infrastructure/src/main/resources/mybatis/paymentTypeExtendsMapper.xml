<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qiyi.vip.paytype.PaymentTypeExtendsMapper" >
  <resultMap id="BaseResultMap" type="com.qiyi.vip.paytype.PaymentTypeExtendsDO" >
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="pay_type" property="payType" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, pay_type, version, create_time, update_time
  </sql>

  <insert id="insert" parameterType="com.qiyi.vip.paytype.PaymentTypeExtendsDO" >
    insert into payment_type_extends (id, pay_type, version, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{payType,jdbcType=INTEGER}, #{version,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="com.qiyi.vip.paytype.PaymentTypeExtendsDO" >
    insert into payment_type_extends
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>