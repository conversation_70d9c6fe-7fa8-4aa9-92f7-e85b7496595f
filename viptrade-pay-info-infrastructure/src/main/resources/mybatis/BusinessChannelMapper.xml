<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.paychannel.BusinessChannelMapper">
    
    <resultMap id="BaseResultMap" type="com.qiyi.vip.paychannel.BusinessChannelDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="business" property="business" jdbcType="VARCHAR"/>
        <result column="pay_channel" property="payChannel" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, business, pay_channel, status, create_time, update_time, operator
    </sql>

    <select id="getById" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List"/>
        from business_channel
        where id = #{id}
    </select>

    <select id="getByBusiness" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List"/>
        from business_channel
        where business = #{business}
        and status = 1
    </select>

    <insert id="insert" parameterType="com.qiyi.vip.paychannel.BusinessChannelDO" useGeneratedKeys="true" keyProperty="id">
        insert into business_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="business != null">business,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operator != null">operator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="business != null">#{business,jdbcType=VARCHAR},</if>
            <if test="payChannel != null">#{payChannel,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.qiyi.vip.paychannel.BusinessChannelDO">
        update business_channel
        <set>
            <if test="business != null">business = #{business,jdbcType=VARCHAR},</if>
            <if test="payChannel != null">pay_channel = #{payChannel,jdbcType=INTEGER},</if>
            <if test="status != null">status = #{status,jdbcType=INTEGER},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="operator != null">operator = #{operator,jdbcType=VARCHAR},</if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteById">
        update business_channel 
        set status = 0,
            update_time = now()
        where id = #{id}
    </update>
</mapper>