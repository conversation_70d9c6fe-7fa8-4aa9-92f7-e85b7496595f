<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.paytype.PayScenePaymentMapper">
  <resultMap id="BaseResultMap" type="com.qiyi.vip.paytype.PayScenePaymentDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="scene_code" jdbcType="VARCHAR" property="sceneCode" />
    <result column="scene_name" jdbcType="VARCHAR" property="sceneName" />
    <result column="scene_env" jdbcType="VARCHAR" property="sceneEnv" />
    <result column="scenario" jdbcType="VARCHAR" property="scenario" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="basic_pay_type" jdbcType="INTEGER" property="basicPayType" />
    <result column="sign_pay_type" jdbcType="INTEGER" property="signPayType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, scene_code, scene_name, scene_env, scenario, pay_channel, basic_pay_type, sign_pay_type,
    status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from pay_scene_payment
    where id = #{id,jdbcType=INTEGER}
  </select>

    <select id="selectBySceneCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from pay_scene_payment
        where scene_code = #{sceneCode,jdbcType=VARCHAR} and status = 1
    </select>

</mapper>