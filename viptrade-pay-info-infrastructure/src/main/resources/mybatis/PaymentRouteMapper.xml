<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qiyi.vip.paytype.PaymentRouteMapper">

    <!-- 定义基础 ResultMap -->
    <resultMap id="BaseResultMap" type="com.qiyi.vip.paytype.PaymentRouteDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="version_from" property="versionFrom" jdbcType="VARCHAR"/>
        <result column="version_to" property="versionTo" jdbcType="VARCHAR"/>
        <result column="pay_type" property="payType" jdbcType="BIGINT"/>
        <result column="pay_channel" property="payChannel" jdbcType="INTEGER"/>
        <result column="ab_test_group" property="abTestGroup" jdbcType="VARCHAR"/>
        <result column="condition_ext" property="conditionExt" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, platform, version_from, version_to,pay_type, pay_channel,ab_test_group, condition_ext, status,create_time, update_time, operator
    </sql>

    <!-- 根据平台查询支付路由 -->
    <select id="findByPlatform" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM payment_route
        WHERE platform = #{platform} and status=1
    </select>

</mapper>
