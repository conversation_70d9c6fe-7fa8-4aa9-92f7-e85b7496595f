<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.qiyi.vip.paytype.PaymentTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.paytype.PaymentTypeDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_chargeback" property="isChargeback" jdbcType="INTEGER"/>
        <result column="is_chargeauto" property="isChargeauto" jdbcType="INTEGER"/>
        <result column="is_background" property="isBackground" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="pay_center_code" property="payCenterCode" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="is_support_sign" property="isSupportSign" jdbcType="BIT"/>
        <result column="is_support_pure_sign" property="isSupportPureSign" jdbcType="BIT"/>
        <result column="basic_pay_type_id" property="basicPayTypeId" jdbcType="INTEGER"/>
        <result column="sign_pay_type_id" property="signPayTypeId" jdbcType="INTEGER"/>
        <result column="password_free_pay_type" property="passwordFreePayType" jdbcType="INTEGER"/>
        <result column="pay_channel" property="payChannel" jdbcType="INTEGER"/>
        <result column="sub_pay_channel" property="subPayChannel" jdbcType="INTEGER"/>
        <result column="pure_signing_pay_type_id" property="pureSigningPayTypeId" jdbcType="INTEGER"/>
        <result column="is_support_password_free_sign" property="isSupportPasswordFreeSign" jdbcType="BIT"/>
        <result column="password_free_open_tips" property="passwordFreeOpenTips" jdbcType="VARCHAR"/>
        <result column="refund_expire_offset" property="refundExpireOffset" jdbcType="INTEGER"/>
        <result column="properties" property="properties" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <collection property="payTypeExtendsDo" ofType="com.qiyi.vip.paytype.PaymentTypeExtendsDO">
            <result column="pay_type" property="payType"/>
            <result column="version" property="version"/>
            <result column="scenario" property="scenario"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, is_chargeback, is_chargeauto, is_background, status, pay_center_code,
    type, is_support_sign, basic_pay_type_id, sign_pay_type_id, password_free_pay_type, pay_channel, sub_pay_channel,
    pure_signing_pay_type_id, is_support_password_free_sign, password_free_open_tips,
    refund_expire_offset, properties, update_time, operator,is_support_pure_sign
    </sql>


    <select id="getPaymentTypeById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_type
        where id = #{payType}
    </select>

    <select id="getPayTypeByChannel" resultMap="BaseResultMap">
        select
        p.id,p.is_support_sign, p.pay_channel, p.sub_pay_channel,p.pure_signing_pay_type_id,
        p.is_support_password_free_sign,p.refund_expire_offset,e.pay_type,e.version,e.scenario
        from payment_type_extends e left join payment_type p on e.pay_type=p.id
        <where>
            p.pay_channel= #{channelId}
            and e.version= #{version}
            <if test="subChannelId != null and subChannelId !=0">
                and p.sub_pay_channel =#{subChannelId}
            </if>
            <!--      <if test="subChannelId == null or subChannelId==0">-->
            <!--        and p.sub_pay_channel is null-->
            <!--      </if>-->
            and status=1
        </where>
    </select>

    <select id="getPaymentTypeByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_type
        <trim prefix="WHERE (" suffix=")" prefixOverrides="AND |OR ">
            status = 1
            <if test="payTypes != null and payTypes.size() > 0">
                and id in
                <foreach collection="payTypes" item="payType" open="(" close=")" separator=",">
                    #{payType}
                </foreach>
            </if>
        </trim>
    </select>

    <sql id="condition">
        <trim prefix="WHERE (" suffix=")" prefixOverrides="AND |OR ">
            and status = 1
            <if test="isChargeback !=null">
                AND is_chargeback = #{isChargeback,jdbcType=INTEGER}
            </if>
            <if test="isChargeauto !=null">
                AND is_chargeauto = #{isChargeauto,jdbcType=INTEGER}
            </if>
            <if test="payCenterCode !=null and payCenterCode != ''">
                AND pay_center_code = #{payCenterCode,jdbcType=VARCHAR}
            </if>
            <if test="payChannel !=null and payChannel != ''">
                AND pay_channel = #{payChannel,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <select id="getPaymentTypeByProperties" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_type
        <include refid="condition"/>
    </select>

    <select id="getPasswordFreeSignPayTypesByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_type
        <where>
            is_support_password_free_sign = 1
            <if test="payTypes != null and payTypes.size() > 0">
                and id in
                <foreach collection="payTypes" item="payType" open="(" close=")" separator=",">
                    #{payType}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getPayTypeByPayChannel" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from payment_type
        where ((pay_channel = #{channelId} and sub_pay_channel is null ) or sub_pay_channel = #{channelId})
    </select>

    <select id="getAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_type
        where status = 1
    </select>

    <update id="update" parameterType="com.qiyi.vip.paytype.PaymentTypeDO">
        update payment_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="isChargeback != null">
                is_chargeback = #{isChargeback,jdbcType=INTEGER},
            </if>
            <if test="isChargeauto != null">
                is_chargeauto = #{isChargeauto,jdbcType=INTEGER},
            </if>
            <if test="isBackground != null">
                is_background = #{isBackground,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="payCenterCode != null">
                pay_center_code = #{payCenterCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="isSupportSign != null">
                is_support_sign = #{isSupportSign,jdbcType=INTEGER},
            </if>
            <if test="basicPayTypeId != null">
                basic_pay_type_id = #{basicPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="signPayTypeId != null">
                sign_pay_type_id = #{signPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="subPayChannel != null">
                sub_pay_channel = #{subPayChannel,jdbcType=INTEGER},
            </if>
            <if test="pureSigningPayTypeId != null">
                pure_signing_pay_type_id = #{pureSigningPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="isSupportPasswordFreeSign != null">
                is_support_password_free_sign = #{isSupportPasswordFreeSign,jdbcType=INTEGER},
            </if>
            <if test="passwordFreeOpenTips != null">
                password_free_open_tips = #{passwordFreeOpenTips,jdbcType=VARCHAR},
            </if>
            <if test="refundExpireOffset != null">
                refund_expire_offset = #{refundExpireOffset,jdbcType=INTEGER},
            </if>
            <if test="properties != null">
                properties = #{properties,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            update_time = now(),
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insert" parameterType="com.qiyi.vip.paytype.PaymentTypeDO" >
        insert into payment_type
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="description != null" >
                description,
            </if>
            <if test="isChargeback != null" >
                is_chargeback,
            </if>
            <if test="isChargeauto != null" >
                is_chargeauto,
            </if>
            <if test="isBackground != null" >
                is_background,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="payCenterCode != null" >
                pay_center_code,
            </if>
            <if test="type != null" >
                type,
            </if>
            <if test="isSupportSign != null" >
                is_support_sign,
            </if>
            <if test="basicPayTypeId != null" >
                basic_pay_type_id,
            </if>
            <if test="signPayTypeId != null" >
                sign_pay_type_id,
            </if>
            <if test="payChannel != null" >
                pay_channel,
            </if>
            <if test="subPayChannel != null" >
                sub_pay_channel,
            </if>
            <if test="pureSigningPayTypeId != null" >
                pure_signing_pay_type_id,
            </if>
            <if test="isSupportPasswordFreeSign != null" >
                is_support_password_free_sign,
            </if>
            <if test="passwordFreePayType != null">
                password_free_pay_type,
            </if>
            <if test="passwordFreeOpenTips != null" >
                password_free_open_tips,
            </if>
            <if test="refundExpireOffset != null">
                refund_expire_offset,
            </if>
            <if test="properties != null" >
                properties,
            </if>
            <if test="operator != null">
                operator,
            </if>
            update_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null" >
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="isChargeback != null" >
                #{isChargeback,jdbcType=INTEGER},
            </if>
            <if test="isChargeauto != null" >
                #{isChargeauto,jdbcType=INTEGER},
            </if>
            <if test="isBackground != null" >
                #{isBackground,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="payCenterCode != null" >
                #{payCenterCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=INTEGER},
            </if>
            <if test="isSupportSign != null" >
                #{isSupportSign,jdbcType=BIT},
            </if>
            <if test="basicPayTypeId != null" >
                #{basicPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="signPayTypeId != null" >
                #{signPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null" >
                #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="subPayChannel != null" >
                #{subPayChannel,jdbcType=INTEGER},
            </if>
            <if test="pureSigningPayTypeId != null" >
                #{pureSigningPayTypeId,jdbcType=INTEGER},
            </if>
            <if test="isSupportPasswordFreeSign != null" >
                #{isSupportPasswordFreeSign,jdbcType=BIT},
            </if>
            <if test="passwordFreePayType != null">
                #{passwordFreePayType,jdbcType=INTEGER},
            </if>
            <if test="passwordFreeOpenTips != null" >
                #{passwordFreeOpenTips,jdbcType=VARCHAR},
            </if>
            <if test="refundExpireOffset != null">
                #{refundExpireOffset,jdbcType=INTEGER},
            </if>
            <if test="properties != null" >
                #{properties,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                #{operator,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>

    <select id="getAdminPayTypesByCondition" parameterType="com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM payment_type
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="payCenterCode != null and payCenterCode != ''">
            AND pay_center_code = #{payCenterCode}
        </if>
        <if test="isChargeback != null">
            AND is_chargeback = #{isChargeback}
        </if>
        <if test="isChargeauto != null">
            AND is_chargeauto = #{isChargeauto}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="isBackground != null">
            AND is_background = #{isBackground}
        </if>
        <if test="payChannel != null">
            AND pay_channel = #{payChannel}
        </if>
        ORDER BY id DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAdminPayTypesByCondition" parameterType="com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM payment_type
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="payCenterCode != null and payCenterCode != ''">
            AND pay_center_code = #{payCenterCode}
        </if>
        <if test="isChargeback != null">
            AND is_chargeback = #{isChargeback}
        </if>
        <if test="isChargeauto != null">
            AND is_chargeauto = #{isChargeauto}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="isBackground != null">
            AND is_background = #{isBackground}
        </if>
        <if test="payChannel != null">
            AND pay_channel = #{payChannel}
        </if>
    </select>
</mapper>