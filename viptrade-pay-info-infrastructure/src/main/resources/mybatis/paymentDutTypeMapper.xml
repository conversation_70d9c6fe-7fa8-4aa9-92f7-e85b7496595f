<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.duttype.PaymentDutTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.duttype.PaymentDutTypeDO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="pay_type" property="payType" jdbcType="INTEGER"/>
        <result column="dut_type" property="dutType" jdbcType="INTEGER"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="pay_channel" property="payChannel" jdbcType="INTEGER"/>
        <result column="service_code" property="serviceCode" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="TINYINT"/>
        <result column="source_vip_type" property="sourceVipType" jdbcType="TINYINT"/>
        <result column="vip_type" property="vipType" jdbcType="TINYINT"/>
        <result column="partner_id" property="partnerId" jdbcType="VARCHAR"/>
        <result column="act_code" property="actCode" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="VARCHAR"/>
        <result column="agreement_act_code" property="agreementActCode" jdbcType="VARCHAR"/>
        <result column="renew_price" property="renewPrice" jdbcType="INTEGER"/>
        <result column="valid_start_time" property="validStartTime" jdbcType="TIMESTAMP"/>
        <result column="valid_end_time" property="validEndTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pay_type, dut_type, agreement_no, pay_channel, service_code, amount, source_vip_type, vip_type,partner_id,
    act_code,sku_id,agreement_act_code, renew_price, valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <insert id="insertSelective" parameterType="com.qiyi.vip.duttype.PaymentDutTypeDO">
        insert into payment_dut_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="payType != null">pay_type,</if>
            <if test="dutType != null">dut_type,</if>
            <if test="agreementNo != null">agreement_no,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="serviceCode != null">service_code,</if>
            <if test="amount != null">amount,</if>
            <if test="sourceVipType != null">source_vip_type,</if>
            <if test="vipType != null">vip_type,</if>
            <if test="actCode != null">act_code,</if>
            <if test="agreementActCode != null">agreement_act_code,</if>
            <if test="renewPrice != null">renew_price,</if>
            <if test="validStartTime != null">valid_start_time,</if>
            <if test="validEndTime != null">valid_end_time,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="type != null">type,</if>
            create_time,update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
            <if test="dutType != null">#{dutType,jdbcType=INTEGER},</if>
            <if test="agreementNo != null">#{agreementNo,jdbcType=INTEGER},</if>
            <if test="payChannel != null">#{payChannel,jdbcType=INTEGER},</if>
            <if test="serviceCode != null">#{serviceCode,jdbcType=VARCHAR},</if>
            <if test="amount != null">#{amount,jdbcType=TINYINT},</if>
            <if test="sourceVipType != null">#{sourceVipType,jdbcType=TINYINT},</if>
            <if test="vipType != null">#{vipType,jdbcType=TINYINT},</if>
            <if test="actCode != null">#{actCode,jdbcType=VARCHAR},</if>
            <if test="agreementActCode != null">#{agreementActCode,jdbcType=VARCHAR},</if>
            <if test="renewPrice != null">#{renewPrice,jdbcType=INTEGER},</if>
            <if test="validStartTime != null">#{validStartTime,jdbcType=TIMESTAMP},</if>
            <if test="validEndTime != null">#{validEndTime,jdbcType=TIMESTAMP},</if>
            <if test="partnerId != null">#{partnerId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            now(), now()
        </trim>
    </insert>

    <select id="getDutTypeWithPayType" resultType="integer">
        select dut_type
        from payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() and vip_type = #{vipType} and amount = #{amount} ]]>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="sourceVipType == null">
                and source_vip_type is null
            </if>
            <if test="actCode != null">
                and act_code = #{actCode}
            </if>
            <if test="actCode == null">
                and act_code is null
            </if>
            <if test="agreementActCode != null">
                and agreement_act_code = #{agreementActCode}
            </if>
            <if test="agreementActCode == null">
                and agreement_act_code is null
            </if>
            <if test="renewPrice != null">
                and renew_price = #{renewPrice}
            </if>
            <choose>
                <when test="partnerId != null">
                    and partner_id = #{partnerId}
                </when>
                <otherwise>
                    and partner_id =''
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getDutTypeWithChannel" resultType="integer">
        select dut_type
        from payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() and vip_type = #{vipType} and amount = #{amount} ]]>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="sourceVipType == null">
                and source_vip_type is null
            </if>
            <if test="actCode != null">
                and act_code = #{actCode}
            </if>
            <if test="actCode == null">
                and act_code is null
            </if>
            <if test="agreementActCode != null">
                and agreement_act_code = #{agreementActCode}
            </if>
            <if test="agreementActCode == null">
                and agreement_act_code is null
            </if>
            <if test="renewPrice != null">
                and renew_price = #{renewPrice}
            </if>
            <choose>
                <when test="partnerId != null">
                    and partner_id = #{partnerId}
                </when>
                <otherwise>
                    and partner_id =''
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectByPayChannel" resultMap="BaseResultMap">
        select *
        from payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() and vip_type = #{vipType} and amount = #{amount} ]]>
            <if test="payChannel != null">
                and pay_channel = #{payChannel}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="sourceVipType == null">
                and source_vip_type is null
            </if>
            <if test="renewPrice != null">
                and renew_price = #{renewPrice}
            </if>
            <choose>
                <when test="partnerId != null">
                    and partner_id = #{partnerId}
                </when>
                <otherwise>
                    and partner_id =''
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectByPayType" resultMap="BaseResultMap">
        select *
        from payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() and vip_type = #{vipType} and amount = #{amount} ]]>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="sourceVipType != null">
                and source_vip_type = #{sourceVipType}
            </if>
            <if test="sourceVipType == null">
                and source_vip_type is null
            </if>
            <if test="renewPrice != null">
                and renew_price = #{renewPrice}
            </if>
        </where>
    </select>

    <select id="getDutTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_dut_type
        <where>
            type=1
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
        </where>
    </select>

    <select id="getPaymentDutTypeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_dut_type
        <trim prefix="where" prefixOverrides="and | or">
            type=1
            <if test="amount != null and amount != '' ">
                and amount = #{amount}
            </if>
            <if test="dutTypes != null">
                and dut_type in
                <foreach collection="dutTypes" item="dutType" open="(" separator="," close=")">
                    #{dutType}
                </foreach>
            </if>
            <if test="actCode != null and actCode != '' ">
                and act_code = #{actCode}
            </if>
            <if test="actCode == null">
                and act_code is null
            </if>
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
        </trim>
        order by create_time DESC
    </select>

    <select id="getPaymentDutTypesByDutType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_dut_type
        where dut_type = #{dutType}
    </select>

    <select id="getDutTypeByViptypeAndAmountExcludeActCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_dut_type
        where type=1 and vip_type = #{vipType} and amount = #{amount} and act_code is null
        <choose>
            <when test="partnerId != null">
                and partner_id = #{partnerId}
            </when>
            <otherwise>
                and partner_id =''
            </otherwise>
        </choose>
    </select>

    <select id="getDutTypeByActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from payment_dut_type where type=1 and act_code = #{actCode} limit 1;
    </select>

    <select id="getPayTypeByPayChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM payment_dut_type
        where type=1 and pay_channel = #{payChannel}
        <if test="vipType != null">
            and vip_type = #{vipType}
        </if>
        order by create_time DESC
    </select>

    <select id="getDutTypeByPayType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM payment_dut_type where pay_type = #{payType}
    </select>

    <select id="getUniquePaymentDutType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM payment_dut_type
        <where>
            <if test="payType != null">and pay_type = #{payType}</if>
            <if test="vipType != null">and vip_type = #{vipType}</if>
            <if test="amount != null">and amount = #{amount}</if>
            <if test="dutType != null">and dut_type = #{dutType}</if>
            <if test="agreementNo != null">and agreement_no = #{agreementNo}</if>
            <if test="agreementNo == null">and agreement_no is null</if>
            <if test="renewPrice != null">and renew_price = #{renewPrice}</if>
            <if test="actCode != null and actCode != '' ">and act_code = #{actCode}</if>
            <if test="actCode == null">and act_code is null</if>
            <if test="agreementActCode != null and agreementActCode != '' ">and agreement_act_code = #{agreementActCode}</if>
            <if test="agreementActCode == null">and agreement_act_code is null</if>
        </where>
    </select>

    <update id="updatePaymentDutType" parameterType="com.qiyi.vip.duttype.PaymentDutTypeDO">
        update payment_dut_type
        <set>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="dutType != null">dut_type = #{dutType},</if>
            <if test="agreementNo != null">agreement_no = #{agreementNo},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="serviceCode != null">service_code = #{serviceCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="sourceVipType != null">source_vip_type = #{sourceVipType},</if>
            <if test="vipType != null">vip_type = #{vipType},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="actCode != null">act_code = #{actCode},</if>
            <if test="renewPrice != null">renew_price = #{renewPrice},</if>
            <if test="type != null">type = #{type},</if>
            <if test="validStartTime != null">valid_start_time = #{validStartTime},</if>
            <if test="validEndTime != null">valid_end_time = #{validEndTime},</if>
            update_time = now()
        </set>
        where agreement_act_code = #{agreementActCode}
    </update>

    <select id="getDutTypeByAgreementActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from payment_dut_type
        where agreement_act_code = #{agreementActCode};
    </select>

    <select id="getDutTypeBySku" resultMap="BaseResultMap">
        select *
        from payment_dut_type
        <where>
            <![CDATA[valid_start_time <= CURRENT_TIMESTAMP() and valid_end_time > CURRENT_TIMESTAMP() ]]>
            <if test="payChannel != null">
               and pay_channel = #{payChannel}
            </if>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="skuId != null">
                and sku_id = #{skuId}
            </if>
        </where>
    </select>
</mapper>