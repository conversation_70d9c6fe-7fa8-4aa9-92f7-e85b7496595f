<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.agreement.AgreementNoInfoMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.agreement.AgreementNoInfoDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="template_code" jdbcType="VARCHAR" property="templateCode"/>
        <result column="default_no" jdbcType="TINYINT" property="defaultNo"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="dut_pay_type" jdbcType="INTEGER" property="dutPayType"/>
        <result column="change_amount" jdbcType="TINYINT" property="changeAmount"/>
        <result column="direct_cancel" jdbcType="TINYINT" property="directCancel"/>
        <result column="support_pure_sign" jdbcType="TINYINT" property="supportPureSign"/>
        <result column="cancel_autorenw_unbind" jdbcType="TINYINT" property="cancelAutorenwUnbind"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="partner_id" jdbcType="VARCHAR" property="partnerId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, type, template_code, default_no, source_vip_type, vip_type, amount, pay_channel, pay_channel_name, pay_channel_type,
        priority, dut_type, dut_pay_type, change_amount, direct_cancel, support_pure_sign, cancel_autorenw_unbind,
        valid_start_time, valid_end_time, partner_id, status, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.agreement.AgreementNoInfoDO" useGeneratedKeys="true" keyProperty="id">
        insert into agreement_no_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="templateCode != null">
                template_code,
            </if>
            <if test="defaultNo != null">
                default_no,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="payChannelName != null">
                pay_channel_name,
            </if>
            <if test="payChannelType != null">
                pay_channel_type,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="dutType != null">
                dut_type,
            </if>
            <if test="dutPayType != null">
                dut_pay_type,
            </if>
            <if test="changeAmount != null">
                change_amount,
            </if>
            <if test="directCancel != null">
                direct_cancel,
            </if>
            <if test="supportPureSign != null">
                support_pure_sign,
            </if>
            <if test="cancelAutorenwUnbind != null">
                cancel_autorenw_unbind,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="partnerId != null">
                partner_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="templateCode != null">
                #{templateCode,jdbcType=VARCHAR},
            </if>
            <if test="defaultNo != null">
                #{defaultNo,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelName != null">
                #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TINYINT},
            </if>
            <if test="dutType != null">
                #{dutType,jdbcType=INTEGER},
            </if>
            <if test="dutPayType != null">
                #{dutPayType,jdbcType=INTEGER},
            </if>
            <if test="changeAmount != null">
                #{changeAmount,jdbcType=TINYINT},
            </if>
            <if test="directCancel != null">
                #{directCancel,jdbcType=TINYINT},
            </if>
            <if test="supportPureSign != null">
                #{supportPureSign,jdbcType=TINYINT},
            </if>
            <if test="cancelAutorenwUnbind != null">
                #{cancelAutorenwUnbind,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="partnerId != null">
                #{partnerId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.qiyi.vip.agreement.AgreementNoInfoDO">
        update agreement_no_info
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="templateCode != null">
                template_code = #{templateCode,jdbcType=VARCHAR},
            </if>
            <if test="defaultNo != null">
                default_no = #{defaultNo,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                pay_channel_type = #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TINYINT},
            </if>
            <if test="dutType != null">
                dut_type = #{dutType,jdbcType=INTEGER},
            </if>
            <if test="dutPayType != null">
                dut_pay_type = #{dutPayType,jdbcType=INTEGER},
            </if>
            <if test="changeAmount != null">
                change_amount = #{changeAmount,jdbcType=TINYINT},
            </if>
            <if test="directCancel != null">
                direct_cancel = #{directCancel,jdbcType=TINYINT},
            </if>
            <if test="supportPureSign != null">
                support_pure_sign = #{supportPureSign,jdbcType=TINYINT},
            </if>
            <if test="cancelAutorenwUnbind != null">
                cancel_autorenw_unbind = #{cancelAutorenwUnbind,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="partnerId != null">
                partner_id = #{partnerId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByDutType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where dut_type = #{dutType,jdbcType=INTEGER}
    </select>

    <select id="getDefaultAgreementNoByDutType" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select id
        from agreement_no_info
        where dut_type = #{dutType,jdbcType=INTEGER} and amount = #{amount,jdbcType=INTEGER} and default_no = 1
        <if test="vipType != null">
            and vip_type = #{vipType,jdbcType=INTEGER}
        </if>
        order by priority desc
        limit 1
    </select>

    <select id="getAgreementListByDutType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where dut_type = #{dutType,jdbcType=INTEGER} and status = 1
    </select>

    <select id="getAgreementNoInfosByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where type = #{type}
    </select>

    <select id="selectMaxPriorityBy" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where template_code = #{templateCode,jdbcType=VARCHAR}
        and pay_channel = #{payChannel,jdbcType=INTEGER}
        and status = 1
        <if test="dutType != null">
            and dut_type = #{dutType,jdbcType=INTEGER}
        </if>
        <if test="partnerId != null and partnerId != ''">
            and partner_id = #{partnerId,jdbcType=VARCHAR}
        </if>
        <if test="partnerId == null or partnerId == ''">
            and partner_id is null
        </if>
        order by priority desc
        limit 1
    </select>

    <select id="selectByTemplateCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_no_info
        where template_code = #{templateCode,jdbcType=VARCHAR}
        and status = 1
    </select>

    <select id="getMaxPriorityTemplateCodeByVipType" parameterType="java.lang.String" resultType="string">
        select distinct a.template_code
        from agreement_no_info a
        join (
            select vip_type,amount,pay_channel,max(priority) priority
            from agreement_no_info
            where type = #{type} and source_vip_type is null and pay_channel in (1,2,9) and amount in (1,3,12) and default_no = #{defaultNo} and status = 1 and `name` not like '%海外%' and `name` not like '%青春%'
            <if test="vipType != null">
                and vip_type = #{vipType}
            </if>
            <if test="isPureSign != null and isPureSign == true">
                and support_pure_sign = 1
            </if>
            group by vip_type,amount,pay_channel
        ) b on a.vip_type = b.vip_type and a.amount = b.amount and a.pay_channel = b.pay_channel and a.priority = b.priority
        where a.type = #{type} and a.source_vip_type is null and a.pay_channel in (1,2,9) and a.amount in (1,3,12) and a.default_no = #{defaultNo} and status = 1 and a.`name` not like '%海外%' and a.`name` not like '%青春%'
        <if test="vipType != null">
            and a.vip_type = #{vipType}
        </if>
        <if test="isPureSign != null and isPureSign == true">
            and a.support_pure_sign = 1
        </if>
    </select>

</mapper>