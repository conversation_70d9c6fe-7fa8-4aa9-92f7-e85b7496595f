<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.duttype.AgreementRouteConfigMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.duttype.AgreementRouteConfigDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="agreement_no" jdbcType="INTEGER" property="agreementNo"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="agreement_act_code" jdbcType="VARCHAR" property="agreementActCode"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, agreement_no, agreement_type, pay_channel, amount, source_vip_type, vip_type, agreement_act_code,
        valid_start_time, valid_end_time, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_route_config
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByPayChannelAndVipType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_route_config
        where valid_start_time &lt;= now()
        and (valid_end_time > now() or valid_end_time is null)
        and pay_channel = #{payChannel}
        and amount = #{amount}
        and vip_type = #{vipType}
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType}
        </if>
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        <if test="agreementActCode != null and agreementActCode != ''">
            and agreement_act_code = #{agreementActCode}
        </if>
        <if test="agreementActCode == null">
            and agreement_act_code is null
        </if>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from agreement_route_config
    </select>

</mapper>