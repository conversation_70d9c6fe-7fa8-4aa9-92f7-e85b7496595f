<?xml version="1.0" encoding="UTF-8" ?>
<!-- mybatis的配置文件 -->
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<mappers>
        <mapper resource="mybatis/paychannelMapper.xml"/>
        <mapper resource="mybatis/paymentDutTypeMapper.xml"/>
        <mapper resource="mybatis/paymentTypeExtendsMapper.xml"/>
        <mapper resource="mybatis/paymentTypeMapper.xml"/>
        <mapper resource="mybatis/PaymentRouteMapper.xml"/>
        <mapper resource="mybatis/AgreementDutMktMapper.xml"/>
        <mapper resource="mybatis/AgreementNoInfoMapper.xml"/>
        <mapper resource="mybatis/AgreementTemplateMapper.xml"/>
        <mapper resource="mybatis/AgreementTempPriceMapper.xml"/>
        <mapper resource="mybatis/AgreementSettlementRuleMapper.xml"/>
        <mapper resource="mybatis/AgreementCancelRestrictionMapper.xml"/>
        <mapper resource="mybatis/AgreementMaterialMapper.xml"/>
        <mapper resource="mybatis/AgreementRouteConfigMapper.xml"/>
        <mapper resource="mybatis/PayScenePaymentMapper.xml"/>
        <mapper resource="mybatis/AutoRenewDutTypeMapper.xml"/>
        <mapper resource="mybatis/BusinessChannelMapper.xml"/>
    </mappers>
</configuration>