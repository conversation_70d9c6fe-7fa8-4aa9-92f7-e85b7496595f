<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.duttype.AutoRenewDutTypeMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.duttype.AutoRenewDutTypeDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="agreement_type" jdbcType="TINYINT" property="agreementType"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="dut_pay_type" jdbcType="INTEGER" property="dutPayType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="change_amount" jdbcType="TINYINT" property="changeAmount"/>
        <result column="direct_cancel" jdbcType="TINYINT" property="directCancel"/>
        <result column="support_pure_sign" jdbcType="TINYINT" property="supportPureSign"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cancel_autorenw_unbind" jdbcType="TINYINT" property="cancelAutorenwUnbind"/>
        <result column="partner_id" jdbcType="VARCHAR" property="partnerId"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, dut_type, name, agreement_type, source_vip_type, vip_type, pay_channel, pay_channel_name,
        pay_channel_type, dut_pay_type, product_code, change_amount, direct_cancel, support_pure_sign,
        priority, status, valid_start_time, valid_end_time, create_time, update_time, cancel_autorenw_unbind,
        partner_id, business_code, operator
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.duttype.AutoRenewDutTypeDO" useGeneratedKeys="true" keyProperty="id">
        insert into autorenew_dut_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="dutType != null">
                dut_type,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="payChannel != null">
                pay_channel,
            </if>
            <if test="payChannelName != null">
                pay_channel_name,
            </if>
            <if test="payChannelType != null">
                pay_channel_type,
            </if>
            <if test="dutPayType != null">
                dut_pay_type,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="changeAmount != null">
                change_amount,
            </if>
            <if test="directCancel != null">
                direct_cancel,
            </if>
            <if test="supportPureSign != null">
                support_pure_sign,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="validStartTime != null">
                valid_start_time,
            </if>
            <if test="validEndTime != null">
                valid_end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="cancelAutorenwUnbind != null">
                cancel_autorenw_unbind,
            </if>
            <if test="partnerId != null">
                partner_id,
            </if>
            <if test="businessCode != null">
                business_code,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="dutType != null">
                #{dutType,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelName != null">
                #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="dutPayType != null">
                #{dutPayType,jdbcType=INTEGER},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="changeAmount != null">
                #{changeAmount,jdbcType=TINYINT},
            </if>
            <if test="directCancel != null">
                #{directCancel,jdbcType=TINYINT},
            </if>
            <if test="supportPureSign != null">
                #{supportPureSign,jdbcType=TINYINT},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelAutorenwUnbind != null">
                #{cancelAutorenwUnbind,jdbcType=TINYINT},
            </if>
            <if test="partnerId != null">
                #{partnerId,jdbcType=VARCHAR},
            </if>
            <if test="businessCode != null">
                #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.qiyi.vip.duttype.AutoRenewDutTypeDO">
        update autorenew_dut_type
        <set>
            <if test="dutType != null">
                dut_type = #{dutType,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                pay_channel_type = #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="dutPayType != null">
                dut_pay_type = #{dutPayType,jdbcType=INTEGER},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="changeAmount != null">
                change_amount = #{changeAmount,jdbcType=TINYINT},
            </if>
            <if test="directCancel != null">
                direct_cancel = #{directCancel,jdbcType=TINYINT},
            </if>
            <if test="supportPureSign != null">
                support_pure_sign = #{supportPureSign,jdbcType=TINYINT},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelAutorenwUnbind != null">
                cancel_autorenw_unbind = #{cancelAutorenwUnbind,jdbcType=TINYINT},
            </if>
            <if test="partnerId != null">
                partner_id = #{partnerId,jdbcType=VARCHAR},
            </if>
            <if test="businessCode != null">
                business_code = #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByDutType" parameterType="com.qiyi.vip.duttype.AutoRenewDutTypeDO">
        update autorenew_dut_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="payChannel != null">
                pay_channel = #{payChannel,jdbcType=INTEGER},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                pay_channel_type = #{payChannelType,jdbcType=INTEGER},
            </if>
            <if test="dutPayType != null">
                dut_pay_type = #{dutPayType,jdbcType=INTEGER},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="changeAmount != null">
                change_amount = #{changeAmount,jdbcType=TINYINT},
            </if>
            <if test="directCancel != null">
                direct_cancel = #{directCancel,jdbcType=TINYINT},
            </if>
            <if test="supportPureSign != null">
                support_pure_sign = #{supportPureSign,jdbcType=TINYINT},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="validStartTime != null">
                valid_start_time = #{validStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="validEndTime != null">
                valid_end_time = #{validEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelAutorenwUnbind != null">
                cancel_autorenw_unbind = #{cancelAutorenwUnbind,jdbcType=TINYINT},
            </if>
            <if test="partnerId != null">
                partner_id = #{partnerId,jdbcType=VARCHAR},
            </if>
            <if test="businessCode != null">
                business_code = #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        where dut_type = #{dutType,jdbcType=INTEGER}
    </update>

    <select id="selectByDutType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where dut_type = #{dutType,jdbcType=INTEGER}
    </select>

</mapper>