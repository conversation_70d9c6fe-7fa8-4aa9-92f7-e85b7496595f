<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiyi.vip.agreement.AgreementTemplateMapper">
    <resultMap id="BaseResultMap" type="com.qiyi.vip.agreement.AgreementTemplateDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pid" jdbcType="VARCHAR" property="pid"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="complete_order_pid" jdbcType="VARCHAR" property="completeOrderPid"/>
        <result column="complete_order_skuid" jdbcType="VARCHAR" property="completeOrderSkuId"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="period_type" jdbcType="TINYINT" property="periodType"/>
        <result column="periods" jdbcType="INTEGER" property="periods"/>
        <result column="discount_type" jdbcType="TINYINT" property="discountType"/>
        <result column="discount_periods" jdbcType="INTEGER" property="discountPeriods"/>
        <result column="period_duration" jdbcType="INTEGER" property="periodDuration"/>
        <result column="period_unit" jdbcType="TINYINT" property="periodUnit"/>
        <result column="pricing_strategy" jdbcType="TINYINT" property="pricingStrategy"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="attributes" jdbcType="VARCHAR" property="attributes"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, code, name, type, source_vip_type, vip_type, pid, sku_id, settlement_order_pid,
        complete_order_pid, complete_order_skuid, amount, period_type, periods, discount_type,
        discount_periods, period_duration, period_unit, pricing_strategy, status, attributes,
        create_time, update_time, operator
    </sql>

    <insert id="insert" parameterType="com.qiyi.vip.agreement.AgreementTemplateDO" useGeneratedKeys="true" keyProperty="id">
        insert into agreement_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="sourceVipType != null">
                source_vip_type,
            </if>
            <if test="vipType != null">
                vip_type,
            </if>
            <if test="pid != null">
                pid,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="settlementOrderPid != null">
                settlement_order_pid,
            </if>
            <if test="completeOrderPid != null">
                complete_order_pid,
            </if>
            <if test="completeOrderSkuid != null">
                complete_order_skuid,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="periodType != null">
                period_type,
            </if>
            <if test="periods != null">
                periods,
            </if>
            <if test="discountType != null">
                discount_type,
            </if>
            <if test="discountPeriods != null">
                discount_periods,
            </if>
            <if test="periodDuration != null">
                period_duration,
            </if>
            <if test="periodUnit != null">
                period_unit,
            </if>
            <if test="pricingStrategy != null">
                pricing_strategy,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="attributes != null">
                attributes,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="operator != null">
                operator,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                #{vipType,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=VARCHAR},
            </if>
            <if test="settlementOrderPid != null">
                #{settlementOrderPid,jdbcType=VARCHAR},
            </if>
            <if test="completeOrderPid != null">
                #{completeOrderPid,jdbcType=VARCHAR},
            </if>
            <if test="completeOrderSkuid != null">
                #{completeOrderSkuid,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="periodType != null">
                #{periodType,jdbcType=TINYINT},
            </if>
            <if test="periods != null">
                #{periods,jdbcType=INTEGER},
            </if>
            <if test="discountType != null">
                #{discountType,jdbcType=TINYINT},
            </if>
            <if test="discountPeriods != null">
                #{discountPeriods,jdbcType=INTEGER},
            </if>
            <if test="periodDuration != null">
                #{periodDuration,jdbcType=INTEGER},
            </if>
            <if test="periodUnit != null">
                #{periodUnit,jdbcType=TINYINT},
            </if>
            <if test="pricingStrategy != null">
                #{pricingStrategy,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="attributes != null">
                #{attributes,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByCode" parameterType="com.qiyi.vip.agreement.AgreementTemplateDO">
        update agreement_template
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="sourceVipType != null">
                source_vip_type = #{sourceVipType,jdbcType=INTEGER},
            </if>
            <if test="vipType != null">
                vip_type = #{vipType,jdbcType=INTEGER},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=VARCHAR},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=VARCHAR},
            </if>
            <if test="settlementOrderPid != null">
                settlement_order_pid = #{settlementOrderPid,jdbcType=VARCHAR},
            </if>
            <if test="completeOrderPid != null">
                complete_order_pid = #{completeOrderPid,jdbcType=VARCHAR},
            </if>
            <if test="completeOrderSkuid != null">
                complete_order_skuid = #{completeOrderSkuid,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="periodType != null">
                period_type = #{periodType,jdbcType=TINYINT},
            </if>
            <if test="periods != null">
                periods = #{periods,jdbcType=INTEGER},
            </if>
            <if test="discountType != null">
                discount_type = #{discountType,jdbcType=TINYINT},
            </if>
            <if test="discountPeriods != null">
                discount_periods = #{discountPeriods,jdbcType=INTEGER},
            </if>
            <if test="periodDuration != null">
                period_duration = #{periodDuration,jdbcType=INTEGER},
            </if>
            <if test="periodUnit != null">
                period_unit = #{periodUnit,jdbcType=TINYINT},
            </if>
            <if test="pricingStrategy != null">
                pricing_strategy = #{pricingStrategy,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="attributes != null">
                attributes = #{attributes,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        where code = #{code,jdbcType=VARCHAR}
    </update>

    <select id="selectByCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_template
        where code = #{code,jdbcType=VARCHAR}
    </select>

    <select id="batchGetByCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_template
        where code in
        <foreach collection="codes" item="code" open="(" close=")" separator=",">
            #{code,jdbcType=VARCHAR}
        </foreach>
        order by vip_type,amount
    </select>
    
    <select id="getDefaultBy" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from agreement_template
        where type = #{agreementType,jdbcType=INTEGER}
        <if test="sourceVipType != null">
            and source_vip_type = #{sourceVipType,jdbcType=INTEGER}
        </if>
        <if test="sourceVipType == null">
            and source_vip_type is null
        </if>
        and vip_type = #{vipType,jdbcType=INTEGER}
        and amount = #{amount,jdbcType=INTEGER}
        and agreement_no is null
        and discount_periods = 0
        and status = 1
    </select>

</mapper>