package com.qiyi.vip.duttype;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgreementRouteConfigMapper {

    AgreementRouteConfigDO selectByPrimaryKey(Integer id);

    List<AgreementRouteConfigDO> selectByPayChannelAndVipType(
        @Param("payChannel") Integer payChannel,
        @Param("sourceVipType") Long sourceVipType,
        @Param("vipType") Long vipType,
        @Param("amount") Integer amount,
        @Param("agreementActCode") String agreementActCode);

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "arc_selectAll", cacheType= CacheType.LOCAL, cacheNullValue = true)
    List<AgreementRouteConfigDO> selectAll();

}