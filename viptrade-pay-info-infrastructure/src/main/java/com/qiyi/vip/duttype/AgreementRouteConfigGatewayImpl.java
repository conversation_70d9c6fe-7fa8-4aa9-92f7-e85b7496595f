package com.qiyi.vip.duttype;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.qiyi.vip.domain.duttype.AgreementRouteConfig;
import com.qiyi.vip.domain.duttype.gateway.AgreementRouteConfigGateway;

@Component
public class AgreementRouteConfigGatewayImpl implements AgreementRouteConfigGateway {

    @Resource
    AgreementRouteConfigMapper agreementRouteConfigMapper;

    @Override
    public AgreementRouteConfig getByPayChannelAndVipType(Integer payChannel, Long sourceVipType, Long vipType, Integer amount, String agreementActCode) {
        List<AgreementRouteConfigDO> allRecords = agreementRouteConfigMapper.selectAll();
        if (CollectionUtils.isEmpty(allRecords)) {
            return null;
        }
        Optional<AgreementRouteConfigDO> routeConfigDOOptional = allRecords.stream()
            .filter(record -> record.getPayChannel().equals(payChannel)
                && Objects.equals(record.getSourceVipType(), sourceVipType)
                && record.getVipType().equals(vipType)
                && record.getAmount().equals(amount)
                && record.getAgreementActCode().equals(agreementActCode)
            ).max(Comparator.comparing(AgreementRouteConfigDO::getId));
        if (!routeConfigDOOptional.isPresent()) {
            return null;
        }
        AgreementRouteConfig agreementRouteConfig = AgreementRouteConfig.of();
        BeanUtils.copyProperties(routeConfigDOOptional.get(), agreementRouteConfig);
        return agreementRouteConfig;
    }
}
