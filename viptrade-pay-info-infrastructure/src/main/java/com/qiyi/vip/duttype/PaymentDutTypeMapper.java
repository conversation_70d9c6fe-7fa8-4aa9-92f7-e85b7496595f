package com.qiyi.vip.duttype;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.SortedSet;

/**
 * <AUTHOR>
 */
@Mapper
public interface PaymentDutTypeMapper {

    int insertSelective(PaymentDutTypeDO record);

    List<PaymentDutTypeDO> getPaymentDutTypeList(Long vipType, Integer amount, String actCode, List<Integer> dutTypes);

    List<PaymentDutTypeDO> getPaymentDutTypesByDutType(Integer dutType);

    List<PaymentDutTypeDO> getDutTypeByViptypeAndAmountExcludeActCode(Long vipType, Integer amount,String partnerId);

    /**
     * 只取一个
     * @param actCode
     * @return
     */
    PaymentDutTypeDO getDutTypeByActCode(String actCode);

    List<PaymentDutTypeDO> getPayTypeByPayChannel(Integer payChannel, Integer vipType);

    List<PaymentDutTypeDO> getDutTypeByPayType(Integer payType);

    SortedSet<Integer> getDutTypeWithPayType(Long payType,
        Long sourceVipType,
        Long vipType,
        Integer amount,
        String actCode,
        String agreementActCode,
        Integer renewPrice,
        String partnerId);

    List<PaymentDutTypeDO> selectByPayType(Long payType, Long sourceVipType, Long vipType, Integer amount, Integer renewPrice);

    SortedSet<Integer> getDutTypeWithChannel(@Param("payChannel") Integer payChannel,
                                             @Param("sourceVipType") Long sourceVipType,
                                             @Param("vipType") Long vipType,
                                             @Param("amount") Integer amount,
                                             @Param("actCode") String actCode,
                                            @Param("agreementActCode") String agreementActCode,
                                             @Param("renewPrice") Integer renewPrice,
                                             @Param("partnerId") String partnerId);

    List<PaymentDutTypeDO> selectByPayChannel(@Param("payChannel") Integer payChannel,
        @Param("sourceVipType") Long sourceVipType,
        @Param("vipType") Long vipType,
        @Param("amount") Integer amount,
        @Param("renewPrice") Integer renewPrice,
        @Param("partnerId") String partnerId);

    List<PaymentDutTypeDO> getDutTypes(Long payType, Integer vipType, Integer amount);

    List<PaymentDutTypeDO> getUniquePaymentDutType(@Param("payType") Integer payType,
                                                   @Param("dutType") Integer dutType,
                                                   @Param("agreementNo") Integer agreementNo,
                                                   @Param("amount") Integer amount,
                                                   @Param("vipType") Long vipType,
                                                   @Param("renewPrice") Integer renewPrice,
                                                   @Param("actCode") String actCode,
                                                   @Param("agreementActCode") String agreementActCode);

    int updatePaymentDutType(PaymentDutTypeDO paymentDutTypeDO);

    List<PaymentDutTypeDO> getDutTypeByAgreementActCode(String agreementActCode);

    List<PaymentDutTypeDO> getDutTypeBySku(@Param("payChannel") Integer payChannel,
                                                        @Param("payType") Integer payType,
                                                        @Param("skuId") String skuId);
}
