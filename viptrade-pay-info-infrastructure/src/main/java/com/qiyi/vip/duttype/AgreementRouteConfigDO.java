package com.qiyi.vip.duttype;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementRouteConfigDO implements Serializable {

    private Integer id;

    private Integer agreementNo;

    private Integer agreementType;

    private Integer payChannel;

    private Integer amount;

    private Long sourceVipType;

    private Long vipType;

    private String agreementActCode;

    private Timestamp validStartTime;

    private Timestamp validEndTime;

    private Timestamp createTime;

    private Timestamp updateTime;

}