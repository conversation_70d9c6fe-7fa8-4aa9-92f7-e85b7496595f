package com.qiyi.vip.duttype;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.qiyi.vip.domain.duttype.AgreementInfo;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.domain.duttype.gateway.PaymentDutTypeGateway;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 11:22 2021/3/8
 */
@Component
public class PaymentDutTypeGatewayImpl implements PaymentDutTypeGateway {
    @Resource
    PaymentDutTypeMapper paymentDutTypeMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_paymentDutTypeList", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentDutType> paymentDutTypeList(Long vipType, Integer amount, String actCode, List<Integer> dutTypes) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getPaymentDutTypeList(vipType, amount, actCode, dutTypes);
        return transfer(paymentDutTypeDOS);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getPaymentDutTypesByDutType", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentDutType> getPaymentDutTypesByDutType(Integer dutType) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getPaymentDutTypesByDutType(dutType);

        return transfer(paymentDutTypeDOS);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getDutTypeByVipTypeAndAmountExcludeActCode", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentDutType> getDutTypeByVipTypeAndAmountExcludeActCode(Long vipType, Integer amount, String partnerId) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getDutTypeByViptypeAndAmountExcludeActCode(vipType, amount, partnerId);
        return transfer(paymentDutTypeDOS);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getDutTypeByActCode", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public PaymentDutType getDutTypeByActCode(String actCode) {
        PaymentDutTypeDO paymentDutTypeDO = paymentDutTypeMapper.getDutTypeByActCode(actCode);
        if (null != paymentDutTypeDO) {
            PaymentDutType paymentDutType = PaymentDutType.of();
            BeanUtils.copyProperties(paymentDutTypeDO, paymentDutType);
            return paymentDutType;
        }
        return null;
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getPayTypeByPayChannel", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentDutType> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getPayTypeByPayChannel(payChannel, vipType);
        return transfer(paymentDutTypeDOS);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getDutTypeByPayType", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentDutType> getDutTypeByPayType(Integer payType) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getDutTypeByPayType(payType);
        return transfer(paymentDutTypeDOS);
    }

    private List<PaymentDutType> transfer(List<PaymentDutTypeDO> paymentDutTypeDOS) {
        List<PaymentDutType> paymentDutTypeList = new ArrayList<>();

        Optional.ofNullable(paymentDutTypeDOS).orElseGet(ArrayList::new).forEach(paymentDutTypeDO -> {
            PaymentDutType paymentDutType = PaymentDutType.of();
            BeanUtils.copyProperties(paymentDutTypeDO, paymentDutType);
            paymentDutTypeList.add(paymentDutType);
        });
        return paymentDutTypeList;
    }

    @Override
    public SortedSet<Integer> getDutTypeWithPayChannel(Integer payChannel,
                                                       Long sourceVipType,
                                                       Long vipType,
                                                       Integer amount,
                                                       String actCode,
                                                       String agreementActCode,
                                                       Integer renewPrice,
                                                       String partnerId) {
        SortedSet dutTypes = paymentDutTypeMapper.getDutTypeWithChannel(
                payChannel, sourceVipType, vipType, amount, actCode, agreementActCode, renewPrice, partnerId);
        if (actCode != null && CollectionUtils.isEmpty(dutTypes)) {
            dutTypes = paymentDutTypeMapper.getDutTypeWithChannel(
                    payChannel, sourceVipType, vipType, amount, null, agreementActCode, renewPrice, partnerId);
        }
        if (agreementActCode != null && CollectionUtils.isEmpty(dutTypes)) {
            dutTypes = paymentDutTypeMapper.getDutTypeWithChannel(
                    payChannel, sourceVipType, vipType, amount, actCode, null, renewPrice, partnerId);
        }
        if (actCode != null && agreementActCode != null && CollectionUtils.isEmpty(dutTypes)) {
            dutTypes = paymentDutTypeMapper.getDutTypeWithChannel(
                    payChannel, sourceVipType, vipType, amount, null, null, renewPrice, partnerId);
        }
        return dutTypes;
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getAgreementInfoWithPayChannel", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<AgreementInfo> getAgreementInfoWithPayChannel(Integer payChannel, Long sourceVipType, Long vipType, Integer amount,
                                                              String actCode, String agreementActCode, Integer renewPrice, String partnerId) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.selectByPayChannel(payChannel, sourceVipType, vipType, amount, renewPrice, partnerId);
        if (CollectionUtils.isEmpty(paymentDutTypeDOS)) {
            return Collections.emptyList();
        }

        //先根据actCode和agreementActCode过滤
        List<PaymentDutTypeDO> filteredList = paymentDutTypeDOS.stream()
                .filter(item -> Objects.equals(item.getActCode(), actCode) && Objects.equals(item.getAgreementActCode(), agreementActCode))
                .collect(Collectors.toList());

        if (actCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.isNull(item.getActCode()) && Objects.equals(item.getAgreementActCode(), agreementActCode))
                    .collect(Collectors.toList());
        }

        if (agreementActCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.equals(item.getActCode(), actCode) && Objects.isNull(item.getAgreementActCode()))
                    .collect(Collectors.toList());
        }

        if (actCode != null && agreementActCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.isNull(item.getActCode()) && Objects.isNull(item.getAgreementActCode()))
                    .collect(Collectors.toList());
        }

        return filteredList.stream()
                .map(item -> new AgreementInfo(item.getDutType(), item.getAgreementNo(), item.getRenewPrice(), item.getType()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public SortedSet<Integer> getDutTypeWithPayType(Long payType,
                                                    Long sourceVipType,
                                                    Long vipType,
                                                    Integer amount,
                                                    String actCode,
                                                    String agreementActCode,
                                                    Integer renewPrice,
                                                    String partnerId) {
        return paymentDutTypeMapper.getDutTypeWithPayType(payType, sourceVipType, vipType, amount, actCode, agreementActCode, renewPrice, partnerId);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getAgreementInfoWithPayType", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<AgreementInfo> getAgreementInfoWithPayType(Long payType, Long sourceVipType, Long vipType, Integer amount,
                                                           String actCode, String agreementActCode, Integer renewPrice, String partnerId) {

        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.selectByPayType(payType, sourceVipType, vipType, amount, renewPrice);
        if (CollectionUtils.isEmpty(paymentDutTypeDOS)) {
            return Collections.emptyList();
        }

        String partnerIdNew = partnerId == null ? "" : partnerId;
        List<PaymentDutTypeDO> filteredList = paymentDutTypeDOS.stream()
                .filter(item -> Objects.equals(item.getActCode(), actCode)
                        && Objects.equals(item.getAgreementActCode(), agreementActCode)
                        && Objects.equals(item.getPartnerId(), partnerIdNew))
                .collect(Collectors.toList());

        if (actCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.isNull(item.getActCode())
                            && Objects.equals(item.getAgreementActCode(), agreementActCode)
                            && Objects.equals(item.getPartnerId(), partnerIdNew))
                    .collect(Collectors.toList());
        }

        if (agreementActCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.equals(item.getActCode(), actCode)
                            && Objects.isNull(item.getAgreementActCode())
                            && Objects.equals(item.getPartnerId(), partnerIdNew))
                    .collect(Collectors.toList());
        }

        if (actCode != null && agreementActCode != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.isNull(item.getActCode())
                            && Objects.isNull(item.getAgreementActCode())
                            && Objects.equals(item.getPartnerId(), partnerIdNew))
                    .collect(Collectors.toList());
        }

        if (partnerId != null && CollectionUtils.isEmpty(filteredList)) {
            filteredList = paymentDutTypeDOS.stream()
                    .filter(item -> Objects.isNull(item.getActCode())
                            && Objects.isNull(item.getAgreementActCode())
                            && Objects.equals(item.getPartnerId(), ""))
                    .collect(Collectors.toList());
        }

        return filteredList.stream()
                .map(item -> new AgreementInfo(item.getDutType(), item.getAgreementNo(), item.getRenewPrice(), item.getType()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<PaymentDutType> getDutTypes(Long payType, Integer vipType, Integer amount) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getDutTypes(payType, vipType, amount);
        return transfer(paymentDutTypeDOS);
    }

    @Override
    public boolean addPaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        PaymentDutTypeDO paymentDutTypeDO = new PaymentDutTypeDO();
        BeanUtils.copyProperties(paymentDutTypeAdminDTO, paymentDutTypeDO);
        paymentDutTypeDO.setValidStartTime(new Timestamp(paymentDutTypeAdminDTO.getValidStartTime()));
        paymentDutTypeDO.setValidEndTime(new Timestamp(paymentDutTypeAdminDTO.getValidEndTime()));
        return paymentDutTypeMapper.insertSelective(paymentDutTypeDO) > 0;
    }

    @Override
    public List<PaymentDutType> getUniquePaymentDutType(Integer payType, Integer dutType, Integer agreementNo, Integer amount, Long vipType, Integer renewPrice, String actCode, String agreementActCode) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getUniquePaymentDutType(payType, dutType, agreementNo, amount, vipType, renewPrice, actCode, agreementActCode);
        return transfer(paymentDutTypeDOS);
    }

    @Override
    public boolean updatePaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        PaymentDutTypeDO paymentDutTypeDO = new PaymentDutTypeDO();
        BeanUtils.copyProperties(paymentDutTypeAdminDTO, paymentDutTypeDO);
        paymentDutTypeDO.setValidStartTime(new Timestamp(paymentDutTypeAdminDTO.getValidStartTime()));
        paymentDutTypeDO.setValidEndTime(new Timestamp(paymentDutTypeAdminDTO.getValidEndTime()));
        return paymentDutTypeMapper.updatePaymentDutType(paymentDutTypeDO) > 0;
    }

    @Override
    public List<PaymentDutType> getDutTypeByAgreementActCode(String agreementActCode) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getDutTypeByAgreementActCode(agreementActCode);
        return transfer(paymentDutTypeDOS);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pdt_getAgreementBySku", cacheType = CacheType.LOCAL, cacheNullValue = true)
    @Override
    public AgreementInfo getAgreementInfo(Integer payChannel, Integer payType, String skuId) {
        List<PaymentDutTypeDO> paymentDutTypeDOS = paymentDutTypeMapper.getDutTypeBySku(payChannel, null, skuId);
        if (CollectionUtils.isNotEmpty(paymentDutTypeDOS) && paymentDutTypeDOS.size() == 1) {
            PaymentDutTypeDO paymentDutTypeDO = paymentDutTypeDOS.get(0);
            return new AgreementInfo(paymentDutTypeDO.getDutType(), paymentDutTypeDO.getAgreementNo(), paymentDutTypeDO.getRenewPrice(), paymentDutTypeDO.getType());
        } else if (CollectionUtils.isNotEmpty(paymentDutTypeDOS) && paymentDutTypeDOS.size() > 1) {
            paymentDutTypeDOS = paymentDutTypeMapper.getDutTypeBySku(null, payType, skuId);
        }
        if (CollectionUtils.isNotEmpty(paymentDutTypeDOS)) {
            PaymentDutTypeDO paymentDutTypeDO = paymentDutTypeDOS.get(0);
            return new AgreementInfo(paymentDutTypeDO.getDutType(), paymentDutTypeDO.getAgreementNo(), paymentDutTypeDO.getRenewPrice(), paymentDutTypeDO.getType());
        }
        return null;
    }
}
