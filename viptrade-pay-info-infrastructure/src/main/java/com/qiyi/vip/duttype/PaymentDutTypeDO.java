package com.qiyi.vip.duttype;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 11:15 2021/3/8
 */
@Data
public class PaymentDutTypeDO {
    /**
     * id.
     */
    protected Long id;

    /**
     * payType.奇悦支付方式编码
     */
    private Integer payType;

    /**
     * dutType.代扣方式编码
     */
    private Integer dutType;

    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * serviceCode
     */
    private String serviceCode;

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    private Integer amount;

    /**
     * 升级自动续费源会员类型
     */
    private Long sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    private Long vipType;

    /**
     * 活动编码（fs值）
     */
    private String actCode;
    /**
     * 协议活动code
     */
    private String agreementActCode;

    /**
     * 签约价，单位为分
     */
    private Integer renewPrice;

    /**
     * 有效开始时间
     */
    private Timestamp validStartTime;

    /**
     * 有效结束时间
     */
    private Timestamp validEndTime;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 支付中心分配的业务方标识
     */
    private String partnerId;

    /**
     * 协议类型，1: 自动续费 2: 芝麻GO 3: 微信支付分 4: 免密
     */
    private Integer type;

    /**
     * skuId
     */
    private String skuId;
}
