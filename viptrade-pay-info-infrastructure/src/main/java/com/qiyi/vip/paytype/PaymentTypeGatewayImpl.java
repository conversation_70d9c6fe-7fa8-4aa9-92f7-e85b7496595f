package com.qiyi.vip.paytype;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.domain.paytype.gateway.PaymentTypeGateway;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import com.qiyi.vip.paytype.assembler.PaymentRouteAssembler;
import com.qiyi.vip.paytype.assembler.PaymentTypeAssembler;

/**
 * <AUTHOR>
 */
@Component
public class PaymentTypeGatewayImpl implements PaymentTypeGateway {
    @Resource
    private PaymentTypeAssembler paymentTypeAssembler;

    @Resource
    private PaymentRouteAssembler paymentRouteAssembler;

    @Resource
    private PaymentTypeMapper paymentTypeMapper;

    @Resource
    private PaymentRouteMapper paymentRouteMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getById", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public PaymentType getById(Long id) {
        PaymentTypeDO paymentTypeDO = paymentTypeMapper.getPaymentTypeById(id);
        if (Objects.nonNull(paymentTypeDO)) {
            return paymentTypeAssembler.toPaymentType(paymentTypeDO);
        } else {
            return null;
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getByIds", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<PaymentType> getByIds(List<Long> payTypes) {
        List<PaymentTypeDO> paymentTypeDOs = paymentTypeMapper.getPaymentTypeByIds(payTypes);
        if (CollectionUtils.isEmpty(paymentTypeDOs)) {
            return Collections.emptyList();
        } else {
            return paymentTypeAssembler.toPaymentTypes(paymentTypeDOs);
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getPasswordFreeSignPayTypesByIds", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<PaymentType> getPasswordFreeSignPayTypesByIds(List<Long> payTypes) {
        List<PaymentTypeDO> paymentTypeDOs = paymentTypeMapper.getPasswordFreeSignPayTypesByIds(payTypes);
        if (CollectionUtils.isEmpty(paymentTypeDOs)) {
            return null;
        } else {
            return paymentTypeAssembler.toPaymentTypes(paymentTypeDOs);
        }
    }

    /**
     * 该实现会对查询结果做再次过滤处理，如此方法加缓存会导致缓存结果不准确为过滤后的结果
     *
     * @param channelId    支付渠道id
     * @param subChannelId 子渠道Id
     * @param version      版本号
     */
    @Override
    public List<PaymentType> getPayTypeByChannel(Integer channelId, Integer subChannelId, String version) {
        List<PaymentTypeDO> paymentTypeDOS = paymentTypeMapper.getPayTypeByChannel(channelId, subChannelId, version);
        if (CollectionUtils.isEmpty(paymentTypeDOS)) {
            return null;
        } else {
            return paymentTypeAssembler.toPaymentTypes(paymentTypeDOS);
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getPayTypeByChannel", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<PaymentType> getPayTypeByChannel(Integer channelId) {
        List<PaymentTypeDO> paymentTypeDOS = paymentTypeMapper.getPayTypeByPayChannel(channelId);
        if (CollectionUtils.isEmpty(paymentTypeDOS)) {
            return Collections.emptyList();
        }
        return paymentTypeAssembler.toPaymentTypes(paymentTypeDOS);
    }

    @Override
    public List<PaymentType> getPayTypeByChannels(List<Integer> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Collections.emptyList();
        }
        PaymentTypeGateway thisObj = (PaymentTypeGateway) AopContext.currentProxy();
        return channelIds.stream()
            .map(thisObj::getPayTypeByChannel)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    @Override
    public boolean add(AddPayTypeDTO addPayTypeDTO) {
        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        BeanUtils.copyProperties(addPayTypeDTO, paymentTypeDO);
        int count = paymentTypeMapper.insert(paymentTypeDO);
        return count > 0;
    }

    @Override
    public Boolean update(UpdatePayTypeDTO updatePayTypeDTO) {
        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        BeanUtils.copyProperties(updatePayTypeDTO, paymentTypeDO);
        int count = paymentTypeMapper.update(paymentTypeDO);
        return count > 0;
    }

    @Override
    public List<PaymentType> getByProperties(QueryPayTypeInfoDTO queryPayTypeInfoDTO) {
        PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
        BeanUtils.copyProperties(queryPayTypeInfoDTO, paymentTypeDO);
        paymentTypeDO.setIsChargeback(queryPayTypeInfoDTO.getChargeback());
        paymentTypeDO.setIsChargeauto(queryPayTypeInfoDTO.getChargeauto());
        List<PaymentTypeDO> paymentTypeDOS = paymentTypeMapper.getPaymentTypeByProperties(paymentTypeDO);
        if (CollectionUtils.isEmpty(paymentTypeDOS)) {
            return null;
        } else {
            return paymentTypeAssembler.toPaymentTypes(paymentTypeDOS);
        }
    }

    @Override
    public List<PaymentType> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryAdminPayTypeInfoDTO) {
        List<PaymentTypeDO> adminPayTypes = paymentTypeMapper.getAdminPayTypesByCondition(queryAdminPayTypeInfoDTO);
        if (CollectionUtils.isEmpty(adminPayTypes)) {
            return Collections.emptyList();
        } else {
            return paymentTypeAssembler.toPaymentTypes(adminPayTypes);
        }
    }

    @Override
    public Integer countAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryAdminPayTypeInfoDTO) {
        return paymentTypeMapper.countAdminPayTypesByCondition(queryAdminPayTypeInfoDTO);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getPaymentRoutesByPlatform", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PaymentRoute> getPaymentRoutesByPlatform(String platform) {
        List<PaymentRouteDO> paymentRoutes = paymentRouteMapper.findByPlatform(platform);
        if (CollectionUtils.isEmpty(paymentRoutes)) {
            return Collections.emptyList();
        } else {
            return paymentRouteAssembler.toPaymentRoutes(paymentRoutes);
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getAll", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<PaymentType> getAll() {
        List<PaymentTypeDO> paymentTypeDOList = paymentTypeMapper.getAll();
        if (CollectionUtils.isEmpty(paymentTypeDOList)) {
            return Collections.emptyList();
        }
        return paymentTypeAssembler.toPaymentTypes(paymentTypeDOList);
    }
}
