package com.qiyi.vip.paytype;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PaymentRoute Mapper接口 定义对PaymentRouteDO进行数据库操作的方法
 */
@Mapper
public interface PaymentRouteMapper {

    /**
     * 根据平台查询支付路由配置
     *
     * @param platform 平台标识
     * @return 匹配的支付路由配置列表
     */
    List<PaymentRouteDO> findByPlatform(@Param("platform") String platform);
}

