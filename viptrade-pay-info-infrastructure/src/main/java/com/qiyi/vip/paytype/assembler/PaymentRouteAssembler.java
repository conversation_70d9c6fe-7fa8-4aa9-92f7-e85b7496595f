package com.qiyi.vip.paytype.assembler;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.paytype.PaymentRouteDO;

@Component
public class PaymentRouteAssembler {


    public List<PaymentRoute> toPaymentRoutes(List<PaymentRouteDO> paymentRoutes) {
        if (CollectionUtils.isNotEmpty(paymentRoutes)) {
            return paymentRoutes.stream().map(PaymentRouteMapper.INSTANCE::toPaymentRoute).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }
}
