package com.qiyi.vip.paytype.assembler;

import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.domain.paytype.PaymentTypeExtends;
import com.qiyi.vip.domain.paytype.PaymentTypeItem;
import com.qiyi.vip.domain.paytype.PaymentTypeTransform;
import com.qiyi.vip.paychannel.PayChannelDO;
import com.qiyi.vip.paychannel.PayChannelMapper;
import com.qiyi.vip.paytype.PaymentTypeDO;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.math.NumberUtils.toLong;


/**
 * <AUTHOR>
 */
@Component
public class PaymentTypeAssembler {

    @Resource
    PayChannelMapper payChannelMapper;

    public PaymentType toPaymentType(PaymentTypeDO paymentTypeDO) {
        PaymentType paymentType = PaymentType.of();
        BeanUtils.copyProperties(paymentTypeDO, paymentType);

        Integer payChannelId = paymentTypeDO.getSubPayChannel() == null ? paymentTypeDO.getPayChannel() : paymentTypeDO.getSubPayChannel();
        PayChannelDO payChannelDO = payChannelMapper.getById(payChannelId);
        PaymentTypeItem paymentTypeItem = PaymentTypeItem.builder()
                .name(paymentTypeDO.getName())
                .description(paymentTypeDO.getDescription())
                .dutAgreementName(payChannelDO.getDutAgreementName())
                .dutAgreementUrl(payChannelDO.getDutAgreementUrl())
                .passwordFreeOpenTips(paymentTypeDO.getPasswordFreeOpenTips())
                .iconUrl(payChannelDO.getIconUrl())
                .build();
        if (Objects.nonNull(paymentTypeDO.getPayTypeExtendsDo())) {
            PaymentTypeExtends payTypeExtend = new PaymentTypeExtends();
            BeanUtils.copyProperties(paymentTypeDO.getPayTypeExtendsDo(), payTypeExtend);
            paymentType.setPaymentTypeExtend(payTypeExtend);
        }
        PaymentTypeTransform paymentTypeTransform = buildPaymentTypeTransform(paymentTypeDO, paymentType);
        paymentType.setPayTypeTransform(paymentTypeTransform);
        paymentType.setPaymentTypeItem(paymentTypeItem);
        return paymentType;
    }


    /**
     * 转换为支付接口对应的参数实休
     */
    public List<PaymentType> toPaymentTypes(List<PaymentTypeDO> paymentTypeDOs) {
        List<PaymentType> paymentTypes = new ArrayList<>(paymentTypeDOs.size());
        Map<Long, PayChannelDO> payChannelDOMap = payChannelMapper.findAll().stream().collect(Collectors.toMap(PayChannelDO::getId, o -> o, (p1, p2) -> p2));

        for (PaymentTypeDO paymentTypeDO : paymentTypeDOs) {
            PaymentType paymentType = PaymentType.of();
            BeanUtils.copyProperties(paymentTypeDO, paymentType);

            Integer payChannelId = paymentTypeDO.getSubPayChannel() == null ? paymentTypeDO.getPayChannel() : paymentTypeDO.getSubPayChannel();
            PayChannelDO payChannelDO = payChannelDOMap.get(payChannelId.longValue());

            PaymentTypeItem paymentTypeItem = PaymentTypeItem.builder()
                    .name(paymentTypeDO.getName())
                    .description(paymentTypeDO.getDescription())
                    .dutAgreementName(payChannelDO == null ? null : payChannelDO.getDutAgreementName())
                    .dutAgreementUrl(payChannelDO == null ? null : payChannelDO.getDutAgreementUrl())
                    .passwordFreeOpenTips(paymentTypeDO.getPasswordFreeOpenTips())
                    .iconUrl(payChannelDO == null ? null : payChannelDO.getIconUrl())
                    .status(paymentTypeDO.getStatus())
                    .build();
            PaymentTypeTransform paymentTypeTransform = buildPaymentTypeTransform(paymentTypeDO, paymentType);
            paymentType.setPayTypeTransform(paymentTypeTransform);
            paymentType.setPaymentTypeItem(paymentTypeItem);

            if (Objects.nonNull(paymentTypeDO.getPayTypeExtendsDo())) {
                PaymentTypeExtends payTypeExtend = new PaymentTypeExtends();
                BeanUtils.copyProperties(paymentTypeDO.getPayTypeExtendsDo(), payTypeExtend);
                paymentType.setPaymentTypeExtend(payTypeExtend);
            }

            if (payChannelDO != null) {
                PayChannel payChannel = PayChannel.of();
                BeanUtils.copyProperties(payChannelDO, payChannel);
                paymentType.setPayChannelItem(payChannel);
            }

            paymentTypes.add(paymentType);
        }

        return paymentTypes;
    }

    private PaymentTypeTransform buildPaymentTypeTransform(PaymentTypeDO paymentTypeDO, PaymentType paymentType) {
        Map<String, String> properties = paymentType.getPropertiesMap();
        Boolean toPasswordFree = null;
        String passwordFreeType = null;
        String passwordFreeDutType = null;
        Integer passwordFreeAgreementNo = null;
        Boolean toH5 = null;
        String passwordFreeCommonPayType = null;
        Long h5PayType = null;
        if (!properties.isEmpty()) {
            toPasswordFree = Boolean.parseBoolean(properties.get(PaymentType.PAYMENT_TYPE_PROP_TO_PASSWORD_FREE));
            passwordFreeType = properties.get(PaymentType.PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_TYPE);
            passwordFreeDutType = properties.get(PaymentType.PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_DUT_TYPE);
            passwordFreeAgreementNo = NumberUtils.createInteger(properties.get(PaymentType.PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_AGREEMENT_NO));
            toH5 = Boolean.parseBoolean(properties.get(PaymentType.PAYMENT_TYPE_PROP_TO_H5));
            if (Objects.nonNull(properties.get(PaymentType.PAYMENT_TYPE_PROP_H5_TYPE))) {
                h5PayType = toLong(properties.get(PaymentType.PAYMENT_TYPE_PROP_H5_TYPE));
            }
            passwordFreeCommonPayType = properties.get(PaymentType.PASSWORD_FREE_COMMON_PAY_TYPE);
        }

        return PaymentTypeTransform.builder()
                .basicPayTypeId(paymentTypeDO.getBasicPayTypeId())
                .pureSigningPayTypeId(paymentTypeDO.getPureSigningPayTypeId())
                .passwordFreePayType(paymentTypeDO.getPasswordFreePayType())
                .signPayTypeId(paymentTypeDO.getSignPayTypeId())
                .isSupportSign(paymentTypeDO.getIsSupportSign())
                .isSupportPureSign(paymentTypeDO.getIsSupportPureSign())
                .isSupportPasswordFreeSign(paymentTypeDO.getIsSupportPasswordFreeSign())
                .toPasswordFree(toPasswordFree)
                .passwordFreeDutType(passwordFreeDutType)
                .passwordFreeAgreementNo(passwordFreeAgreementNo)
                .passwordFreeCommonPayType(passwordFreeCommonPayType)
                .passwordFreeType(passwordFreeType)
                .toH5(toH5)
                .h5PayType(h5PayType).build();
    }
}
