package com.qiyi.vip.paytype;


import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;

/**
 * <AUTHOR>
 */
@Mapper
public interface PaymentTypeMapper {

    PaymentTypeDO getPaymentTypeById(Long payType);

    List<PaymentTypeDO> getPaymentTypeByIds(@Param("payTypes") List<Long> payTypes);

    List<PaymentTypeDO> getPasswordFreeSignPayTypesByIds(@Param("payTypes") List<Long> payTypes);

    int insert(PaymentTypeDO record);

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pt_getPayTypeByChannel_mapper", cacheType= CacheType.LOCAL, cacheNullValue=true)
    List<PaymentTypeDO> getPayTypeByChannel(Integer channelId, Integer subChannelId, String version);

    List<PaymentTypeDO> getPayTypeByPayChannel(Integer channelId);

    int update(PaymentTypeDO paymentTypeDO);

    List<PaymentTypeDO> getPaymentTypeByProperties(PaymentTypeDO paymentTypeDO);

    List<PaymentTypeDO> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryInfo);

    Integer countAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryInfo);

    List<PaymentTypeDO> getAll();
}
