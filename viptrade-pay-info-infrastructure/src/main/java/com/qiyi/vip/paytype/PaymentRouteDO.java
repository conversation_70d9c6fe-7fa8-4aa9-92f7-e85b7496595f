package com.qiyi.vip.paytype;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 支付路由配置实体类
 * 该类用于定义不同支付场景下的路由规则，以实现灵活的支付流程控制
 * 实现了Serializable接口，以支持对象的序列化和反序列化
 */
@Data
public class PaymentRouteDO implements Serializable {

    /**
     * 主键ID
     * 用于唯一标识每条支付路由配置
     */
    private Long id;

    /**
     * 平台标识
     * 表示该路由配置适用的平台，如Web、iOS、Android等
     */
    private String platform;

    /**
     * 起始版本号
     * 定义该路由配置生效的最低版本
     */
    private String versionFrom;

    /**
     * 终止版本号
     * 定义该路由配置生效的最高版本
     */
    private String versionTo;

    /**
     * 支付类型
     * 用于指定该路由配置适用的支付类型，如货到付款、在线支付等
     */
    private Long payType;

    /**
     * 支付渠道
     * 表示该路由配置适用的支付渠道，如支付宝、微信支付等
     */
    private Integer payChannel;

    /**
     * A/B测试分组
     * 用于区分不同A/B测试组的路由配置
     */
    private String abTestGroup;

    /**
     * 扩展条件
     * 保存额外的条件信息，用于更复杂的路由逻辑判断
     */
    private String conditionExt;

    /**
     * 状态
     * 用于标识支付路由的当前状态，例如：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 创建时间
     * 记录该路由配置创建的时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     * 记录该路由配置最后一次更新的时间
     */
    private Timestamp updateTime;

    /**
     * 操作者
     * 记录最后一次修改该路由配置的用户信息
     */
    private String operator;
}

