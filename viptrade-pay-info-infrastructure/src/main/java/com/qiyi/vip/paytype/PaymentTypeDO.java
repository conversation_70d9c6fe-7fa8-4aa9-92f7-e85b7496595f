package com.qiyi.vip.paytype;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 支付方式域定义
 * @date Create in 17:47 2021/3/5
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentTypeDO implements Serializable {

    /**
     * id
     */
    protected Long id;

    /**
     * 是否支持退单
     */
    private Integer isChargeback;
    /**
     * 是否支持自动退款
     */
    private Integer isChargeauto;
    /**
     * 是否支持后台方式退款
     */
    private Integer isBackground;
    /**
     * 表明该支付方式是否支持签约支付，即自动续费
     */
    private Boolean isSupportSign = false;
    /**
     * 是否支持免密支付签约
     */
    private Boolean isSupportPasswordFreeSign = false;

    /**
     * 是否支持纯签约支付
     */
    private Boolean isSupportPureSign;

    /**
     * 支付方式状态，0 ： 已下线 1：正常
     */
    private Integer status;

    /**
     * 支付方式对应的支付中心编码
     */
    private String payCenterCode;
    /**
     * 类别,1:在线购买,2:手机支付,3:OTT,4:其他
     */
    private Integer type;

    /**
     * 如果是签约支付，则对应同类型的基本支付
     */
    private Long basicPayTypeId;

    /**
     * 如果是普通支付，则对应同类型的签约支付
     */
    private Long signPayTypeId;

    /**
     * 免密支付的支付方式
     */
    private Long passwordFreePayType;
    /**
     * 所对应的纯签约支付方式
     **/
    private Long pureSigningPayTypeId;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 子支付渠道
     */
    private Integer subPayChannel;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 免密支付弹窗上描述文案
     */
    private String passwordFreeOpenTips;


    /**
     * 渠道方支持的最大退款时间，以天为单位
     */
    private Integer refundExpireOffset;

    /**
     * 支付方式属性拓展字段
     */
    private String properties;
    /**
     * 修改时间
     */
    private Timestamp updateTime;

    private PaymentTypeExtendsDO payTypeExtendsDo;

    /**
     * 申请人
     */
    private String operator;
}
