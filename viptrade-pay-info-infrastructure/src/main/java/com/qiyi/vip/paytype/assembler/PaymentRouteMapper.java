package com.qiyi.vip.paytype.assembler;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.paytype.PaymentRouteDO;

/**
 * 支付方式路由映射器
 *
 * <AUTHOR>
 */
@Mapper
public interface PaymentRouteMapper {

    PaymentRouteMapper INSTANCE = Mappers.getMapper(PaymentRouteMapper.class);

    PaymentRoute toPaymentRoute(PaymentRouteDO paymentRouteDO);

}
