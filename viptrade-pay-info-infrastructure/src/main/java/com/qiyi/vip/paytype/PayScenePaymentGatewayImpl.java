package com.qiyi.vip.paytype;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.qiyi.vip.domain.paytype.PayScenePayment;
import com.qiyi.vip.domain.paytype.gateway.PayScenePaymentGateway;

/**
 * @auther: guojing
 * @date: 2023/8/30 15:27
 */
@Component
public class PayScenePaymentGatewayImpl implements PayScenePaymentGateway {

    @Resource
    private PayScenePaymentMapper payScenePaymentMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "PayScenePayment_getBySceneCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<PayScenePayment> getBySceneCode(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return Collections.emptyList();
        }
        List<PayScenePaymentDO> payScenePaymentDOS = payScenePaymentMapper.selectBySceneCode(sceneCode);
        return payScenePaymentDOS.stream().map(payScenePaymentDO -> {
            PayScenePayment payScenePayment = PayScenePayment.of();
            BeanUtils.copyProperties(payScenePaymentDO, payScenePayment);
            return payScenePayment;
        }).collect(Collectors.toList());
    }

}
