package com.qiyi.vip.paytype;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class PayScenePaymentDO {

    private Integer id;

    /**
     * 支付场景code
     */
    private String sceneCode;
    /**
     * 支付场景名称
     */
    private String sceneName;
    /**
     * 支付环境
     */
    private String sceneEnv;
    /**
     * 拉起支付场景：APP、JSAPI、H5、MINI_PROGRAM
     */
    private String scenario;

    private Integer payChannel;
    /**
     * 普通支付方式
     */
    private Integer basicPayType;
    /**
     * 签约支付方式
     */
    private Integer signPayType;

    private Integer status;


    private Timestamp createTime;

    private Timestamp updateTime;

}