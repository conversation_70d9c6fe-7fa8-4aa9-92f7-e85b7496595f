package com.qiyi.vip.config;

import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * @auther: guojing
 * @date: 2023/2/9 3:52 PM
 * @description:
 */
@Component("customKeyGenerator")
public class CustomKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        return method.getName() + "#" + StringUtils.arrayToDelimitedString(params, "#");
    }

}
