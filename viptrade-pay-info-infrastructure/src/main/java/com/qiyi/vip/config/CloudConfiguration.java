package com.qiyi.vip.config;

import com.ctrip.framework.apollo.core.ConfigRegion;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.CloudConfigListener;
import com.iqiyi.solar.config.client.CloudConfigService;
import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;


@Configuration
@EnableCloudConfig
public class CloudConfiguration {

    @Value("${application.name}")
    private String appName;
    @Value("${application.env}")
    private String appEnv;
    @Value("${application.region}")
    private String appRegion;

    @Resource(name = "signKeyListener")
    private CloudConfigListener signKeyListener;

    @Bean
    public CloudConfig cloudConfig() {
        CloudConfig config = CloudConfigService.builder()
                .withConfigRegion(ConfigRegion.fromRegion(StringUtils.defaultString(appRegion, ConfigRegion.DEFAULT.getRegion())))
                .withAppID(appName)
                .withEnv(appEnv)
                .build();
        config.addChangeListener(signKeyListener);
        return config;
    }
}
