package com.qiyi.vip.config;

import com.iqiyi.solar.config.client.CloudConfig;
import com.qiyi.vip.domain.cloudconfig.CloudConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Component
public class CloudConfigImpl implements CloudConfigService {

    @Resource
    CloudConfig cloudConfig;
    @Override
    public String getProperty(String key, String defaultValue) {
        return cloudConfig.getProperty(key, defaultValue);
    }
}
