package com.qiyi.vip.config;

import org.apache.commons.codec.digest.MessageDigestAlgorithms;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

import com.iqiyi.kit.http.client.spring.ApacheRestTemplateFactoryBean;

import static com.qiyi.vip.util.ApacheRestTemplateFactoryBeanUtil.initRestTemplateFactoryBean;


/**
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {

    @Value("${basic.data.signKey}")
    private String basicDataSignKey;

    @Bean(name = "userTagClient")
    public ApacheRestTemplateFactoryBean userTagClient() {
        return initRestTemplateFactoryBean(500, 1000, 10);
    }

    @Bean(name = "promotionClient")
    public ApacheRestTemplateFactoryBean promotionClient() {
        return initRestTemplateFactoryBean(100, 200, 10);
    }

    @Bean(name = "queryHttpRestTemplate300")
    public ApacheRestTemplateFactoryBean bossClient() {
        return initRestTemplateFactoryBean(100, 150, 10);
    }

    @Bean(name = "accountClient")
    public ApacheRestTemplateFactoryBean accountClient() {
        return initRestTemplateFactoryBean(100, 1000, 50);
    }

    @Bean(name = "payCenterPwdFreeClient")
    public ApacheRestTemplateFactoryBean payCenterPwdFreeClient() {
        return initRestTemplateFactoryBean(500, 1000, 50);
    }

    @Bean(name = "autoRenewClient")
    public ApacheRestTemplateFactoryBean autoRenewClient() {
        return initRestTemplateFactoryBean(100, 1000, 50);
    }

    @Lazy(false)
    @LoadBalanced
    @Bean(name = "commodityCenterClient")
    public ApacheRestTemplateFactoryBean commodityCenterClient() {
        return initRestTemplateFactoryBean(100, 1000, 50);
    }

    @Lazy(false)
    @LoadBalanced
    @Bean(name = "basicDataClient")
    public RestTemplate basicDataClient() throws Exception {
        ApacheRestTemplateFactoryBean factoryBean = initRestTemplateFactoryBean(100, 1000, 50);
        RestTemplate restTemplate = factoryBean.getObject();
        restTemplate.getInterceptors().add(new OuterServiceInvokeSignatureInterceptor("viptrade-pay-info", basicDataSignKey, MessageDigestAlgorithms.MD5));
        return restTemplate;
    }

}