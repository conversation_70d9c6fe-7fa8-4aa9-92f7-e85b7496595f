package com.qiyi.vip.platform;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 支付平台实体类 该类用于定义支付平台的基本信息 实现了Serializable接口，以支持对象的序列化和反序列化
 */
@Data
public class PaymentPlatformDO implements Serializable {

    /**
     * 主键ID 用于唯一标识每个支付平台
     */
    private Long id;

    /**
     * 支付平台code 用于标识支付平台的唯一编码
     */
    private String code;

    /**
     * 描述信息 用于补充支付平台的相关说明
     */
    private String description;

    /**
     * 创建时间 记录支付平台创建的时间
     */
    private Timestamp createTime;

    /**
     * 更新时间 记录支付平台最后一次更新的时间
     */
    private Timestamp updateTime;
}
