package com.qiyi.vip.platform;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 支付平台版本实体类 该类用于定义支付平台的版本信息 实现了Serializable接口，以支持对象的序列化和反序列化
 */
@Data
public class PaymentPlatformVersionDO implements Serializable {

    /**
     * 主键ID 用于唯一标识每个支付平台版本记录
     */
    private Long id;

    /**
     * 平台 表示支付平台的名称或标识
     */
    private String platform;

    /**
     * 版本 表示支付平台的版本号
     */
    private String version;

    /**
     * 创建时间 记录支付平台版本记录的创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间 记录支付平台版本记录最后一次更新的时间
     */
    private Timestamp updateTime;
}
