package com.qiyi.vip.remote;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.qiyi.vip.commons.component.SimpleHystrixCommand;
import com.qiyi.vip.commons.util.SignUtil;
import com.qiyi.vip.domain.remote.PayCenterGateway;
import com.iqiyi.kit.http.client.ResponseObject;

/**
 *
 */
@Slf4j
@Component
public class PayCenterGatewayImpl implements PayCenterGateway {

    private static final String PARTNER = "qiyue";

    @Value("${payCenter.sign.key}")
    private String signKey;

    @Value("${paycenter.pwdFree.queryUrl:http://inter.account.qiyi.domain/account/dut/pwdFreePayQuery.action}")
    private String pwdFreePayQueryUrl;

    @Resource
    private RestTemplate payCenterPwdFreeClient;


    private UserPwdFreeSignInfo queryUserPwdFreeSignInfo(String userId) {
        try {
            return new QueryUserPwdFreeSignInfoCommand(userId).execute();
        } catch (Exception e) {
            log.error("queryUserPwdFreeSignInfo exception", e);
            return null;
        }
    }

    @Override
    public Boolean hasOpenPasswordFree(Long userId, String passwordFreeCommonType) {
        UserPwdFreeSignInfo userPwdFreeSignInfo = queryUserPwdFreeSignInfo(String.valueOf(userId));
        if (userPwdFreeSignInfo == null) {
            return false;
        }
        return userPwdFreeSignInfo.hasSign() && userPwdFreeSignInfo.getSignPayType().contains(passwordFreeCommonType);
    }

    private class QueryUserPwdFreeSignInfoCommand extends SimpleHystrixCommand<UserPwdFreeSignInfo> {

        String userId;

        QueryUserPwdFreeSignInfoCommand(String userId) {
            super("QueryUserPwdFreeSignInfoCommand", 20, 500);
            this.userId = userId;
        }

        @Override
        public UserPwdFreeSignInfo run() {
            Map<String, String> parameters = new HashMap<>();
            parameters.put("partner", PARTNER);
            parameters.put("uid", userId);
            parameters.values().removeAll(Collections.singleton(null));
            parameters.put("sign", doSign(parameters));
            String url = pwdFreePayQueryUrl + "?" + Joiner.on("&").withKeyValueSeparator("=").join(parameters);
            log.info("QueryUserPwdFreeSignInfoCommand url:{}", url);
            ResponseEntity<ResponseObject<List<PayPwdFreeSignInfo>>> responseEntity = payCenterPwdFreeClient.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<ResponseObject<List<PayPwdFreeSignInfo>>>() {
            });
            log.info("QueryUserPwdFreeSignInfoCommand response:{}", responseEntity.getBody());
            ResponseObject<List<PayPwdFreeSignInfo>> webResult = responseEntity.getBody();
            if (webResult == null) {
                return null;
            }
            if (!"A00000".equals(webResult.getCode()) || Objects.isNull(webResult.getData())) {
                return null;
            }
            List<PayPwdFreeSignInfo> payPwdFreeSignInfos = webResult.getData();
            UserPwdFreeSignInfo userSignInfo = new UserPwdFreeSignInfo(userId);
            Set<String> userSignedPayType = payPwdFreeSignInfos.stream().filter(PayPwdFreeSignInfo::getHasSign)
                .map(PayPwdFreeSignInfo::getCommonPayType).collect(Collectors.toSet());
            userSignInfo.setSignPayType(userSignedPayType);
            return userSignInfo;
        }

        @Override
        protected UserPwdFreeSignInfo getFallback() {
            log.error("QueryUserPwdFreeSignInfoCommand fallback, userId:{}", userId, getExecutionException());
            return null;
        }
    }

    private static final Set<String> SKIPPED_SIGN_PARAMS = Sets.newHashSet("sign", "sign_type", "ip", "cip");

    /**
     * 调用参数签名
     */
    private String doSign(Map<String, String> parameters) {
        // 过滤为空的参数
        Map<String, String> filteredParams = Maps.filterEntries(parameters, paramEntry ->
            !SKIPPED_SIGN_PARAMS.contains(paramEntry.getKey().toLowerCase()) && StringUtils.isNotBlank(paramEntry.getValue()));
        return SignUtil.getSign(filteredParams, signKey);
    }
}
