package com.qiyi.vip.remote;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.qiyi.vip.commons.util.SignUtil;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.CommodityCenterGateway;
import com.qiyi.vip.dto.SingleResponse;

/**
 * 商品中心接口文档：https://iq.feishu.cn/wiki/KfPCwadlRi1B5nkzOEGcQ1YlnEa
 * @author: guojing
 * @date: 2024/6/24 10:44
 */
@Slf4j
@Component
public class CommodityCenterGatewayImpl implements CommodityCenterGateway {

    private static final String CALLER = "viptrade-pay-info";

    private static final String QUERY_URL = "/vip-commodity/sku/query";
    private static final String BATCH_QUERY_URL = "/vip-commodity/sku/batchQuery";

    @Value("${commodity.center.sign.key}")
    private String signKey;

    @Value("${commodity.center.domain}")
    private String commodityCenterDomain;

    @Resource
    private RestTemplate commodityCenterClient;


    @CacheRefresh(refresh = 5 * 60, stopRefreshAfterLastAccess = 5 * 60)
    @Cached(cacheType = CacheType.LOCAL, expire = 30 * 60)
    @Override
    public Commodity query(String skuId) {
        Map<String, Commodity> skuIdToCommodityMap = batchQuery(skuId);
        return MapUtils.isNotEmpty(skuIdToCommodityMap) ? skuIdToCommodityMap.get(skuId) : null;
    }

    @Override
    public Map<String, Commodity> batchQuery(String skuIds) {
        if (StringUtils.isBlank(skuIds)) {
            return Collections.emptyMap();
        }
        Map<String, String> paramMap = new HashMap<>(8);
        paramMap.put("skuIds", skuIds);
        paramMap.put("caller", CALLER);
        paramMap.put("validFlag", "false");
        String sign = SignUtil.getSign(paramMap, signKey);
        paramMap.put("sign", sign);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = JSON.toJSONString(paramMap);
        HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<SingleResponse<Map<String, Commodity>>> responseEntity = commodityCenterClient.exchange(
            commodityCenterDomain + BATCH_QUERY_URL, HttpMethod.POST, httpEntity,
            new ParameterizedTypeReference<SingleResponse<Map<String, Commodity>>>() {});
        SingleResponse<Map<String, Commodity>> responseBody = responseEntity.getBody();
        if (!HttpStatus.OK.equals(responseEntity.getStatusCode()) || responseBody == null || responseBody.returnFailed()) {
            log.error("batchQuery error, skuIds:{}, response:{}", skuIds, responseBody.toString());
            return Collections.emptyMap();
        }
        return responseBody.getData();
    }
}
