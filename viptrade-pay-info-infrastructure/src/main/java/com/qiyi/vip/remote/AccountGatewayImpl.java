package com.qiyi.vip.remote;

import java.util.Optional;

import javax.annotation.Resource;

import com.qiyi.vip.commons.component.AccountApi;
import com.qiyi.vip.commons.component.dto.AccountBindInfo;
import com.qiyi.vip.domain.remote.AccountGateway;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 15:59 2021/3/10
 */
@Component
public class AccountGatewayImpl implements AccountGateway {

    @Resource
    private AccountApi accountApi;

    /**
     * 查询账户的签约关系
     *
     * @param userId  用户id
     * @param dutType
     */
    @Override
    public Optional<String> getContractNo(Long userId, Integer dutType) {
        Optional<AccountBindInfo> accountBindInfo = accountApi.queryAccountBindInfo(userId, dutType);
        if (!accountBindInfo.isPresent() || 1 != accountBindInfo.get().getStatus()) {
            //无签约signCode
            return Optional.empty();
        } else {
            return Optional.of(accountBindInfo.get().getSignCode());
        }
    }

    /**
     * 是否已签约
     */
    @Override
    public Boolean isAccountBind(Long userId, Integer dutType) {
        return accountApi.isBind(userId, dutType);
    }
}
