package com.qiyi.vip.remote;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import com.qiyi.vip.domain.remote.BasicDataGateway;
import com.qiyi.vip.domain.remote.VipType;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.exception.BizException;

/**
 * @author: guojing
 * @date: 2024/6/24 18:07
 */
@Slf4j
@Component
public class BasicDataGatewayImpl implements BasicDataGateway {

    @Value("${basic.data.url:http://VIP-BASIC-DATA-ONLINE/basic-data}")
    private String basicDataUrl;

    @Resource
    private RestTemplate basicDataClient;

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(cacheType = CacheType.LOCAL, name = "VipType_getById", expire = 1800)
    @Override
    public VipType getVipTypeById(Long id) {
        String queryUrl = basicDataUrl + "/viptype/queryById?id={id}";
        ResponseEntity<SingleResponse<VipType>> responseEntity = basicDataClient.exchange(queryUrl,
            HttpMethod.GET, null, new ParameterizedTypeReference<SingleResponse<VipType>>() {}, id);
        return getDataFromWebResult(responseEntity);
    }

    private <T> T getDataFromWebResult(ResponseEntity<SingleResponse<T>> responseEntity) {
        if (responseEntity.getBody() == null) {
            log.error("Get Basic data failed. body is empty");
            throw new BizException("QUERY_BASIC_DATA_FAILED", "basic data empty");
        }
        SingleResponse<T> result = responseEntity.getBody();
        if (!"A00000".equals(result.getCode())) {
            log.error("Get Basic data failed. code illegal, code:{}", result.getCode());
            throw new BizException("QUERY_BASIC_DATA_FAILED", "basic data failed");
        }
        return result.getData();
    }

}
