package com.qiyi.vip.remote;

import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

@Data
public class UserPwdFreeSignInfo {

    /**
     * userId
     */
    private String userId;

    /**
     * 已签约的支付列表
     */
    private Set<String> signPayType;

    public UserPwdFreeSignInfo(String userId) {
        this.userId = userId;
        this.signPayType = Sets.newHashSet();
    }

    public boolean hasSign() {
        return CollectionUtils.isNotEmpty(signPayType);
    }
}
