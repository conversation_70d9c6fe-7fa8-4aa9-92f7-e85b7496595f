package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 协议模板价格信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementTempPriceDO {
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 协议编号
     */
    @Deprecated
    private Integer agreementNo;
    /**
     * 协议模板code
     */
    private String agreementCode;
    /**
     * 支付渠道
     */
    @Deprecated
    private Integer payChannel;
    /**
     * 第*期
     */
    private Integer periodNo;
    /**
     * 扣费金额，单位：分
     */
    private Integer price;
    /**
     * 原价，单位：分
     */
    private Integer originalPrice;
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;

}