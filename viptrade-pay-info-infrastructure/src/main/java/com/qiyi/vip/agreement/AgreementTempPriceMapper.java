package com.qiyi.vip.agreement;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgreementTempPriceMapper {

    int insert(AgreementTempPriceDO record);

    int updateByPrimaryKey(AgreementTempPriceDO record);

    int updateByAgreementNo(AgreementTempPriceDO record);

    AgreementTempPriceDO selectByPrimaryKey(Integer id);

    List<AgreementTempPriceDO> selectByAgreementNo(@Param("agreementNo") Integer agreementNo);

    AgreementTempPriceDO selectByTemplateCode(String templateCode);

}