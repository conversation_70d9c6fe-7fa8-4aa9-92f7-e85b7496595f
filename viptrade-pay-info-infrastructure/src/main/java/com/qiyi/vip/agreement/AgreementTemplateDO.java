package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 协议基础信息模板
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementTemplateDO {

    private Integer id;
    /**
     * 协议模板code
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 协议模板类型
     * @see com.qiyi.vip.enums.AgreementTypeEnum
     */
    private Integer type;
    /**
     * 原会员类型，升级前会员类型
     */
    private Long sourceVipType;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 签约产品code
     */
    private String pid;
    /**
     * 签约产品skuId
     */
    private String skuId;
    /**
     * 完结订单产品code
     */
    private String completeOrderPid;
    /**
     * 完结订单产品skuId
     */
    private String completeOrderSkuId;
    /**
     * 签约产品时长
     */
    private Integer amount;
    /**
     * 周期类型
     */
    private Integer periodType;
    /**
     * 期数
     */
    private Integer periods;
    /**
     * 促销类型：0:正价;1:首X期优惠
     */
    private Integer discountType;
    /**
     * 优惠期数
     */
    private Integer discountPeriods;
    /**
     * 每期持续时长
     */
    private Integer periodDuration;
    /**
     * 每期的时间单位
     */
    private Integer periodUnit;
    /**
     * 定价策略
     */
    private Integer pricingStrategy;
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 扩展信息
     */
    private String attributes;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;

}