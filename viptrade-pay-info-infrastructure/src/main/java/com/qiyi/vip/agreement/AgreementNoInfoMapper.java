package com.qiyi.vip.agreement;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.qiyi.vip.domain.agreement.AgreementNoInfo;

@Mapper
public interface AgreementNoInfoMapper {

    int insert(AgreementNoInfoDO record);

    int updateByPrimaryKey(AgreementNoInfoDO record);

    AgreementNoInfoDO selectByPrimaryKey(Integer id);

    List<AgreementNoInfoDO> selectByDutType(Integer dutType);

    Integer getDefaultAgreementNoByDutType(@Param("dutType") Integer dutType, @Param("amount") Integer amount, @Param("vipType") Long vipType);

    List<AgreementNoInfoDO> getAgreementListByDutType(Integer dutType);

    List<AgreementNoInfoDO> getAgreementNoInfosByType(int type);

    AgreementNoInfoDO selectMaxPriorityBy(@Param("templateCode") String templateCode, @Param("payChannel") Integer payChannel, @Param("partnerId") String partnerId, @Param("dutType") Integer dutType);

    List<AgreementNoInfoDO> selectByTemplateCode(String templateCode);

    List<String> getMaxPriorityTemplateCodeByVipType(@Param("type") Integer type, @Param("vipType") Long vipType, @Param("defaultNo") Integer defaultNo);

}