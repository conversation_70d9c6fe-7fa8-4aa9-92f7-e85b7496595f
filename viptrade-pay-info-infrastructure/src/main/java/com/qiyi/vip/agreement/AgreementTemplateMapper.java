package com.qiyi.vip.agreement;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgreementTemplateMapper {

    int insert(AgreementTemplateDO record);

    int updateByCode(AgreementTemplateDO record);

    AgreementTemplateDO selectByCode(String code);

    List<AgreementTemplateDO> batchGetByCode(List<String> codes);

    List<AgreementTemplateDO> getDefaultBy(@Param("agreementType") Integer agreementType
        , @Param("sourceVipType") Long sourceVipType, @Param("vipType") Long vipType, @Param("amount") Integer amount);

}