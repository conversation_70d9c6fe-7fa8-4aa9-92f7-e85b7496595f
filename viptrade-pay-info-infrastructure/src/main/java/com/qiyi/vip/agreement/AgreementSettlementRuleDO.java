package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementSettlementRuleDO {

    private Integer id;

    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 结算产品code
     */
    private String pid;
    /**
     * 豁免期，单位：天
     */
    private Integer exemptionPeriod;
    /**
     * 承诺任务期数
     */
    private Integer promisePeriods;
    /**
     * 承诺任务期内结算策略,1:固定金额,2:补差价
     */
    private Integer withinPeriodSettlementStrategy;
    /**
     * 承诺任务期内固定结算金额,单位:分
     */
    private Integer withinPeriodSettlementFee;
    /**
     * 承诺任务期外结算策略,1:固定金额
     */
    private Integer withoutPeriodSettlementStrategy;
    /**
     * 承诺任务期外固定结算金额,单位:分
     */
    private Integer withoutPeriodSettlementFee;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}