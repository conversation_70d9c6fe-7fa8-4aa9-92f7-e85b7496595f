package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 代扣营销活动
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementDutMktDO {

    private Integer id;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 活动类型,1:首X期每期Y元
     */
    private Integer type;
    /**
     * 活动期数
     */
    private Integer periods;
    /**
     * 代扣活动产品code
     */
    private String pid;
    /**
     * 代扣时长
     */
    private Integer amount;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}