package com.qiyi.vip.agreement;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.qiyi.vip.domain.agreement.AgreementMaterial;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.agreement.AgreementTempPrice;
import com.qiyi.vip.domain.agreement.AgreementTemplate;
import com.qiyi.vip.domain.agreement.AutoRenewDutType;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.duttype.AutoRenewDutTypeDO;
import com.qiyi.vip.duttype.AutoRenewDutTypeMapper;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @auther: guojing
 * @date: 2023/2/9 3:48 PM
 * @description:
 */
@Component
public class AgreementGatewayImpl implements AgreementGateway {

    @Resource
    AgreementNoInfoMapper agreementNoInfoMapper;
    @Resource
    AgreementTempPriceMapper agreementTempPriceMapper;
    @Resource
    AgreementMaterialMapper materialMapper;
    @Resource
    private AutoRenewDutTypeMapper autoRenewDutTypeMapper;
    @Resource
    private AgreementTemplateMapper agreementTemplateMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getAgreementNoInfo", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public AgreementNoInfo getAgreementNoInfo(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        AgreementNoInfoDO agreementNoInfoDO = agreementNoInfoMapper.selectByPrimaryKey(agreementNo);
        if (agreementNoInfoDO == null) {
            return null;
        }
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of();
        BeanUtils.copyProperties(agreementNoInfoDO, agreementNoInfo);
        return agreementNoInfo;
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getAgreementNoInfosByTemplateCode", cacheType= CacheType.LOCAL)
    public List<AgreementNoInfo> getAgreementNoInfosByTemplateCode(String templateCode) {
        if (StringUtils.isEmpty(templateCode)) {
            return Collections.emptyList();
        }
        List<AgreementNoInfoDO> agreementNoInfoDOS = agreementNoInfoMapper.selectByTemplateCode(templateCode);
        if (CollectionUtils.isEmpty(agreementNoInfoDOS)) {
            return Collections.emptyList();
        }

        return agreementNoInfoDOS.stream().map(item -> {
            AgreementNoInfo agreementNoInfo = AgreementNoInfo.of();
            BeanUtils.copyProperties(item, agreementNoInfo);
            return agreementNoInfo;
        }).collect(Collectors.toList());
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "agt_getDefaultAgreementTemplate", cacheType= CacheType.LOCAL)
    @Override
    public List<AgreementTemplate> getDefaultAgreementTemplate(Integer agreementType, Long sourceVipType, Long vipType, Integer amount) {
        List<AgreementTemplateDO> agreementTemplateDOS = agreementTemplateMapper.getDefaultBy(agreementType, sourceVipType, vipType, amount);
        if (CollectionUtils.isEmpty(agreementTemplateDOS)) {
            return Collections.emptyList();
        }

        return agreementTemplateDOS.stream().map(item -> {
            AgreementTemplate agreementTemplate = AgreementTemplate.of();
            BeanUtils.copyProperties(item, agreementTemplate);
            return agreementTemplate;
        }).collect(Collectors.toList());
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "atp_getTemplatePrice", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AgreementTempPrice> getTemplatePrice(Integer agreementNo) {
        if (agreementNo == null) {
            return Collections.emptyList();
        }
        List<AgreementTempPriceDO> priceDOS = agreementTempPriceMapper.selectByAgreementNo(agreementNo);
        if (CollectionUtils.isEmpty(priceDOS)) {
            return Collections.emptyList();
        }
        return priceDOS.stream().map(item -> {
            AgreementTempPrice price = AgreementTempPrice.of();
            BeanUtils.copyProperties(item, price);
            return price;
        }).collect(Collectors.toList());
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "getTemplatePriceByCodes", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public AgreementTempPrice getTemplatePriceByCode(String templateCode) {
        AgreementTempPriceDO agreementTempPriceDO = agreementTempPriceMapper.selectByTemplateCode(templateCode);
        if (agreementTempPriceDO == null) {
            return null;
        }
        AgreementTempPrice price = AgreementTempPrice.of();
        BeanUtils.copyProperties(agreementTempPriceDO, price);
        return price;
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "am_getMaterial", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public AgreementMaterial getMaterial(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        AgreementMaterialDO agreementMaterialDO = materialMapper.selectByAgreementNo(agreementNo);
        if (agreementMaterialDO == null) {
            return null;
        }
        AgreementMaterial agreementMaterial = AgreementMaterial.of();
        BeanUtils.copyProperties(agreementMaterialDO, agreementMaterial);
        return agreementMaterial;
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getDefaultAgreementNoByDutType", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public Integer getDefaultAgreementNoByDutType(Integer dutType, Integer amount, Long vipType) {
        return agreementNoInfoMapper.getDefaultAgreementNoByDutType(dutType, amount, vipType);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getAgreementNoByCodeAndPayChannel", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public AgreementNoInfo getAgreementNoByCodeAndPayChannel(String templateCode, Integer payChannel, String partnerId, Integer dutType) {
        if (StringUtils.isBlank(templateCode)) {
            return null;
        }
        AgreementNoInfoDO agreementNoInfoDO = agreementNoInfoMapper.selectMaxPriorityBy(templateCode, payChannel, partnerId, dutType);
        if (agreementNoInfoDO == null) {
            return null;
        }
        AgreementNoInfo agreementNoInfo = AgreementNoInfo.of();
        BeanUtils.copyProperties(agreementNoInfoDO, agreementNoInfo);
        return agreementNoInfo;
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getAgreementListByDutType", cacheType= CacheType.LOCAL, cacheNullValue=true)
    @Override
    public List<AgreementNoInfo> getAgreementListByDutType(Integer dutType) {
        List<AgreementNoInfoDO> agreementNoInfoDOS = agreementNoInfoMapper.getAgreementListByDutType(dutType);
        if (CollectionUtils.isEmpty(agreementNoInfoDOS)) {
            return Collections.emptyList();
        }
        return agreementNoInfoDOS.stream().map(item -> {
            AgreementNoInfo agreementNoInfo = AgreementNoInfo.of();
            BeanUtils.copyProperties(item, agreementNoInfo);
            return agreementNoInfo;
        }).collect(Collectors.toList());
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ani_getAgreementNoInfosByType", cacheType= CacheType.LOCAL, cacheNullValue=true)
    public List<AgreementNoInfo> getAgreementNoInfosByType(int type) {
        List<AgreementNoInfoDO> agreementNoInfosByType = agreementNoInfoMapper.getAgreementNoInfosByType(type);
        if (CollectionUtils.isEmpty(agreementNoInfosByType)) {
            return Collections.emptyList();
        }
        return agreementNoInfosByType.stream().map(item -> {
            AgreementNoInfo agreementNoInfo = AgreementNoInfo.of();
            BeanUtils.copyProperties(item, agreementNoInfo);
            return agreementNoInfo;
        }).collect(Collectors.toList());
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AutoRenewDutType_getByDutType", cacheType= CacheType.LOCAL)
    @Override
    public AutoRenewDutType getByDutType(Integer dutType) {
        if (dutType == null) {
            return null;
        }
        AutoRenewDutTypeDO autoRenewDutTypeDO = autoRenewDutTypeMapper.selectByDutType(dutType);
        if (autoRenewDutTypeDO == null) {
            return null;
        }
        AutoRenewDutType autoRenewDutType = AutoRenewDutType.of();
        BeanUtils.copyProperties(autoRenewDutTypeDO, autoRenewDutType);
        return autoRenewDutType;
    }

    @Override
    @Transactional
    public Integer addIos(AutoRenewDutType autoRenewDutType, AgreementNoInfo agreementNoInfo) {
        if (autoRenewDutType != null) {
            AutoRenewDutTypeDO autoRenewDutTypeDO = new AutoRenewDutTypeDO();
            BeanUtils.copyProperties(autoRenewDutType, autoRenewDutTypeDO);
            autoRenewDutTypeMapper.insert(autoRenewDutTypeDO);
        }
        AgreementNoInfoDO agreementNoInfoDO = new AgreementNoInfoDO();
        BeanUtils.copyProperties(agreementNoInfo, agreementNoInfoDO);
        agreementNoInfoMapper.insert(agreementNoInfoDO);
        return agreementNoInfoDO.getId();
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "agt_getAgreementTemplateByCode", cacheType= CacheType.LOCAL, cacheNullValue=true)
    @Override
    public AgreementTemplate getAgreementTemplateByCode(String code) {
        AgreementTemplateDO agreementTemplateDO = agreementTemplateMapper.selectByCode(code);
        if (agreementTemplateDO == null) {
            return null;
        }
        AgreementTemplate agreementTemplate = AgreementTemplate.of();
        BeanUtils.copyProperties(agreementTemplateDO, agreementTemplate);
        return agreementTemplate;
    }

    @Override
    public List<AgreementTemplate> batchGetAgreementTemplateByCode(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<AgreementTemplateDO> agreementTemplateDOS = agreementTemplateMapper.batchGetByCode(codes);
        return agreementTemplateDOS.stream().map(item -> {
            AgreementTemplate agreementTemplate = AgreementTemplate.of();
            BeanUtils.copyProperties(item, agreementTemplate);
            return agreementTemplate;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> getMaxPriorityTemplateCodeByVipType(Integer type, Long vipType, Integer defaultNo) {
        return agreementNoInfoMapper.getMaxPriorityTemplateCodeByVipType(type, vipType, defaultNo);
    }
}
