package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementMaterialDO {

    private Integer id;

    private Integer agreementNo;
    /**
     * 协议描述文案
     */
    private String description;
    /**
     * 协议详情页地址
     */
    private String detailUrl;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}