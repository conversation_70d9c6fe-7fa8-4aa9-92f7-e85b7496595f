package com.qiyi.vip.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementCancelRestrictionDO {

    private Integer id;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 时长限制,单位:天
     */
    private Integer duration;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}