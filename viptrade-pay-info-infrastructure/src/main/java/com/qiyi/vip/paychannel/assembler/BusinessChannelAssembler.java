package com.qiyi.vip.paychannel.assembler;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.qiyi.vip.domain.paychannel.BusinessChannel;
import com.qiyi.vip.paychannel.BusinessChannelDO;

/**
 * 按业务查询对应的payChannel,实体间进行转换
 *
 * <AUTHOR>
 */
@Mapper
public interface BusinessChannelAssembler {

    BusinessChannelAssembler INSTANCE = Mappers.getMapper(BusinessChannelAssembler.class);

    BusinessChannel toBusinessPayChannel(BusinessChannelDO businessChannelDO);

}
