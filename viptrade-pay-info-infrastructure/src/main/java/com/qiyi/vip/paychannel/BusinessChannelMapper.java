package com.qiyi.vip.paychannel;

import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 业务支付渠道关联表 Mapper
 *
 * <AUTHOR>
 * @date 2024/01/06
 */
@Mapper
public interface BusinessChannelMapper {
    
    /**
     * 根据ID查询
     */
    BusinessChannelDO getById(Long id);

    /**
     * 根据业务编码查询
     */
    List<BusinessChannelDO> getByBusiness(String business);
    
    /**
     * 插入数据
     */
    int insert(BusinessChannelDO businessChannel);

    /**
     * 更新数据
     */
    int update(BusinessChannelDO businessChannel);

    /**
     * 删除数据
     */
    int deleteById(Long id);
}