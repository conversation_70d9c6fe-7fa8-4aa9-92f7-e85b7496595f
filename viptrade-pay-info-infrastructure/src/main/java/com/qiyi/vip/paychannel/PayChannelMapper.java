package com.qiyi.vip.paychannel;


import java.util.List;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 8:40 PM
 */
@Mapper
public interface PayChannelMapper {
    PayChannelDO getById(Integer id);

    PayChannelDO getByCode(String code);

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pc_findAll_mapper", cacheType= CacheType.LOCAL, cacheNullValue=true)
    List<PayChannelDO> findAll();

    List<PayChannelDO> getByIdOrNameOrCode(Integer id, String name, String code, int pageSize, int offset);

    List<PayChannelDO> getTopPayChannels();

    Integer getCountByIdOrNameOrCode(Integer id, String name, String code);

    int insert(PayChannelDO payChannelDO);

    int update(PayChannelDO payChannelDO);
}
