package com.qiyi.vip.paychannel;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;

import com.qiyi.vip.domain.paychannel.BusinessChannel;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paychannel.gateway.PayChannelGateway;
import com.qiyi.vip.dto.data.AddPayChannelDTO;
import com.qiyi.vip.dto.data.UpdatePayChannelDTO;
import com.qiyi.vip.paychannel.assembler.BusinessChannelAssembler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 8:57 PM
 */
@Slf4j
@Component
public class PayChannelGatewayImpl implements PayChannelGateway {
    @Autowired
    private PayChannelMapper payChannelMapper;

    @Resource
    private BusinessChannelMapper businessChannelMapper;

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pc_getById", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public PayChannel getById(Integer id) {
        PayChannelDO payChannelDO = payChannelMapper.getById(id);
        if (null != payChannelDO) {
            PayChannel payChannel = PayChannel.of();
            BeanUtils.copyProperties(payChannelDO, payChannel);
            return payChannel;
        } else {
            return null;
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pc_getByCode", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public PayChannel getByCode(String code) {
        PayChannelDO payChannelDO = payChannelMapper.getByCode(code);
        if (null != payChannelDO) {
            PayChannel payChannel = PayChannel.of();
            BeanUtils.copyProperties(payChannelDO, payChannel);
            return payChannel;
        } else {
            return null;
        }
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "pc_getAll", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<PayChannel> getAll() {
        List<PayChannelDO> payChannelDos = payChannelMapper.findAll();
        if (CollectionUtils.isNotEmpty(payChannelDos)) {
            return payChannelDos.stream().map(payChannelDO -> {
                PayChannel payChannel = PayChannel.of();
                BeanUtils.copyProperties(payChannelDO, payChannel);
                return payChannel;
            }).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    @Override
    public List<PayChannel> getByIdOrNameOrCode(Integer id, String name, String code, int pageNo, int pageSize) {
        List<PayChannelDO> payChannelDos = payChannelMapper.getByIdOrNameOrCode(id, name, code, pageSize, pageSize * (pageNo - 1));
        if (CollectionUtils.isNotEmpty(payChannelDos)) {
            return payChannelDos.stream().map(payChannelDO -> {
                PayChannel payChannel = PayChannel.of();
                BeanUtils.copyProperties(payChannelDO, payChannel);
                return payChannel;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public boolean addPayChannel(AddPayChannelDTO addPayChannelDTO) {
        PayChannelDO payChannelDO = new PayChannelDO();
        BeanUtils.copyProperties(addPayChannelDTO, payChannelDO);
        return payChannelMapper.insert(payChannelDO) > 0;
    }

    @Override
    public Boolean updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO) {
        PayChannelDO payChannelDO = new PayChannelDO();
        BeanUtils.copyProperties(updatePayChannelDTO, payChannelDO);
        int affectRows = payChannelMapper.update(payChannelDO);
        log.info("updatePayChannel success, affectRows: {}", affectRows);
        return true;
    }

    @Override
    public List<PayChannel> getTopPayChannels() {
        List<PayChannelDO> payChannelDos = payChannelMapper.getTopPayChannels();
        if (CollectionUtils.isNotEmpty(payChannelDos)) {
            return payChannelDos.stream().map(payChannelDO -> {
                PayChannel payChannel = PayChannel.of();
                BeanUtils.copyProperties(payChannelDO, payChannel);
                return payChannel;
            }).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public Integer getCountByIdOrNameOrCode(Integer id, String name, String code) {
        return payChannelMapper.getCountByIdOrNameOrCode(id, name, code);
    }

    @Override
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "bc_getByBusinessPayChannels", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public List<BusinessChannel> getPayChannelsByBusiness(String business) {
        List<BusinessChannelDO> businessChannels = businessChannelMapper.getByBusiness(business);
        if (CollectionUtils.isEmpty(businessChannels)) {
            return Collections.emptyList();
        }
        return businessChannels.stream().map(BusinessChannelAssembler.INSTANCE::toBusinessPayChannel).collect(Collectors.toList());
    }
}
