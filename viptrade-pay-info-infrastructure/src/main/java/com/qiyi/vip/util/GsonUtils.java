package com.qiyi.vip.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;

import java.lang.reflect.Type;


/**
 * Gson 工具类
 *
 * <AUTHOR>
 */
public class GsonUtils {

    private GsonUtils() {
    }

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss")
        .setNumberToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE).setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
        .create();


    /**
     * 将对象转为 JSON 字符串
     *
     * @param object 要转换的对象
     * @return 对象的 JSON 字符串表示
     */
    public static String toJson(Object object) {
        //去掉空值
        return GSON.toJson(object);
    }

    /**
     * 将 JSON 字符串转为指定类型的对象
     *
     * @param jsonString JSON 字符串
     * @param type 目标对象类型
     * @param <T> 目标对象的泛型类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String jsonString, Type type) {
        return GSON.fromJson(jsonString, type);
    }

    /**
     * 将 JSON 字符串转为指定类型的对象
     *
     * @param jsonString JSON 字符串
     * @param clazz 目标对象类型
     * @param <T> 目标对象的泛型类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String jsonString, Class<T> clazz) {
        return GSON.fromJson(jsonString, clazz);
    }
}
