package com.qiyi.vip.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import com.iqiyi.kit.http.client.ResponseObject;
import com.iqiyi.kit.http.client.spring.ApacheRestTemplateFactoryBean;

public class ApacheRestTemplateFactoryBeanUtil {

    public static ApacheRestTemplateFactoryBean initRestTemplateFactoryBean(Integer connectTimeoutInMillis,
                                                                            Integer readTimeoutInMillis,
                                                                            int maxPoolSize) {
        ApacheRestTemplateFactoryBean apacheRestTemplateFactoryBean = new ApacheRestTemplateFactoryBean();
        apacheRestTemplateFactoryBean.setConnectTimeoutInMillis(connectTimeoutInMillis);
        apacheRestTemplateFactoryBean.setReadTimeoutInMillis(readTimeoutInMillis);
        apacheRestTemplateFactoryBean.setMaxPoolSize(maxPoolSize);
        apacheRestTemplateFactoryBean.setCodeAwareClass(ResponseObject.class);
        apacheRestTemplateFactoryBean.setEnableMonitor(true);
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        setMessageConverters(apacheRestTemplateFactoryBean, messageConverters);
        return apacheRestTemplateFactoryBean;
    }

    private static void setMessageConverters(ApacheRestTemplateFactoryBean apacheRestTemplateFactoryBean,
                                             List<HttpMessageConverter<?>> messageConverters) {
        //Add the Jackson Message converter
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        // Note: here we are making this converter to process any kind of response,
        // not only application/*json, which is the default behaviour
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.ALL));
        messageConverters.add(converter);
        apacheRestTemplateFactoryBean.setMessageConverters(messageConverters);
    }
}
