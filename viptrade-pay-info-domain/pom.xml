<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qiyi.vip</groupId>
        <artifactId>viptrade-pay-info</artifactId>
        <version>1.3.50</version>
    </parent>

    <artifactId>viptrade-pay-info-domain</artifactId>
    <packaging>jar</packaging>
    <name>viptrade-pay-info-domain</name>

    <dependencies>
        <!-- COLA components -->
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-component-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-component-exception</artifactId>
        </dependency>
        <!-- COLA components End-->
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-logging</artifactId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.config</groupId>
            <artifactId>config-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
