package com.qiyi.vip.domain.paytype;

import com.google.common.base.Splitter;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

import com.qiyi.vip.constant.PasswordFreeEnum;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.remote.AccountGateway;
import com.qiyi.vip.domain.remote.PayCenterGateway;
import com.qiyi.vip.exception.BizException;
import com.iqiyi.solar.config.client.CloudConfig;

import static org.apache.commons.lang3.math.NumberUtils.toInt;

/**
 * <AUTHOR>
 */
@Builder
@Getter
public class PaymentTypeTransform {

    /**
     * 表明该支付方式是否支持签约支付，即自动续费
     */
    private final Boolean isSupportSign;
    /**
     * 是否支持纯签约支付
     */
    private final Boolean isSupportPureSign;
    /**
     * 是否支持免密支付签约
     */
    private final Boolean isSupportPasswordFreeSign;

    /**
     * 如果是签约支付，则对应同类型的基本支付
     */
    private final Long basicPayTypeId;

    /**
     * 免密支付的支付方式
     */
    private final Long passwordFreePayType;

    /**
     * 如果是普通支付，则对应同类型的签约支付
     */
    private final Long signPayTypeId;
    /**
     * 所对应的纯签约支付方式
     **/
    private final Long pureSigningPayTypeId;

    private final Boolean toPasswordFree;

    private final String passwordFreeType;

    private final String passwordFreeCommonPayType;

    private final String passwordFreeDutType;

    private final Integer passwordFreeAgreementNo;

    private final Boolean toH5;

    private final Long h5PayType;

    private boolean isBasicPayType() {
        return basicPayTypeId == null || basicPayTypeId == 0;
    }

    private boolean isSignPayType() {
        return isSupportSign != null && isSupportSign;
    }

    protected Optional<TransformRes> transferPayType(TransformParam transParam) {
        TransformRes transformRes = new TransformRes(transParam.getPayType());
        boolean isBind = isAccountBind(transParam.getUserId(), transParam.getDutType());

        isHasSign(isBind, transParam.getPayType());

        transformRes.setIsFirstSign(!isBind);

        Optional<Long> payTypePureSign = transformSignToPureSign(transParam.getFee());
        if (payTypePureSign.isPresent()) {
            transformRes.setPayType(payTypePureSign.get());
            return Optional.of(transformRes);
        }

        Optional<Long> payTypeH5 = transformSdkToH5(transParam.getUseSdk());
        payTypeH5.ifPresent(transformRes::setPayType);

        if (canTransPasswordFree() && isBind) {
            Boolean hasOpen = hasOpenPasswordFree(transParam.getUserId(), passwordFreeCommonPayType);
            if (Boolean.TRUE.equals(hasOpen) && isNotSureUsePasswordFree(transParam)) {
                throw new BizException(BizException.HAS_SIGN_AND_OPEN_PASSWORD_FREE, "用户已签约且已开通免密~");
            }
            transformRes.setPayType(passwordFreePayType);
            transformRes.setNeedReturnDutType(false);
            return Optional.of(transformRes);
        }

        Optional<Long> basicPayType = transformSignToBasic(transParam.isAutoRenew(), isBind);
        basicPayType.ifPresent(transformRes::setPayType);

        Optional<Long> signPayType = transformBasicToSign(transParam.isAutoRenew(), isBind);
        signPayType.ifPresent(transformRes::setPayType);

        return Optional.of(transformRes);
    }

    /**
     * 是否可以转为免密支付
     */
    private boolean canTransPasswordFree() {
        CloudConfig cloudConfig = ApplicationContextHelper.getBean(CloudConfig.class);
        boolean openTransPasswordFree = cloudConfig.getBooleanProperty("open_trans_password_free", true);
        return Objects.nonNull(passwordFreePayType) && StringUtils.isNotEmpty(passwordFreeCommonPayType) && openTransPasswordFree;
    }

    /**
     * 已签约同时已开通免密支付，中断流程提醒用户是否使用免密
     */
    private boolean isNotSureUsePasswordFree(TransformParam transParam) {
        CloudConfig cloudConfig = ApplicationContextHelper.getBean(CloudConfig.class);
        boolean passwordFreePopups = cloudConfig.getBooleanProperty("password_free_popUps", true);
        if (!passwordFreePopups) {
            return false;
        } else {
            return StringUtils.isEmpty(transParam.getIsPasswordFree());
        }
    }

    /**
     * 如果已签约，部分支付方式不允许重复签约
     *
     * @param isBind  是否已签约
     * @param payType 支付方式
     */
    private void isHasSign(boolean isBind, Long payType) {
        CloudConfig cloudConfig = ApplicationContextHelper.getBean(CloudConfig.class);
        String notAllowRepeatSign = cloudConfig.getProperty("not_allow_repeat_sign_payType", null);
        if (StringUtils.isBlank(notAllowRepeatSign) || Objects.isNull(payType)) {
            return;
        }

        if (isBind && Splitter.on(",").trimResults().splitToList(notAllowRepeatSign).contains(String.valueOf(payType))) {
            throw new BizException(BizException.HAS_SIGN, "用户已签约~");
        }
    }

    private Optional<Long> transformSignToPureSign(Integer fee) {
        if (Objects.isNull(fee) || fee > 0) {
            return Optional.empty();
        }
        if (Objects.nonNull(pureSigningPayTypeId)) {
            return Optional.of(pureSigningPayTypeId);
        }
        return Optional.empty();
    }

    /**
     * 是购买的自动续费但是没有建立自动续费签约关系，则不需要转普通paytype。
     * 如果已经建立签约关系才转普通，因为已经签约了 没有必要再签约
     */
    protected Optional<Long> transformSignToBasic(boolean isAutoRenew, boolean isBind) {
        //isSignPayType老的支付方式，签约购买和普通购买都支持 没有限制
        if (isBasicPayType() || !isSignPayType()) {
            return Optional.empty();
        }

        if (isAutoRenew && !isBind) {
            return Optional.empty();
        }

        return Optional.of(basicPayTypeId);
    }

    protected Optional<TransformRes> transformToPasswordFree(Long payType, Long userId, String isPasswordFree) {
        if (Objects.nonNull(isSupportPasswordFreeSign) && isSupportPasswordFreeSign) {
            TransformRes transformRes = new TransformRes(payType);

            if (StringUtils.isEmpty(passwordFreeDutType)) {
                return Optional.of(transformRes);
            }

            transformRes.setDutType(Integer.parseInt(passwordFreeDutType));
            transformRes.setAgreementNo(passwordFreeAgreementNo);
            if (PasswordFreeEnum.PASSWORDFREESIGNPAY.isPasswordFreePay(isPasswordFree)) {
                firstPwdFreeSignPay(userId, passwordFreeDutType, transformRes);
                return Optional.of(transformRes);
            } else if (PasswordFreeEnum.BASICPAY.isPasswordFreePay(isPasswordFree)) {
                transformRes.setPayType(basicPayTypeId);
                return Optional.of(transformRes);
            } else if (PasswordFreeEnum.PASSWORDFREEPAY.isPasswordFreePay(isPasswordFree)) {
                passwordFreePay(userId, passwordFreeDutType, transformRes);
                return Optional.of(transformRes);
            }
            transformRes.setPayType(basicPayTypeId);
            return Optional.of(transformRes);
        }
        return Optional.empty();
    }

    protected boolean isTransformToPasswordFree() {
        return Objects.nonNull(toPasswordFree)
                && Objects.nonNull(passwordFreeType)
                && Objects.nonNull(passwordFreeDutType)
                && toPasswordFree;
    }

    /**
     * 是购买的自动续费，且payType支持签约，而且没有建立签约关系，则进行签约购买（普通转签约）
     */
    private Optional<Long> transformBasicToSign(boolean isAutoRenew, boolean isBind) {
        if (isSignPayType() || !isAutoRenew) {
            return Optional.empty();
        }

        if (Objects.nonNull(signPayTypeId) && !isBind) {
            return Optional.of(signPayTypeId);
        }
        return Optional.empty();
    }


    private Boolean hasOpenPasswordFree(Long userId, String passwordFreeCommonType) {
        PayCenterGateway payCenterGateway = ApplicationContextHelper.getBean(PayCenterGateway.class);
        return payCenterGateway.hasOpenPasswordFree(userId, passwordFreeCommonType);
    }

    private Boolean isAccountBind(Long userId, Integer dutType) {
        AccountGateway accountGateway = ApplicationContextHelper.getBean(AccountGateway.class);
        return accountGateway.isAccountBind(userId, dutType);
    }

    /**
     * 在 SDK 和 Web 支付方式之间转换。例如，因用户手机没有相应支付方式的 SDK 时，需转换为对应的 H5 支付方式。部分如支付宝 SDK 支持
     * 自动转换，但如百度支付 SDK 则不支持，需要后端进行转换。
     */
    private Optional<Long> transformSdkToH5(String useSdk) {
        if (hasInstallSdk(useSdk) || cannotTransformToH5()) {
            return Optional.empty();
        }
        return Optional.of(h5PayType);
    }

    /**
     * useSDK == 0 means the user's phone is not install payment sdk.
     */
    private boolean hasInstallSdk(String useSdk) {
        return StringUtils.isEmpty(useSdk)
                || toInt(useSdk) != 0;
    }

    private boolean cannotTransformToH5() {
        return Objects.isNull(toH5) || !toH5 || Objects.isNull(h5PayType);
    }

    /**
     * 用户传的是0表示要开通免密签约，或者前端没有取到免密关系。
     * 这个时候如果后端获取到了，走普通支付（规避第三方支付不能再次建立签约关系的情况）。
     * 没有获取到则设置免密支付的dutType
     */
    private void firstPwdFreeSignPay(Long userId, String dutType, TransformRes transformRes) {
        AccountGateway accountGateway = ApplicationContextHelper.getBean(AccountGateway.class);
        if (Boolean.TRUE.equals(accountGateway.isAccountBind(userId, Integer.parseInt(dutType)))) {
            //设置普通支付。
            transformRes.setPayType(basicPayTypeId);
        }
    }

    /**
     * 调用查询免密关系接口，如果不存在免密关系，则不转换，如果存在免密关系需要转换为免密支付的payType和dutType
     */
    private void passwordFreePay(Long userId, String dutType, TransformRes transformRes) {
        Optional<String> result = passwordFreePay(userId, Integer.valueOf(dutType));
        if (result.isPresent()) {
            transformRes.setPayType(Long.parseLong(passwordFreeType));
            transformRes.setContractNo(result.get());
        }
    }

    /**
     * 签约关系双重判断，首先获取自动续费接口里建立的签约关系，以这个为准
     * （因管理页取消免密签约关系时，不通知支付中心，支付中心取消时会通知自动续费系统里的免密关系关系）。
     * 其次获取signcode，这个免密签约使用，如果没有signcode也会认为无签约关系
     */
    private Optional<String> passwordFreePay(Long userId, Integer dutType) {
        if (isPasswordFreeSign(userId, dutType)) {
            //到支付中心查询免密签约sign
            AccountGateway accountGateway = ApplicationContextHelper.getBean(AccountGateway.class);
            return accountGateway.getContractNo(userId, dutType);
        }
        return Optional.empty();
    }

    private boolean isPasswordFreeSign(Long userId, Integer dutType) {
        AccountGateway accountGateway = ApplicationContextHelper.getBean(AccountGateway.class);
        return Boolean.TRUE.equals(accountGateway.isAccountBind(userId, dutType));
    }
}
