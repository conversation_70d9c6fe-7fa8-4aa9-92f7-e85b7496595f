package com.qiyi.vip.domain.domainservice;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.constant.OrderPayAutoRenew;
import com.qiyi.vip.constant.PayTypeEnum;
import com.qiyi.vip.constant.SceneEnum;
import com.qiyi.vip.constant.UserAgentEnum;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.AgreementMaterial;
import com.qiyi.vip.domain.agreement.AgreementTempPrice;
import com.qiyi.vip.domain.constants.PayChannelConstants;
import com.qiyi.vip.domain.duttype.AgreementInfo;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paytype.PayScenePayment;
import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.domain.paytype.PaymentSceneResDTO;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.CommodityCenterGateway;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.PayChannelReqDTO;
import com.qiyi.vip.dto.data.PayChannelResDTO;
import com.qiyi.vip.dto.data.PayInfoReqDTO;
import com.qiyi.vip.dto.data.PayInfoResDTO;
import com.qiyi.vip.dto.data.ProductReqDTO;
import com.qiyi.vip.dto.data.ProductResDTO;
import com.qiyi.vip.dto.data.RoutePayChannelReqDTO;
import com.qiyi.vip.dto.data.RoutePayChannelResult;
import com.qiyi.vip.dto.data.RoutePaymentInfoDTO;
import com.qiyi.vip.dto.data.RoutePaymentInfoResult;
import com.qiyi.vip.dto.data.RouteProductReqDTO;
import com.qiyi.vip.dto.data.RouteProductResResult;
import com.qiyi.vip.dto.data.SmartPayInfoReqDTO;
import com.qiyi.vip.enums.AgreementTypeEnum;
import com.qiyi.vip.exception.Assert;
import com.qiyi.vip.utils.AesEncrypt;
import com.qiyi.vip.utils.AgreementUtils;
import com.qiyi.vip.utils.ClientVersionUtil;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;

import static com.qiyi.vip.constant.ScenarioEnum.H5_WAP;

/**
 * <AUTHOR>
 * @description: 按渠道维度生成下单url, 屏蔽关注细粒度的支付方式细节,收拢支付方式相关业务，降低整体链路的复杂度。
 * @date Create in 17:25 2021/3/10
 */
@Component
@Slf4j
public class PayInfoService {

    private static final String H5_ENV = "h5";

    @Value("${qiyi.domain:http://i.vip.qiyi.com}")
    private String domain;

    @ConfigJsonValue("${smart.pay.info.switch:true}")
    private boolean enableSmartPayInfoSwitch;


    @ConfigJsonValue("${change_payType_condition:[]}")
    private List<ConditionConfig> conditionConfigs;

    public PayInfoResDTO payInfo(PayInfoReqDTO payInfoReqDTO) {
        PayInfoResDTO payInfoResDTO = new PayInfoResDTO();
        List<ProductResDTO> products = new ArrayList<>();

        for (ProductReqDTO productReq : payInfoReqDTO.getProducts()) {
            Assert.notNull(productReq.getSkuId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
            Assert.notNull(productReq.getAutoRenew(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
            ProductResDTO productRes = new ProductResDTO(productReq.getSkuId(), productReq.getAutoRenew(), productReq.getFee());
            List<PayChannelResDTO> payChannelRes = new ArrayList<>();

            for (PayChannelReqDTO channelReqDTO : productReq.getPayChannels()) {
                if (!UserAgentEnum.isNeedHandle(payInfoReqDTO.getUserAgent(), channelReqDTO.getPayChannelId())) {
                    continue;
                }
                Assert.notNull(channelReqDTO.getVersion(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
                Assert.notNull(channelReqDTO.getPayChannelId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());

                PayChannel payChannel = PayChannel.of().getPayChannelById(channelReqDTO.getPayChannelId());
                Assert.notNull(payChannel, ErrorCodeEnum.PARAMETER_ERR.getCode(), "payChannel is invalid!");
                if (Objects.nonNull(channelReqDTO.getSubPayChannelId())) {
                    PayChannel subPayChannel = PayChannel.of().getPayChannelById(channelReqDTO.getSubPayChannelId());
                    Assert.notNull(subPayChannel, ErrorCodeEnum.PARAMETER_ERR.getCode(), "subPayChannel is invalid!");
                }

                List<PaymentType> paymentTypes = PaymentType.of().getPaymentTypeGateway()
                        .getPayTypeByChannel(channelReqDTO.getPayChannelId(), channelReqDTO.getSubPayChannelId(), channelReqDTO.getVersion());

                if (CollectionUtils.isEmpty(paymentTypes)) {
                    log.info("not route to available payment,payChannel:{},subPayChannel:{},version:{}", channelReqDTO.getPayChannelId(),
                            channelReqDTO.getSubPayChannelId(),
                            channelReqDTO.getVersion());
                    continue;
                }

                //多结果情况下，根据是否支持自动续费进行过滤
                if (CollectionUtils.isNotEmpty(paymentTypes) && paymentTypes.size() > 1) {
                    filterByAutoRenew(productReq.getAutoRenew(), paymentTypes);
                }

                hitPayType(productReq, payInfoReqDTO, payChannelRes, channelReqDTO, payChannel, paymentTypes);
            }
            productRes.setPayChannels(payChannelRes);
            products.add(productRes);
        }
        payInfoResDTO.setProducts(products);
        return payInfoResDTO;
    }

    public RoutePaymentInfoResult routePaymentInfo(RoutePaymentInfoDTO routePaymentInfo) {
        List<Integer> payChannels = extractPayChannels(routePaymentInfo);

        RoutePaymentInfoResult result = new RoutePaymentInfoResult(routePaymentInfo.getPayPlatform(), routePaymentInfo.getClientVersion());

        // 获取支付渠道及相关信息
        Map<Long, PayChannel> payChannelMap = getPayChannelMap(payChannels);
        Map<Integer, List<PaymentType>> payChannelToPaymentTypes = getPayChannelToPaymentTypes(payChannels);
        List<PaymentRoute> paymentRoutes = getPaymentRoutes(routePaymentInfo.getPayPlatform());
        RoutePaymentContext routePaymentContext = RoutePaymentContext.builder()
            .routePaymentInfo(routePaymentInfo)
            .payChannelMap(payChannelMap)
            .payChannelToPaymentTypes(payChannelToPaymentTypes)
            .paymentRoutes(paymentRoutes)
            .build();

        // 处理每个商品的支付渠道
        List<RouteProductResResult> products = processProducts(routePaymentContext);

        result.setProducts(products);
        return result;
    }

    /**
     * 获取支持的支付渠道
     * @param routePaymentInfo 请求参数
     * @return 支付渠道列表
     */
    private List<Integer> extractPayChannels(RoutePaymentInfoDTO routePaymentInfo) {
        return routePaymentInfo.getProducts().stream()
            .flatMap(productReqDTO -> productReqDTO.getPayChannels().stream())
            .map(RoutePayChannelReqDTO::getPayChannelId)
            .distinct()
            .collect(Collectors.toList());
    }

    private Map<Long, PayChannel> getPayChannelMap(List<Integer> payChannels) {
        return PayChannel.of().batchGetByIds(payChannels);
    }

    private Map<Integer, List<PaymentType>> getPayChannelToPaymentTypes(List<Integer> payChannels) {
        List<PaymentType> channelPaymentTypes = PaymentType.of().getPaymentTypeGateway().getPayTypeByChannels(payChannels);
        Map<Integer, List<PaymentType>> map = new HashMap<>();
        for (PaymentType paymentType : channelPaymentTypes) {
            Integer key = paymentType.getSubPayChannel() != null ? paymentType.getSubPayChannel() : paymentType.getPayChannel();
            map.computeIfAbsent(key, k -> new ArrayList<>()).add(paymentType);
        }
        return map;
    }

    private List<PaymentRoute> getPaymentRoutes(String payPlatform) {
        return PaymentType.of().getPaymentTypeGateway().getPaymentRoutesByPlatform(payPlatform);
    }

    private List<RouteProductResResult> processProducts(RoutePaymentContext routePaymentContext) {
        List<RouteProductResResult> products = new ArrayList<>();
        RoutePaymentInfoDTO routePaymentInfo = routePaymentContext.getRoutePaymentInfo();
        for (RouteProductReqDTO productReq : routePaymentInfo.getProducts()) {
            Assert.notNull(productReq.getSkuId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
            RouteProductResResult productRes = new RouteProductResResult(productReq.getSkuId());

            Commodity commodity = querySkuInfo(productReq.getSkuId());
            boolean supportSign = determineSupportSign(commodity);

            List<RoutePayChannelResult> payChannelRes = processPayChannels(productReq, routePaymentContext, supportSign);

            productRes.setPayChannels(payChannelRes);
            products.add(productRes);
        }
        return products;
    }

    private boolean determineSupportSign(Commodity commodity) {
        if (Objects.nonNull(commodity)) {
            int supportSignValue =
                OrderPayAutoRenew.isAutoRenewPayRequest(commodity.getAutoRenew()) ? PaymentType.SUPPORT_SIGN : PaymentType.NOT_SUPPORT_SIGN;
            return BooleanUtils.toBoolean(supportSignValue);
        } else {
            return false;
        }
    }

    private List<RoutePayChannelResult> processPayChannels(RouteProductReqDTO productReq, RoutePaymentContext routePaymentContext, boolean supportSign) {
        List<RoutePayChannelResult> payChannelRes = new ArrayList<>();
        productReq.getPayChannels().forEach(routePayChannelReq -> {
            long payChannelId = routePayChannelReq.getPayChannelId();
            Assert.notNull(routePayChannelReq, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
            PayChannel payChannel = routePaymentContext.getPayChannelMap().get(payChannelId);
            if (payChannel == null) {
                log.error("invalid payChannel Id, productReq:{}, payChannelId:{}", productReq, payChannelId);
                return;
            }
            Map<Integer, PaymentType> paymentTypes = getPaymentTypeMapByChannelAndSign(payChannel, routePaymentContext.getPayChannelToPaymentTypes()
                .get(routePayChannelReq.getPayChannelId()), supportSign);
            log.info("get paymentTypes by channelId:{},supportSign:{}", routePayChannelReq.getPayChannelId(), supportSign);

            if (MapUtils.isEmpty(paymentTypes)) {
                log.info("no PaymentType matched!, productReq:{}, payChannelId:{}", productReq, payChannelId);
                return;
            }

            RoutePaymentTempContext routePaymentTempContext = RoutePaymentTempContext.builder()
                .productReqDTO(productReq)
                .payChannel(payChannel)
                .paymentTypes(paymentTypes)
                .payChannelReqDTO(routePayChannelReq)
                .build();

            processPaymentRoutes(routePaymentContext, routePaymentTempContext, payChannelRes);
        });
        return payChannelRes;
    }

    private void processPaymentRoutes(RoutePaymentContext routePaymentContext,
        RoutePaymentTempContext routePaymentTempContext,
        List<RoutePayChannelResult> payChannelRes) {
        List<PaymentRoute> matchedRoutes = routePaymentContext.getPaymentRoutes().stream()
            .filter(paymentRoute -> paymentRoute.getPayChannel().equals(routePaymentTempContext.getPayChannel().getId().intValue()))
            .filter(paymentRoute -> isVersionInRange(routePaymentContext, paymentRoute))
            .collect(Collectors.toList());

        // 当查出多条时，进行 AB或其它条件的过滤
        if (matchedRoutes.size() > 1 && StringUtils.isNotEmpty(routePaymentContext.getRoutePaymentInfo().getAbTestGroup())) {
            matchedRoutes = matchedRoutes.stream()
                .filter(paymentRoute -> Objects.equals(paymentRoute.getAbTestGroup(), routePaymentContext.getRoutePaymentInfo().getAbTestGroup()))
                .collect(Collectors.toList());
        }
        log.info("matchedRoutes size: {}, matchedRoutes: {}", matchedRoutes.size(), matchedRoutes);
        if (CollectionUtils.isEmpty(matchedRoutes)) {
            return;
        }
        matchedRoutes
            .forEach(paymentRoute -> addMatchingPayChannelResult(paymentRoute, routePaymentTempContext, payChannelRes));
    }


    private boolean isVersionInRange(RoutePaymentContext routePaymentContext, PaymentRoute paymentRoute) {
        RoutePaymentInfoDTO routePaymentInfo = routePaymentContext.getRoutePaymentInfo();

        return ClientVersionUtil.isInVersionRange(routePaymentInfo.getClientVersion(), paymentRoute.getVersionFrom(), paymentRoute.getVersionTo());
    }

    private void addMatchingPayChannelResult(PaymentRoute paymentRoute,
        RoutePaymentTempContext routePaymentTempContext,
        List<RoutePayChannelResult> payChannelRes) {
        if (MapUtils.isEmpty(routePaymentTempContext.getPaymentTypes())) {
            return;
        }
        PaymentType paymentType = routePaymentTempContext.getPaymentTypes().get(paymentRoute.getPayType().intValue());
        //从支持的所有支付方式中获取路由到的支付方式，如果未获取到则说明该支付方式并不支持该套餐使用
        if (Objects.nonNull(paymentType)) {
            Integer payTypeInt = getPayTypeInt(paymentRoute, routePaymentTempContext);
            //如果路由到后又经过配置中心干预调整了，此时需要重新获取payType相关信息
            if (!payTypeInt.equals(paymentRoute.getPayType().intValue())) {
                paymentType = PaymentType.of().getPaymentTypeGateway().getById(Long.valueOf(payTypeInt));
            }
            log.info("this paymentRoute can use, paymentRoute:{},payChannel:{},skuId:{}",
                paymentRoute, routePaymentTempContext.getPayChannelReqDTO().getPayChannelId(), routePaymentTempContext.getProductReqDTO().getSkuId());
            RoutePayChannelResult routePayChannelResult = createRoutePayChannelResult(routePaymentTempContext.getPayChannel(), paymentType);
            log.info("this paymentRoute can use, routePayChannelResult:{}", routePayChannelResult);
            payChannelRes.add(routePayChannelResult);
        } else {
            log.info("this paymentRoute not can use!, paymentRoute:{},skuId:{}", paymentRoute, routePaymentTempContext.getProductReqDTO().getSkuId());
        }
    }

    private Integer getPayTypeInt(PaymentRoute paymentRoute, RoutePaymentTempContext routePaymentTempContext) {
        Integer payTypeInt = paymentRoute.getPayType().intValue();
        if (CollectionUtils.isNotEmpty(conditionConfigs)) {
            for (ConditionConfig config : conditionConfigs) {
                if (config.isMatch(routePaymentTempContext, paymentRoute)) {
                    log.info("this payType change by config, before payType is :{},after payType is :{}", payTypeInt, config.getValue());
                    payTypeInt = config.getValue();
                    break;
                }
            }
        }
        return payTypeInt;
    }

    private static final Map<Integer, String> supportTypeMap;

    static {
        Map<Integer, String> map = new HashMap<>();
        map.put(378, "ALI,WECHAT");
        map.put(384, "WECHAT");
        supportTypeMap = Collections.unmodifiableMap(map);
    }

    private RoutePayChannelResult createRoutePayChannelResult(PayChannel payChannel, PaymentType payType) {
        RoutePayChannelResult routePayChannelResult = new RoutePayChannelResult();
        routePayChannelResult.setPayChannelId(payChannel.getId().intValue());
        routePayChannelResult.setPayChannelCode(payChannel.getCode());
        routePayChannelResult.setName(payChannel.getDescription());
        routePayChannelResult.setIcon(payChannel.getIconUrl());
        routePayChannelResult.setPayType(payType.getId());
        routePayChannelResult.setSupportType(supportTypeMap.get(payType.getId().intValue()));
        routePayChannelResult.setPayCenterCode(payType.getPayCenterCode());
        routePayChannelResult.setTips(getPasswordFreeOpenTips(payType));
        routePayChannelResult.setIsSupportPasswordFreeSign(isSupportPasswordFreeSign(payType));
        routePayChannelResult.setChannelProperties(payChannel.getProperties());
        routePayChannelResult.setIsSupportPureSign(isSupportPureSign(payType));
        routePayChannelResult.setDutAgreementName(payChannel.getDutAgreementName());
        routePayChannelResult.setDutAgreementUrl(payChannel.getDutAgreementUrl());
        routePayChannelResult.setPromotionText(payChannel.getPromotionText());
        return routePayChannelResult;
    }

    private String getPasswordFreeOpenTips(PaymentType payerType) {
        return Objects.nonNull(payerType.getPaymentTypeItem()) ? payerType.getPaymentTypeItem().getPasswordFreeOpenTips() : null;
    }

    private boolean isSupportPasswordFreeSign(PaymentType payerType) {
        return Objects.nonNull(payerType.getPayTypeTransform()) && payerType.getPayTypeTransform().getIsSupportPasswordFreeSign();
    }

    private boolean isSupportPureSign(PaymentType payerType) {
        return Objects.nonNull(payerType.getPayTypeTransform()) && payerType.getPayTypeTransform().getIsSupportPureSign();
    }


    private Commodity querySkuInfo(String skuId) {
        CommodityCenterGateway commodityCenterGateway = ApplicationContextHelper.getBean(CommodityCenterGateway.class);
        Commodity commodity = commodityCenterGateway.query(skuId);
        log.info("querySkuInfo, skuId:{},result:{}", skuId, commodity);
        return commodity;
    }

    public PayInfoResDTO smartPayInfo(SmartPayInfoReqDTO smartPayInfoReqDto) {
        String sceneCode = smartPayInfoReqDto.getSceneCode();
        if (StringUtils.isBlank(sceneCode)) {
            sceneCode = SceneEnum.H5.getCode();
        }
        boolean lessThan1390 = ClientVersionUtil.lt(smartPayInfoReqDto.getClientVersion(), "13.9.0");
        boolean adrScene = SceneEnum.isAdrScene(sceneCode);
        if (enableSmartPayInfoSwitch && lessThan1390 && adrScene) {
            sceneCode = SceneEnum.H5.getCode();
        }

        PayInfoResDTO payInfoResDTO = new PayInfoResDTO();
        List<ProductResDTO> products = new ArrayList<>();
        List<Integer> payChannels = smartPayInfoReqDto.getProducts().stream()
                .flatMap(productReqDTO -> productReqDTO.getPayChannels().stream())
                .map(PayChannelReqDTO::getPayChannelId)
                .distinct().collect(Collectors.toList());
        Map<Long, PayChannel> payChannelMap = PayChannel.of().batchGetByIds(payChannels);
        List<PaymentType> channelPaymentTypes = PaymentType.of().getPaymentTypeGateway().getPayTypeByChannels(payChannels);
        Map<Integer, List<PaymentType>> payChannelToPaymentTypes = new HashMap<>();
        for (PaymentType paymentType : channelPaymentTypes) {
            Integer key = paymentType.getSubPayChannel() != null ? paymentType.getSubPayChannel() : paymentType.getPayChannel();
            payChannelToPaymentTypes.computeIfAbsent(key, k -> new ArrayList<>()).add(paymentType);
        }
        Map<Integer, List<PayScenePayment>> payChannelToScenePaymentMap = PayScenePayment.of().getPayChannelMapBySceneCode(sceneCode);
        boolean paymentScenePaymentNotConfig = MapUtils.isEmpty(payChannelToScenePaymentMap);
        for (ProductReqDTO productReq : smartPayInfoReqDto.getProducts()) {
            Integer autoRenew = productReq.getAutoRenew();
            Assert.notNull(productReq.getSkuId(), ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
            ProductResDTO productRes = new ProductResDTO(productReq.getSkuId(), autoRenew, productReq.getFee());
            int supportSignValue = OrderPayAutoRenew.isAutoRenewPayRequest(autoRenew) ? PaymentType.SUPPORT_SIGN : PaymentType.NOT_SUPPORT_SIGN;
            boolean supportSign = BooleanUtils.toBoolean(supportSignValue);
            if (paymentScenePaymentNotConfig) {
                productRes.setPayChannels(Collections.emptyList());
                products.add(productRes);
                continue;
            }

            List<PayChannelResDTO> payChannelRes = new ArrayList<>();
            for (PayChannelReqDTO channelReqDTO : productReq.getPayChannels()) {
                Integer payChannelId = channelReqDTO.getPayChannelId();
                Assert.notNull(payChannelId, ErrorCodeEnum.PARAMETER_ERR.getCode(), ErrorCodeEnum.PARAMETER_ERR.getName());
                if (payChannelRes.stream().map(PayChannelResDTO::getPayChannelId).anyMatch(payChannelId::equals)) {
                    continue;
                }
                PayChannel payChannel = payChannelMap.get(payChannelId.longValue());
                if (payChannel == null) {
                    log.error("invalid payChannel Id, productReq:{}, payChannelId:{}", productReq, payChannelId);
                    continue;
                }
                List<PaymentType> paymentTypes = getPaymentTypeByChannelAndSign(payChannel, payChannelToPaymentTypes.get(payChannelId), supportSign);
                if (CollectionUtils.isEmpty(paymentTypes)) {
                    log.info("no PaymentType matched!, productReq:{}, payChannelId:{}", productReq, payChannelId);
                    continue;
                }

                PaymentSceneResDTO paymentSceneResDTO = getPayChannelRes(payChannelToScenePaymentMap, supportSign, paymentTypes, payChannel);
                if (paymentSceneResDTO == null) {
                    log.info("not match cloudConfig payType!, productReq:{}, payChannelId:{}", productReq, payChannelId);
                    continue;
                }

                PayChannelResDTO payChannelResDTO = paymentSceneResDTO.getPayChannelResDTO();
                if (productReq.getVipType() != null && productReq.getAmount() != null && productReq.getAutoRenew() != null && productReq.getAutoRenew() != 0) {
                    PaymentType destPaymentType = paymentSceneResDTO.getPaymentType();
                    AgreementInfoResDTO agreementInfoResDTO = buildAgreementInfoRes(productReq, destPaymentType.getPayChannel(), destPaymentType.getId());
                    if (agreementInfoResDTO != null) {
                        payChannelResDTO.setAgreementInfo(agreementInfoResDTO);
                    }
                }
                payChannelRes.add(payChannelResDTO);
            }
            productRes.setPayChannels(payChannelRes);
            products.add(productRes);
        }
        payInfoResDTO.setProducts(products);
        return payInfoResDTO;
    }

    private AgreementInfoResDTO buildAgreementInfoRes(ProductReqDTO productReq, Integer payChannel, Long payType) {
        AgreementInfo agreementInfo = PaymentDutType.of()
                .getAgreementInfo(payChannel, payType, productReq.getSourceVipType(), productReq.getVipType(),
                        productReq.getAmount(), null, productReq.getPriceActCode(), null, null);

        if (agreementInfo == null) {
            return null;
        }

        Integer agreementNo = agreementInfo.getAgreementNo() != null ? agreementInfo.getAgreementNo() : null;
        Integer agreementType = agreementInfo.getType();
        AgreementMaterial agreementMaterial = AgreementMaterial.of().getAgreementGateway().getMaterial(agreementNo);
        AgreementTypeEnum agreementTypeEnum = AgreementTypeEnum.valueOf(agreementType);

        return AgreementInfoResDTO.builder()
                .agreementNo(agreementNo)
                .agreementType(agreementType)
                .pullUp(agreementTypeEnum.getPullUp())
                .description(getAgreementDescription(productReq.getAmount(), agreementNo, agreementType, agreementMaterial, agreementInfo))
                .build();
    }

    private String getAgreementDescription(Integer amount, Integer agreementNo, Integer agreementType, AgreementMaterial agreementMaterial, AgreementInfo agreementInfo) {
        if (agreementMaterial != null && agreementMaterial.getDescription() != null) {
            return agreementMaterial.getDescription();
        }
        if (agreementType != AgreementTypeEnum.AUTO_RENEW.getValue()) {
            return null;
        }
        AgreementTempPrice agreementTempPrice = AgreementTempPrice.of().getFirstRenewPrice(agreementNo);
        if (agreementInfo == null && agreementTempPrice == null) {
            return null;
        }
        Integer renewPrice = agreementTempPrice != null ? agreementTempPrice.getPrice() : agreementInfo.getRenewPrice();
        return AgreementUtils.getDefaultAgreementDesc(amount, renewPrice);
    }

    /**
     * 银行卡支付方式特殊，一个支付方式既支持普通，又支持签约，所以此处不能做前置过滤
     * @param payChannel 支付渠道
     * @param allPaymentTypeList 该渠道下的所有支付试试
     * @param supportSign 是否支持签约
     * @return 支持的支付方式
     */
    private List<PaymentType> getPaymentTypeByChannelAndSign(PayChannel payChannel, List<PaymentType> allPaymentTypeList, boolean supportSign) {
        if (PayChannelConstants.YHK_PAY.equals(payChannel.getCode()) || PayChannelConstants.SCAN_PAY.equals(payChannel.getCode())) {
            return allPaymentTypeList;
        }
        if (CollectionUtils.isEmpty(allPaymentTypeList)) {
            return Collections.emptyList();
        }
        return allPaymentTypeList.stream()
            .filter(Objects::nonNull)
            .filter(p -> supportSign == p.isSupportSign())
            .collect(Collectors.toList());
    }
    /**
     * 银行卡支付方式特殊，一个支付方式既支持普通，又支持签约，所以此处不能做前置过滤
     * 扫码支付同样特殊，并不是一个有效的支付方式，实际支付方式的使用需要再次路由，这就是个占位的，为了兼容
     * @param payChannel 支付渠道
     * @param allPaymentTypeList 该渠道下的所有支付试试
     * @param supportSign 是否支持签约
     * @return 支持的支付方式
     */
    private Map<Integer, PaymentType> getPaymentTypeMapByChannelAndSign(PayChannel payChannel, List<PaymentType> allPaymentTypeList, boolean supportSign) {
        Map<Integer, PaymentType> map = new HashMap<>();
        if (PayChannelConstants.YHK_PAY.equals(payChannel.getCode()) || PayChannelConstants.SCAN_PAY.equals(payChannel.getCode())) {
            for (PaymentType paymentType : allPaymentTypeList) {
                map.put(paymentType.getId().intValue(), paymentType);
            }
            return map;
        }
        if (CollectionUtils.isEmpty(allPaymentTypeList)) {
            return Collections.emptyMap();
        }
        return allPaymentTypeList.stream()
            .filter(Objects::nonNull)
            .filter(p -> supportSign == p.isSupportSign())
            .collect(Collectors.toMap(paymentType -> paymentType.getId().intValue(), p -> p));

    }

    private static final String KEY = "oi7Rwib0TOcJdP81";

    private PaymentSceneResDTO getPayChannelRes(Map<Integer, List<PayScenePayment>> payChannelToScenePaymentMap, boolean supportSign,
                                              List<PaymentType> paymentTypes, PayChannel payChannel) {
        int payChannelId = payChannel.getId().intValue();
        List<PayScenePayment> payScenePayments = payChannelToScenePaymentMap.get(payChannelId);
        if (CollectionUtils.isEmpty(payScenePayments)) {
            log.warn("payScenePayments is null for payChannel:{}", payChannel.getPayChannelById(payChannelId));
            return null;
        }
        PayScenePayment payScenePayment = payScenePayments.get(0);
        Integer scenePaymentType = supportSign ? payScenePayment.getSignPayType() : payScenePayment.getBasicPayType();
        if (Objects.isNull(scenePaymentType)) {
            return null;
        }
        PaymentType destinationPayType = paymentTypes.stream()
            .filter(paymentType -> paymentType.getId().intValue() == scenePaymentType)
            .findFirst().orElse(null);
        if (Objects.isNull(destinationPayType)) {
            return null;
        }
        PayChannelResDTO payChannelResDTO = PayChannelResDTO.builder()
                .payType(destinationPayType.getId())
                .icon(payChannel.getIconUrl())
                .payChannelId(payChannelId)
                .payChannelCode(payChannel.getCode())
                .isSupportPasswordFreeSign(Objects.nonNull(destinationPayType.getPayTypeTransform())
                    ? destinationPayType.getPayTypeTransform().getIsSupportPasswordFreeSign()
                    : Boolean.FALSE)
                .payCenterCode(destinationPayType.getPayCenterCode())
                .tips(Objects.nonNull(destinationPayType.getPaymentTypeItem())
                    ? destinationPayType.getPaymentTypeItem().getPasswordFreeOpenTips()
                    : null)
                .name(destinationPayType.getPaymentTypeItem().getName())
                .dutAgreementName(destinationPayType.isSupportSign() ? payChannel.getDutAgreementName() : null)
                .dutAgreementUrl(destinationPayType.isSupportSign() ? payChannel.getDutAgreementUrl() : null)
                .promotionText(destinationPayType.isSupportSign() ? payChannel.getPromotionText() : null)
                .scenario(payScenePayment.getScenario())
                .env(payScenePayment.getSceneEnv())
                .build();
        return PaymentSceneResDTO.builder()
            .payChannelResDTO(payChannelResDTO)
            .paymentType(destinationPayType)
            .build();
    }

    /**
     * 扫码支付的payType由扫码后路由
     *
     * @param interfaceType 接口类型
     * @param id            支付方式
     * @return 生成url地址
     */
    private String generate(String interfaceType, Long id) {
        String doPayUrl = domain.concat("/pay/dopay.action?basicParam=");
        String scanPayUrl = domain.concat("/pay/scan_pay.action?basicParam=");
        String basicParam = "apiVersion=2";
        try {
            if ("scanpay".equals(interfaceType)) {
                return scanPayUrl.concat("?basicParam=".concat(AesEncrypt.encrypt(basicParam, KEY)));
            } else {
                basicParam = basicParam.concat("&payType=".concat(id.toString()));
                return doPayUrl.concat(AesEncrypt.encrypt(basicParam, KEY));
            }
        } catch (Exception e) {
            log.error("generate url failed!", e);
        }
        return null;
    }


    /**
     * 通过版本命中唯一的支付方式
     *
     * @param payInfoReqDTO 请求入参
     * @param payChannelRes 根据渠道入参生成的渠道信息
     * @param channelReqDTO 渠道请求参数
     * @param payChannel    支付渠道
     * @param paymentTypes  过滤后的支付方式
     */
    private void hitPayType(ProductReqDTO productReq, PayInfoReqDTO payInfoReqDTO, List<PayChannelResDTO> payChannelRes,
                            PayChannelReqDTO channelReqDTO, PayChannel payChannel, List<PaymentType> paymentTypes) {
        for (PaymentType paymentType : paymentTypes) {
            if (Objects.isNull(paymentType.getPaymentTypeExtend())) {
                continue;
            }

            String url;
            Long payType = paymentType.getId();
            //h5活动页放在微信中使用时，支付方式只支持使用公众号支付
            if (UserAgentEnum.WECHAT.getUserAgent().equals(payInfoReqDTO.getUserAgent()) &&
                    H5_WAP.getScenario().equals(channelReqDTO.getScenario())) {
                payType = PayTypeEnum.routePayType(payInfoReqDTO.getUserAgent(), productReq.getAutoRenew());
            } else if (!UserAgentEnum.ALIPAY.getUserAgent().equals(payInfoReqDTO.getUserAgent())
                    && PayTypeEnum.ONLY_SUPPORTED_IN_ALI_CLIENT.contains(paymentType.getId())) {
                continue;
            }

            //todo 临时性处理,reason:415/416不能完全替代301,可替代后该逻辑删除
            if (UserAgentEnum.ALIPAY.getUserAgent().equals(payInfoReqDTO.getUserAgent())) {
                if (OrderPayAutoRenew.isAutoRenewPayRequest(productReq.getAutoRenew()) && paymentType.getId().intValue() == 301) {
                    continue;
                }
            }

            url = generate(payInfoReqDTO.getInterfaceType(), payType);

            PayChannelResDTO payChannelResDTO = new PayChannelResDTO(channelReqDTO.getPayChannelId(),
                    channelReqDTO.getSubPayChannelId(), payChannel.getDescription());
            if (payInfoReqDTO.getHasPayType()) {
                payChannelResDTO.setPayType(payType);
            }
            payChannelResDTO.setIcon(paymentType.getPaymentTypeItem().getIconUrl());
            payChannelResDTO.setVersion(channelReqDTO.getVersion());
            payChannelResDTO.setUrl(url);
            payChannelResDTO.setScenario(paymentType.getPaymentTypeExtend().getScenario());
            payChannelRes.add(payChannelResDTO);
            break;
        }
    }

    /**
     * 是否开通自动续费
     * todo 因历史数据原因，多条数据的情况下，此维度只remove一条,数据保证正确性后此逻辑需要改造
     *
     * @param autoRenew    自动续费状态
     * @param paymentTypes 支付方式列表
     */
    private void filterByAutoRenew(Integer autoRenew, List<PaymentType> paymentTypes) {
        final Iterator<PaymentType> each = paymentTypes.iterator();
        if (OrderPayAutoRenew.isAutoRenewPayRequest(autoRenew)) {
            while (each.hasNext()) {
                if (!each.next().isSupportSign()) {
                    each.remove();
                    break;
                }
            }
        } else {
            while (each.hasNext()) {
                if (each.next().isSupportSign()) {
                    each.remove();
                    break;
                }
            }
        }
    }
}
