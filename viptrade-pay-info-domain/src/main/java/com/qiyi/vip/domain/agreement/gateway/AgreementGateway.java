package com.qiyi.vip.domain.agreement.gateway;

import com.qiyi.vip.domain.agreement.AgreementMaterial;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.agreement.AgreementTempPrice;
import com.qiyi.vip.domain.agreement.AgreementTemplate;
import com.qiyi.vip.domain.agreement.AutoRenewDutType;

import java.util.List;

/**
 * @auther: guojing
 * @date: 2023/2/9 3:47 PM
 * @description:
 */
public interface AgreementGateway {

    AgreementNoInfo getAgreementNoInfo(Integer agreementNo);

    List<AgreementNoInfo> getAgreementNoInfosByTemplateCode(String templateCode);

    /**
     * 查询协议价格
     * @param agreementNo
     */
    List<AgreementTempPrice> getTemplatePrice(Integer agreementNo);

    /**
     * 通过【协议模板code】查询【协议模板价格】
     */
    AgreementTempPrice getTemplatePriceByCode(String templateCode);

    /**
     * 查询协议素材信息
     * @param agreementNo
     */
    AgreementMaterial getMaterial(Integer agreementNo);

    /**
     * 根据代扣方式获取正价协议号
     */
    Integer getDefaultAgreementNoByDutType(Integer dutType, Integer amount, Long vipType);

    AgreementNoInfo getAgreementNoByCodeAndPayChannel(String templateCode, Integer payChannel, String partnerId,Integer dutType);

    List<AgreementNoInfo> getAgreementListByDutType(Integer dutType);

    List<AgreementNoInfo> getAgreementNoInfosByType(int type);

    AutoRenewDutType getByDutType(Integer dutType);

    /**
     * 新增苹果自动续费协议
     * @param autoRenewDutType
     * @param agreementNoInfo
     */
    Integer addIos(AutoRenewDutType autoRenewDutType, AgreementNoInfo agreementNoInfo);

    List<AgreementTemplate> getDefaultAgreementTemplate(Integer agreementType, Long sourceVipType, Long vipType, Integer amount);

    AgreementTemplate getAgreementTemplateByCode(String code);

    List<AgreementTemplate> batchGetAgreementTemplateByCode(List<String> codes);

    List<String> getMaxPriorityTemplateCodeByVipType(Integer type, Long vipType, Integer defaultNo, Boolean isPureSign);

}
