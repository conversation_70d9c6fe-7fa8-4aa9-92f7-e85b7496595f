package com.qiyi.vip.domain.paytype;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description: 支付方式域定义
 * @date Create in 17:47 2021/3/5
 */
@ToString
@Builder
@Getter
public class PaymentTypeItem {
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 免密支付弹窗上描述文案
     */
    private String passwordFreeOpenTips;

    /**
     * 付款授权服务协议名称
     */
    private String dutAgreementName;
    /**
     * 付款授权服务协议url
     */
    private String dutAgreementUrl;
    /**
     * 图标url
     */
    private String iconUrl;

    /**
     * 状态，0：已下线，1：正常
     */
    private Integer status;

}
