package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

import com.qiyi.vip.constant.StatusEnum;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.enums.AgreementTypeEnum;

/**
 * 协议基础信息模板
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementTemplate implements Serializable {

    /**
     * 首次代扣时间
     */
    public static final String FIRST_RENEW_DAYS = "firstRenewDays";

    private Integer id;
    /**
     * 协议模板code
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 协议模板类型
     * @see com.qiyi.vip.enums.AgreementTypeEnum
     */
    private Integer type;
    /**
     * 原会员类型，升级前会员类型
     */
    private Long sourceVipType;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 签约产品code
     */
    private String pid;
    /**
     * 签约产品skuId
     */
    private String skuId;
    /**
     * 完结订单产品code
     */
    private String completeOrderPid;
    /**
     * 签约产品时长
     */
    private Integer amount;
    /**
     * 周期类型
     */
    private Integer periodType;
    /**
     * 期数
     */
    private Integer periods;
    /**
     * 促销类型：0:正价;1:首X期优惠
     */
    private Integer discountType;
    /**
     * 优惠期数
     */
    private Integer discountPeriods;
    /**
     * 每期持续时长
     */
    private Integer periodDuration;
    /**
     * 每期的时间单位
     * @see com.qiyi.vip.enums.PeriodUnitEnum
     */
    private Integer periodUnit;
    /**
     * 定价策略
     * @see com.qiyi.vip.enums.PricingStrategyEnum
     */
    private Integer pricingStrategy;
    /**
     * 状态,0:无效;1:有效
     * @see com.qiyi.vip.constant.StatusEnum
     */
    private Integer status;
    /**
     * 扩展信息
     */
    private String attributes;

    private AgreementGateway agreementGateway;

    public static AgreementTemplate of() {
        return new AgreementTemplate();
    }

    public AgreementGateway getAgreementGateway() {
        if (this.agreementGateway == null) {
            this.agreementGateway = ApplicationContextHelper.getBean(AgreementGateway.class);
        }
        return agreementGateway;
    }

    public boolean invalid(){
        return this.status != null && this.status == StatusEnum.INVALID.getStatus();
    }

    /**
     * 是否需要结算
     */
    public boolean needSettle() {
        AgreementTypeEnum agreementTypeEnum = AgreementTypeEnum.valueOf(this.type);
        return agreementTypeEnum != null && agreementTypeEnum.needSettle();
    }

    public AgreementTemplate getByCode(String code) {
        return getAgreementGateway().getAgreementTemplateByCode(code);
    }

    public List<AgreementTemplate> batchGetByCode(List<String> codes) {
        return getAgreementGateway().batchGetAgreementTemplateByCode(codes);
    }

}