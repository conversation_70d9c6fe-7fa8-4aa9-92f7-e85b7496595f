package com.qiyi.vip.domain.duttype;

import com.qiyi.vip.constant.AutoRenewConstants;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.duttype.gateway.PaymentDutTypeGateway;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;

/**
 * <AUTHOR>
 * @description: 支付方式域定义
 * @date Create in 17:47 2021/3/5
 */
@Data
public class PaymentDutType implements Serializable {
    /**
     * id.
     */
    protected Long id;

    /**
     * payType.奇悦支付方式编码
     *
     * @return
     */
    private Integer payType;

    /**
     * dutType.代扣方式编码
     *
     * @return3
     */
    private Integer dutType;
    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * serviceCode
     *
     * @return
     */
    private String serviceCode;

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    private Integer amount;

    /**
     * 升级自动续费源会员类型
     */
    private Long sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    private Long vipType;

    /**
     * 活动编码（fs值）
     */
    private String actCode;
    /**
     * 协议活动code
     */
    private String agreementActCode;

    /**
     * 签约价，单位为分
     */
    private Integer renewPrice;

    /**
     * 有效开始时间
     */
    private Timestamp validStartTime;

    /**
     * 有效结束时间
     */
    private Timestamp validEndTime;

    private String partnerId;

    PaymentDutTypeGateway paymentDutTypeGateway;

    public List<PaymentDutType> getDutTypeInfo(Long vipType, Integer amount,
                                               String actCode, List<Integer> dutTypes) {
        return getPaymentDutTypeGateway().paymentDutTypeList(vipType, amount, actCode, dutTypes);
    }

    public PaymentDutType getRenewPrice(Long vipType, Integer amount,
                                        String actCode, List<Integer> dutTypes) {
        List<PaymentDutType> paymentDutTypes = getPaymentDutTypeGateway().paymentDutTypeList(vipType, amount, actCode, dutTypes);
        if (CollectionUtils.isEmpty(paymentDutTypes)) {
            if (StringUtils.isNotBlank(actCode)) {
                paymentDutTypes = getPaymentDutTypeGateway().paymentDutTypeList(vipType, amount, null, dutTypes);
                if (CollectionUtils.isNotEmpty(paymentDutTypes)) {
                    return paymentDutTypes.get(0);
                }
            }
            return null;
        }
        return paymentDutTypes.get(0);
    }

    /**
     * 微信不同代扣方式可能对应不同时长
     * 包月(6)/青春套餐(22)：amount=1
     * 包季(23)：amount=3
     * 包年(24)：amount=12
     *
     * @param dutType 代扣类型
     * @return Integer
     */
    public Integer getWechatAmountByDutType(Integer dutType) {
        List<PaymentDutType> paymentDutTypes = getPaymentDutTypeGateway().getPaymentDutTypesByDutType(dutType);
        if (!CollectionUtils.isEmpty(paymentDutTypes)) {
            return paymentDutTypes.get(0).getAmount();
        }
        return AutoRenewConstants.AMOUNT_OF_COMMON_AUTORENEW;
    }

    public List<PaymentDutType> getDutTypeExcludeActCode(Long vipType, Integer amount, String partnerId) {
        List<PaymentDutType> paymentDutTypes = getPaymentDutTypeGateway().getDutTypeByVipTypeAndAmountExcludeActCode(vipType, amount, partnerId);
        if (CollectionUtils.isEmpty(paymentDutTypes)) {
            paymentDutTypes = getPaymentDutTypeGateway().getDutTypeByVipTypeAndAmountExcludeActCode(vipType, amount, null);
        }
        return paymentDutTypes;
    }

    public PaymentDutType getDutTypeByActCode(String actCode) {
        PaymentDutType paymentDutType = getPaymentDutTypeGateway().getDutTypeByActCode(actCode);
        return paymentDutType;
    }

    public List<PaymentDutType> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        return getPaymentDutTypeGateway().getPayTypeByPayChannel(payChannel, vipType);
    }

    public List<PaymentDutType> getDutTypeByPayType(Integer payType) {
        return getPaymentDutTypeGateway().getDutTypeByPayType(payType);
    }

    /**
     * 通过以下各维度定位一个dut_type，会限制dut_type的有效期
     *
     * @param payChannel    支付渠道
     * @param payType       支付方式
     * @param sourceVipType 源会员类型
     * @param vipType       会员类型
     * @param amount        时长
     * @param actCode       活动code
     * @return dut_type
     */
    public Integer getDutType(Integer payChannel, Long payType, Long sourceVipType, Long vipType, Integer amount,
        String actCode, String agreementActCode, Integer renewPrice, String partnerId) {
        if (Objects.isNull(vipType)) {
            return null;
        }

        // If the product is upgrade, use amount = 1 to query the dut type, since the value of the amount in the
        // upgrade product scenario is various.
        if (sourceVipType != null) {
            amount = 1;
        }

        SortedSet<Integer> dutTypes = null;
        if (Objects.nonNull(payChannel)) {
            dutTypes = getPaymentDutTypeGateway().getDutTypeWithPayChannel(payChannel, sourceVipType, vipType,
                amount, actCode, agreementActCode, renewPrice, partnerId);
        }
        if (dutTypes != null && dutTypes.size() == 1) {
            return dutTypes.first();
        }

        //查出多条信息时通过payType再次路由
        SortedSet<Integer> dutTypesTemp = null;
        if (CollectionUtils.isEmpty(dutTypes) || dutTypes.size() > 1) {
            dutTypesTemp = getPaymentDutTypeGateway().getDutTypeWithPayType(payType, sourceVipType, vipType, amount,
                    actCode, agreementActCode, renewPrice, partnerId);
        }

        if (CollectionUtils.isEmpty(dutTypesTemp) && StringUtils.isNoneEmpty(agreementActCode)) {
            dutTypesTemp = getPaymentDutTypeGateway().getDutTypeWithPayType(payType, sourceVipType, vipType, amount,
                    actCode, null, renewPrice, partnerId);
        }
        if (CollectionUtils.isEmpty(dutTypesTemp) && StringUtils.isNoneEmpty(actCode)) {
            dutTypesTemp = getPaymentDutTypeGateway().getDutTypeWithPayType(payType, sourceVipType, vipType, amount,
                null, agreementActCode, renewPrice, partnerId);
        }
        if (CollectionUtils.isEmpty(dutTypesTemp) && StringUtils.isNoneEmpty(actCode) && StringUtils.isNoneEmpty(agreementActCode)) {
            dutTypesTemp = getPaymentDutTypeGateway().getDutTypeWithPayType(payType, sourceVipType, vipType, amount,
                null, null, renewPrice, partnerId);
        }
        if (CollectionUtils.isEmpty(dutTypesTemp) && StringUtils.isNoneEmpty(partnerId)) {
            dutTypesTemp = getPaymentDutTypeGateway().getDutTypeWithPayType(payType, sourceVipType, vipType, amount,
                    null, null, renewPrice, null);
        }

        if (CollectionUtils.isNotEmpty(dutTypesTemp)) {
            dutTypes = dutTypesTemp;
        }

        if (CollectionUtils.isEmpty(dutTypes)) {
            return null;
        }
        //dut_type是个自增id，最大的肯定为最新的
        return dutTypes.last();
    }

    public AgreementInfo getAgreementInfo(Integer payChannel, Integer payType, String skuId) {
        return getPaymentDutTypeGateway().getAgreementInfo(payChannel, payType, skuId);
    }

    public AgreementInfo getAgreementInfo(Integer payChannel, Long payType, Long sourceVipType, Long vipType,
        Integer amount, String actCode, String agreementActCode, Integer renewPrice, String partnerId) {
        if (Objects.isNull(vipType)) {
            return null;
        }
        //升级场景设置amount为1
        if (sourceVipType != null) {
            amount = 1;
        }
        List<AgreementInfo> agreementInfos = null;
        if (Objects.nonNull(payChannel)) {
            agreementInfos = getPaymentDutTypeGateway().getAgreementInfoWithPayChannel(payChannel, sourceVipType,
                vipType, amount, actCode, agreementActCode, renewPrice, partnerId);
        }
        if (agreementInfos != null && agreementInfos.size() == 1) {
            return agreementInfos.get(0);
        }

        //查出多条信息时通过payType再次路由
        List<AgreementInfo> agreementInfosTemp = getPaymentDutTypeGateway().getAgreementInfoWithPayType(payType, sourceVipType, vipType,
            amount, actCode, agreementActCode, renewPrice, partnerId);
        if (CollectionUtils.isNotEmpty(agreementInfosTemp)) {
            agreementInfos = agreementInfosTemp;
        }
        if (CollectionUtils.isEmpty(agreementInfos)) {
            return null;
        }
        //dut_type是个自增id，最大的肯定为最新的
        return agreementInfos.get(agreementInfos.size() - 1);
    }

    public List<PaymentDutType> getDutTypes(Long payType, Integer vipType, Integer amount) {
        return getPaymentDutTypeGateway().getDutTypes(payType, vipType, amount);
    }

    public List<PaymentDutType> getUniquePaymentDutType(Integer payType, Integer dutType, Integer agreementNo, Integer amount, Long vipType, Integer renewPrice, String actCode, String agreementActCode) {
        return getPaymentDutTypeGateway().getUniquePaymentDutType(payType, dutType, agreementNo, amount, vipType, renewPrice, actCode, agreementActCode);
    }

    public static PaymentDutType of() {
        return new PaymentDutType();
    }

    public PaymentDutTypeGateway getPaymentDutTypeGateway() {
        if (this.paymentDutTypeGateway == null) {
            this.paymentDutTypeGateway = ApplicationContextHelper.getBean(PaymentDutTypeGateway.class);
        }
        return this.paymentDutTypeGateway;
    }


    public List<PaymentDutType> getDutTypeByAgreementActCode(String agreementActCode) {
        return getPaymentDutTypeGateway().getDutTypeByAgreementActCode(agreementActCode);
    }
}
