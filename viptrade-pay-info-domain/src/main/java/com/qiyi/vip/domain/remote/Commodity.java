package com.qiyi.vip.domain.remote;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Commodity {

    private String skuId;
    private String spuId;
    private String skuName;
    private String skuDesc;
    private Date startTime;
    private Date endTime;
    private String catalogId;
    private BigDecimal price;
    private Integer status;
    private Map<String, Object> specAttributes;
    private Map<String, Object> normalAttributes;
    private Date createTime;
    private String creator;
    private Date updateTime;
    private String updater;

    public Long getProductId() {
        String productId = MapUtils.getString(specAttributes, "productId");
        return StringUtils.isNotBlank(productId) ? Long.parseLong(productId) : null;
    }

    public Integer getAutoRenew() {
        String autoRenew = MapUtils.getString(specAttributes, "autoRenew");
        return StringUtils.isNotBlank(autoRenew) ? Integer.parseInt(autoRenew) : null;
    }

    public String getProductCode() {
        String productCode = MapUtils.getString(specAttributes, "productCode");
        return StringUtils.isNotBlank(productCode) ? productCode : null;
    }

    public Long getVipType() {
        String subTypeStr = MapUtils.getString(specAttributes, "subType");
        return StringUtils.isNotBlank(subTypeStr) ? Long.parseLong(subTypeStr) : null;
    }

    public Long getSourceSubType() {
        String sourceSubTypeStr = MapUtils.getString(specAttributes, "sourceSubType");
        return StringUtils.isNotBlank(sourceSubTypeStr) ? Long.parseLong(sourceSubTypeStr) : null;
    }

    public Integer getAmount() {
        String amountStr = MapUtils.getString(specAttributes, "amount");
        return StringUtils.isNotBlank(amountStr) ? Integer.parseInt(amountStr) : null;
    }

    public Integer getIntPrice() {
        return price.intValue();
    }

    /**
     * 时长单位 1-天 2-月 3-小时 4-分钟
     */
    public Integer getPeriodUnit() {
        String periodUnitStr = MapUtils.getString(specAttributes, "periodUnit");
        return StringUtils.isNotBlank(periodUnitStr) ? Integer.parseInt(periodUnitStr) : null;
    }

    /**
     * 商品时长
     */
    public Integer getPeriod() {
        String periodStr = MapUtils.getString(specAttributes, "period");
        return StringUtils.isNotBlank(periodStr) ? Integer.parseInt(periodStr) : null;
    }

    /**
     *  本次需求: IOS端支持首X月Y月, 商品中心将 agreementNo 字段刷为: 协议模板code
     */
    public String getAgreementTemplateCode() {
        String agreementNo = MapUtils.getString(specAttributes, "agreementNo");
        return StringUtils.isNotBlank(agreementNo) ? agreementNo : null;
    }

}