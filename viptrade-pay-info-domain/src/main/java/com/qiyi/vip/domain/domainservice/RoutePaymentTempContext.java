package com.qiyi.vip.domain.domainservice;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.dto.data.RoutePayChannelReqDTO;
import com.qiyi.vip.dto.data.RouteProductReqDTO;

/**
 * <AUTHOR>
 * @description: 支付方式路由处理临时参数信息
 */
@Data
@Builder
public class RoutePaymentTempContext {

    private RouteProductReqDTO productReqDTO;

    private RoutePayChannelReqDTO payChannelReqDTO;

    private PayChannel payChannel;

    private Map<Integer, PaymentType> paymentTypes;
}
