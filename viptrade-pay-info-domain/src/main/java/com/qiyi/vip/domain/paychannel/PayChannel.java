package com.qiyi.vip.domain.paychannel;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.paychannel.gateway.PayChannelGateway;
import com.qiyi.vip.dto.data.AddPayChannelDTO;
import com.qiyi.vip.dto.data.PayChannelDTONew;
import com.qiyi.vip.dto.data.UpdatePayChannelDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 8:54 PM
 */
@Data
public class PayChannel implements Serializable {
    private Long id;
    private String code;
    private String categoryCode;
    private String description;
    private String dutAgreementName;
    private String dutAgreementUrl;
    private String promotionText;
    private String iconUrl;
    private String properties;

    PayChannelGateway payChannelGateway;

    public static PayChannel of() {
        return new PayChannel();
    }

    public List<BusinessChannel> getPayChannelsByBusiness(String code) {
        return getPayChannelGateway().getPayChannelsByBusiness(code);
    }

    public PayChannel getPayChannelByCode(String code) {
        return getPayChannelGateway().getByCode(code);
    }

    public List<PayChannel> getAll() {
        return getPayChannelGateway().getAll();
    }

    public PayChannel getPayChannelById(Integer id) {
        return getPayChannelGateway().getById(id);
    }

    public Map<Long, PayChannel> batchGetByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        return ids.stream().map(this::getPayChannelById).collect(Collectors.toMap(PayChannel::getId, p -> p));
    }

    public Integer getCountByIdOrNameOrCode(Integer id, String name, String code) {
        return getPayChannelGateway().getCountByIdOrNameOrCode(id, name, code);
    }

    public List<PayChannel> getPayChannelByIdOrNameOrCode(Integer id, String name, String code, Integer pageNo, Integer pageSize) {
        return getPayChannelGateway().getByIdOrNameOrCode(id, name, code, pageNo, pageSize);
    }

    // 新增支付渠道
    public boolean addPayChannel(AddPayChannelDTO addPayChannelDTO) {
        return getPayChannelGateway().addPayChannel(addPayChannelDTO);
    }

    private PayChannelGateway getPayChannelGateway() {
        if (this.payChannelGateway == null) {
            this.payChannelGateway = ApplicationContextHelper.getBean(PayChannelGateway.class);
        }
        return this.payChannelGateway;
    }


    public Boolean updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO) {
        return getPayChannelGateway().updatePayChannel(updatePayChannelDTO);
    }


    public List<PayChannel> getTopPayChannels() {
        return getPayChannelGateway().getTopPayChannels();
    }

}
