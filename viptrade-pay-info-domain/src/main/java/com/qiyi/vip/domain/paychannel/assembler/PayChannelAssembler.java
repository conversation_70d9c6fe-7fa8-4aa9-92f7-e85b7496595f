package com.qiyi.vip.domain.paychannel.assembler;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.dto.data.PayChannelDTONew;

/**
 * 支付渠道实体间进行转换
 *
 * <AUTHOR>
 */
@Mapper
public interface PayChannelAssembler {

    PayChannelAssembler INSTANCE = Mappers.getMapper(PayChannelAssembler.class);

    PayChannelDTONew toPayChannel(PayChannel payChannel);

}
