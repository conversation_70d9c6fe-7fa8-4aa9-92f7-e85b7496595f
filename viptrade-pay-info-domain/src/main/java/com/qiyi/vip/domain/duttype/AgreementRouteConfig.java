package com.qiyi.vip.domain.duttype;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.duttype.gateway.AgreementRouteConfigGateway;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgreementRouteConfig implements Serializable {

    private Integer id;

    private Integer agreementNo;

    private Integer agreementType;

    private Integer payChannel;

    private Integer amount;

    private Long sourceVipType;

    private Long vipType;

    private String agreementActCode;

    private Timestamp validStartTime;

    private Timestamp validEndTime;

    private AgreementRouteConfigGateway agreementRouteConfigGateway;

    public static AgreementRouteConfig of() {
        return new AgreementRouteConfig();
    }

    public AgreementRouteConfigGateway getAgreementRouteConfigGateway() {
        if (this.agreementRouteConfigGateway == null) {
            this.agreementRouteConfigGateway = ApplicationContextHelper.getBean(AgreementRouteConfigGateway.class);
        }
        return this.agreementRouteConfigGateway;
    }

    public AgreementRouteConfig getByPayChannelAndVipType(Integer payChannel, Long sourceVipType, Long vipType, Integer amount, String priceActCode) {
        if (payChannel == null || vipType == null || amount == null || StringUtils.isBlank(priceActCode)) {
            return null;
        }
        return getAgreementRouteConfigGateway().getByPayChannelAndVipType(payChannel, sourceVipType, vipType, amount, priceActCode);
    }

}
