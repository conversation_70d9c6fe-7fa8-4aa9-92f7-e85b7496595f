package com.qiyi.vip.domain.domainservice;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import com.qiyi.vip.domain.paytype.PaymentRoute;

/**
 * <AUTHOR> 条件配置实体
 */
@Data
public class ConditionConfig {

    private Condition condition;

    private Integer value;
    public boolean isMatch(RoutePaymentTempContext routePaymentTempContext, PaymentRoute paymentRoute) {
        if (Objects.nonNull(condition)) {
            return condition.hitCondition(routePaymentTempContext, paymentRoute);
        }
        return false;
    }

    @Data
    public static class Condition {

        private String platform;

        private Integer payType;

        private String skuId;


        private boolean equalsPayType(Integer payType) {
            if (Objects.nonNull(this.payType)) {
                return this.payType.equals(payType);
            }
            return true;
        }

        private boolean equalsSkuId(String skuId) {
            if (StringUtils.isNotBlank(this.skuId)) {
                return this.skuId.equals(skuId);
            }
            return true;
        }

        private boolean equalsPlatform(String platform) {
            if (StringUtils.isNotBlank(this.platform)) {
                return this.platform.equals(platform);
            }
            return true;
        }


        public boolean hitCondition(RoutePaymentTempContext routePaymentTempContext, PaymentRoute paymentRoute) {
            if (Objects.isNull(payType) && StringUtils.isBlank(platform) && StringUtils.isBlank(skuId)) {
                return false;
            }
            return this.equalsPayType(paymentRoute.getPayType().intValue())
                && this.equalsPlatform(paymentRoute.getPlatform())
                && this.equalsSkuId(routePaymentTempContext.getProductReqDTO().getSkuId());
        }
    }
}
