package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementCancelRestriction {

    private Integer id;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 时长限制,单位:天
     */
    private Integer duration;

    private Integer status;

}