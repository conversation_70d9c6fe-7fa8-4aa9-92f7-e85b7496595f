package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementMaterial implements Serializable {

    private Integer id;

    private Integer agreementNo;
    /**
     * 协议描述文案
     */
    private String description;
    /**
     * 协议详情页地址
     */
    private String detailUrl;

    private Integer status;

    private AgreementGateway agreementGateway;

    public static AgreementMaterial of() {
        return new AgreementMaterial();
    }

    public AgreementGateway getAgreementGateway() {
        if (this.agreementGateway == null) {
            this.agreementGateway = ApplicationContextHelper.getBean(AgreementGateway.class);
        }
        return agreementGateway;
    }
}