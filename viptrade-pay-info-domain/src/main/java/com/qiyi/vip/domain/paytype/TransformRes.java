package com.qiyi.vip.domain.paytype;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TransformRes {

    /**
     * payType.奇悦支付方式编码
     */
    private Long payType;

    private Integer dutType;
    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 是否首次签约
     */
    private Boolean isFirstSign;

    /**
     * 其次获取signcode，这个免密签约使用，如果没有signcode也会认为无签约关系
     */
    private String contractNo;

    /**
     * 是否返回dutType
     * eg:免密支付时不能返回dutType，该dutType交由支付处理
     * 但签约支付时，已签约的情况下，协议需要正常返回且记录到订单
     */
    private Boolean needReturnDutType = Boolean.TRUE;

    public TransformRes(Long payType) {
        this.payType = payType;
    }
}