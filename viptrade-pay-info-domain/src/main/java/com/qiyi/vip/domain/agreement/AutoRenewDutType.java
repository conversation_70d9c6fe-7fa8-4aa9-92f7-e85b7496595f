package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.domain.constants.PayChannelConstants;
import com.qiyi.vip.utils.DateHelper;

/**
 * @author: guojing
 * @date: 2024/6/24 14:44
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AutoRenewDutType implements Serializable {

    /**
     * id.
     */
    protected Long id;

    /**
     * 代扣方式.
     */
    private Integer dutType;

    /**
     * 代扣方式名称.
     */
    private String name;
    /**
     * 协议类型
     * @see com.qiyi.vip.enums.AgreementTypeEnum
     */
    private Integer agreementType;
    /**
     * 会员类型.
     */
    private Long vipType;

    /**
     * 升级源会员类型.
     */
    private Long sourceVipType;

    /**
     * 支付渠道.
     */
    private Integer payChannel;

    /**
     * 支付渠道名称.
     */
    private String payChannelName;

    /**
     * 支付渠道类型.
     */
    private Integer payChannelType;

    /**
     * 代扣支付方式.
     */
    private Integer dutPayType;

    /**
     * 产品编码.
     */
    private String productCode;

    /**
     * 是否支持切换时长(1:支持,0:不支持)
     */
    private Integer changeAmount;

    /**
     * 是否支持直接取消自动续费(1:支持,0:不支持)
     */
    private Integer directCancel;

    /**
     * 取消自动续费时解绑(0:不解绑，1:解绑)
     */
    private Integer cancelAutorenwUnbind;

    /**
     * 是否支持纯签约(1:支持,0:不支持)
     */
    private Integer supportPureSign;
    /**
     * 优先级(数字越大优先级越高)
     */
    private Short priority;
    /**
     * 状态.
     */
    private Integer status;

    /**
     * 业务方code
     */
    private String businessCode;
    /**
     * 业务方id
     */
    private String partnerId;
    /**
     * 操作人
     */
    private String operator;

    /**
     * 有效开始时间.
     */
    private Timestamp validStartTime;
    /**
     * 有效结束时间.
     */
    private Timestamp validEndTime;

    private AgreementGateway agreementGateway;

    public static AutoRenewDutType of() {
        return new AutoRenewDutType();
    }

    public AgreementGateway getAgreementGateway() {
        if (this.agreementGateway == null) {
            this.agreementGateway = ApplicationContextHelper.getBean(AgreementGateway.class);
        }
        return agreementGateway;
    }

    public static AutoRenewDutType buildIosFrom(Integer dutType, String name, Short priority, AgreementTemplate agreementTemplate) {
        return AutoRenewDutType.builder()
            .dutType(dutType)
            .name(name)
            .agreementType(agreementTemplate.getType())
            .sourceVipType(agreementTemplate.getSourceVipType())
            .vipType(agreementTemplate.getVipType())
            .payChannel(PayChannelConstants.IAP)
            .payChannelName("苹果")
            .payChannelType(PayChannelConstants.PAY_CHANNEL_TYPE_THIRD_DUT_PAY)
            .dutPayType(PayChannelConstants.APPLE_IAP_DUT)
            .productCode(agreementTemplate.getPid())
            .changeAmount(0)
            .directCancel(0)
            .supportPureSign(0)
            .priority(priority)
            .status(1)
            .validStartTime(DateHelper.getCurrentTime())
            .cancelAutorenwUnbind(0)
            .build();
    }

}
