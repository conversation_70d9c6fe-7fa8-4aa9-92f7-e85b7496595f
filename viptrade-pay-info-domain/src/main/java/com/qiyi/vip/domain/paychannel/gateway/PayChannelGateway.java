package com.qiyi.vip.domain.paychannel.gateway;

import java.util.List;

import com.qiyi.vip.domain.paychannel.BusinessChannel;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.dto.data.AddPayChannelDTO;
import com.qiyi.vip.dto.data.UpdatePayChannelDTO;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 8:55 PM
 */
public interface PayChannelGateway {
    PayChannel getById(Integer id);

    PayChannel getByCode(String code);

    List<PayChannel> getAll();

    List<PayChannel> getByIdOrNameOrCode(Integer id, String name, String code, int pageNo, int pageSize);

    boolean addPayChannel(AddPayChannelDTO addPayChannelDTO);

    Boolean updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO);

    List<PayChannel> getTopPayChannels();

    Integer getCountByIdOrNameOrCode(Integer id, String name, String code);

    /**
     * 根据业务编码获取支付渠道列表
     *
     * @param business 业务编码
     * @return 支付渠道列表
     */
    List<BusinessChannel> getPayChannelsByBusiness(String business);
}
