package com.qiyi.vip.domain.paytype;

import com.google.common.base.Splitter;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.qiyi.vip.constant.StatusEnum;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paytype.gateway.PaymentTypeGateway;

/**
 * <AUTHOR>
 * @description: 支付方式域定义
 * @date Create in 17:47 2021/3/5
 */
@ToString
@Data
public class PaymentType implements Serializable {
    public static final int CHARGEBACK_CAN = 1;
    public static final int CHARGEBACK_CANNOT = 0;
    public static final int CHARGEAUTO_CAN = 1;
    public static final int CHARGEAUTO_CANNOT = 0;
    public static final int BACKGROUND_CAN = 1;
    public static final int BACKGROUND_CANNOT = 0;
    public static final int SUPPORT_SIGN = 1;
    public static final int NOT_SUPPORT_SIGN = 0;

    public static final String PAYMENT_TYPE_PROP_TO_PASSWORD_FREE = "toPasswordFree";
    public static final String PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_TYPE = "passwordFreeType";
    public static final String PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_DUT_TYPE = "passwordFreeDutType";
    public static final String PAYMENT_TYPE_PROP_TO_PASSWORD_FREE_AGREEMENT_NO = "passwordFreeAgreementNo";
    public static final String PAYMENT_TYPE_PROP_TO_H5 = "toH5";
    public static final String PAYMENT_TYPE_PROP_H5_TYPE = "h5Type";
    public static final String PASSWORD_FREE_COMMON_PAY_TYPE = "commonPayType";

    private static ConcurrentMap<Long, Map<String, String>> propertiesCache = new ConcurrentHashMap<>();
    private static Map<String, String> emptyProperties = new HashMap<>();

    public PaymentType() {
    }

    /**
     * id
     */
    private Long id;

    /**
     * 是否支持退单
     */
    private Integer isChargeback;
    /**
     * 是否支持自动退款
     */
    private Integer isChargeauto;
    /**
     * 是否支持后台方式退款
     */
    private Integer isBackground;

    private StatusEnum status;
    /**
     * 支付方式对应的支付中心编码
     */
    private String payCenterCode;
    /**
     * 类别,1:在线购买,2:手机支付,3:OTT,4:其他
     */
    private Integer type;
    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 子支付渠道
     */
    private Integer subPayChannel;

    /**
     * 渠道方支持的最大退款时间，以天为单位
     */
    private Integer refundExpireOffset;

    /**
     * 支付方式属性拓展字段
     */
    private String properties;

    /**
     * 申请人
     */
    private String operator;

    private PaymentTypeTransform payTypeTransform;

    /**
     * 部分属性
     */
    private PaymentTypeItem paymentTypeItem;
    /**
     * 支付方式扩展配置
     */
    private PaymentTypeExtends paymentTypeExtend;

    PaymentTypeGateway paymentTypeGateway;

    /**
     * 支付渠道信息
     */
    private PayChannel payChannelItem;

    /**
     * Not thread-safe
     */
    public Map<String, String> getPropertiesMap() {
        if (StringUtils.isEmpty(properties)) {
            return emptyProperties;
        }
        return propertiesCache.computeIfAbsent(id,
                (id) -> Splitter.on(",").withKeyValueSeparator("=").split(properties));
    }


    public PaymentTypeItem getPaymentTypeItem() {
        return paymentTypeItem;
    }

    public void setPaymentTypeItem(PaymentTypeItem paymentTypeItem) {
        this.paymentTypeItem = paymentTypeItem;
    }

    public PaymentTypeExtends getPaymentTypeExtend() {
        return paymentTypeExtend;
    }

    public void setPaymentTypeExtend(PaymentTypeExtends paymentTypeExtend) {
        this.paymentTypeExtend = paymentTypeExtend;
    }


    public Optional<TransformRes> transferPayType(TransformParam transParam) {
        if (Objects.isNull(payTypeTransform)) {
            return Optional.empty();
        }
        transParam.setPayType(id);
        return payTypeTransform.transferPayType(transParam);
    }

    public Optional<Long> transformSignToBasic(boolean isAutoRenew,boolean isBind) {
        if (Objects.isNull(payTypeTransform)) {
            return Optional.empty();
        }
        return payTypeTransform.transformSignToBasic(isAutoRenew, isBind);
    }

    public Optional<TransformRes> transformToPasswordFree(Long userId, String isPasswordFree) {
        if (Objects.isNull(payTypeTransform)) {
            return Optional.empty();
        }
        return payTypeTransform.transformToPasswordFree(id, userId, isPasswordFree);
    }

    public boolean isTransformToPasswordFree() {
        return (Objects.nonNull(payTypeTransform) && payTypeTransform.isTransformToPasswordFree())
            || (Objects.nonNull(payTypeTransform) && payTypeTransform.getIsSupportPasswordFreeSign());
    }

    public static PaymentType of() {
        return new PaymentType();
    }

    public PaymentTypeGateway getPaymentTypeGateway() {
        if (this.paymentTypeGateway == null) {
            this.paymentTypeGateway = ApplicationContextHelper.getBean(PaymentTypeGateway.class);
        }
        return this.paymentTypeGateway;
    }

    public boolean isSupportSign() {
        return Objects.nonNull(payTypeTransform) && payTypeTransform.getIsSupportSign();
    }
}
