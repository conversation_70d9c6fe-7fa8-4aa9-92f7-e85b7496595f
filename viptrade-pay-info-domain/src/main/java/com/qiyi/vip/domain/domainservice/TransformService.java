package com.qiyi.vip.domain.domainservice;

import java.util.Objects;
import java.util.Optional;

import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.constant.OrderPayAutoRenew;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.duttype.AgreementInfo;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.domain.paytype.TransformParam;
import com.qiyi.vip.domain.paytype.TransformRes;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.exception.Assert;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 支付方式转换
 * 包含但不限于以下几种情况的转换
 * -、免密支付相关支付方式间的转换
 * -、是否安装sdk情况下的 sdk/h5支付方式间的转换
 * -、已签约转为普通支付
 * -、普通支付在买自动续费商品时转签约支付
 * @date Create in 17:25 2021/3/10
 */
@Component
public class TransformService {

    public TransformResDTO transform(PayTypeTransforReqDTO transforReqDTO) {
        PaymentType paymentType = PaymentType.of().getPaymentTypeGateway().getById(transforReqDTO.getPayType());
        TransformResDTO transformResDTO = new TransformResDTO(paymentType.getPayChannel(), transforReqDTO.getPayType());
        Assert.notNull(paymentType, ErrorCodeEnum.PARAMETER_ERR.getCode(),"未查询到相关payType!");
        if (paymentType.isTransformToPasswordFree()) {
            Optional<TransformRes> transformResOption = paymentType.transformToPasswordFree(
                    transforReqDTO.getUserId(), transforReqDTO.getIsPasswordFree());

            if (transformResOption.isPresent()) {
                transformResDTO.setPayType(transformResOption.get().getPayType());
                transformResDTO.setDutType(transformResOption.get().getDutType());
                transformResDTO.setAgreementNo(transformResOption.get().getAgreementNo());
                transformResDTO.setContractNo(transformResOption.get().getContractNo());
                return transformResDTO;
            }
        }
        AgreementInfo agreementInfo = getAgreementInfo(transforReqDTO, paymentType);

        if (agreementInfo != null) {
            transformResDTO.setDutType(agreementInfo.getDutType());
            transformResDTO.setAgreementNo(agreementInfo.getAgreementNo());
        }

        //当查询不到代扣方式时，将签约支付方式转换成普通支付方式，否则无法支付成功
        if (Objects.isNull(agreementInfo)) {
            Optional<Long> basicPayType = paymentType.transformSignToBasic(
                    OrderPayAutoRenew.isAutoRenewPayRequest(transforReqDTO.getAutoRenew()), false);
            basicPayType.ifPresent(transformResDTO::setPayType);
            return transformResDTO;
        }
        TransformParam transParam = TransformParam.builder()
            .userId(transforReqDTO.getUserId())
            .dutType(agreementInfo.getDutType())
            .useSdk(transforReqDTO.getUseSDK())
            .isAutoRenew(OrderPayAutoRenew.isAutoRenewPayRequest(transforReqDTO.getAutoRenew()))
            .fee(transforReqDTO.getFee())
            .isPasswordFree(transforReqDTO.getIsPasswordFree())
            .build();
        Optional<TransformRes> transformResOption = paymentType.transferPayType(transParam);

        if (transformResOption.isPresent()) {
            transformResDTO.setPayType(transformResOption.get().getPayType());
            transformResDTO.setIsFirstSign(transformResOption.get().getIsFirstSign());
            if (Boolean.FALSE.equals(transformResOption.get().getNeedReturnDutType())) {
                transformResDTO.setDutType(null);
            }
        }

        return transformResDTO;
    }

    /**
     * 获取代扣方式信息/协议信息
     * @param transferReqDTO 转换请求
     * @param paymentType 支付方式
     * @return 协议/代扣方式信息
     */
    private AgreementInfo getAgreementInfo(PayTypeTransforReqDTO transferReqDTO, PaymentType paymentType) {
        AgreementInfo agreementInfo = null;
        Integer agreementNo = transferReqDTO.getAgreementNo();
        if (agreementNo != null) {
            AgreementNoInfo agreementNoInfo = AgreementNoInfo.of().getAgreementNoById(agreementNo);
            return new AgreementInfo(agreementNoInfo.getDutType(), agreementNo, null, agreementNoInfo.getType());
        }
        if (StringUtils.isNotBlank(transferReqDTO.getSkuId())) {
            //优化通过支付渠道和sku进行精确路由
            agreementInfo = PaymentDutType.of()
                .getAgreementInfo(paymentType.getPayChannel(), paymentType.getId().intValue(), transferReqDTO.getSkuId());
        }

        //通过会员类型等维度再进行路由
        if (Objects.isNull(agreementInfo)) {
            agreementInfo = PaymentDutType.of().getAgreementInfo(
                paymentType.getPayChannel(),
                transferReqDTO.getPayType(),
                transferReqDTO.getSourceVipType(),
                transferReqDTO.getVipType(),
                transferReqDTO.getAmount(),
                transferReqDTO.getActCode(),
                transferReqDTO.getAgreementActCode(),
                null,
                transferReqDTO.getPartnerId());
        }
        return agreementInfo;
    }
}
