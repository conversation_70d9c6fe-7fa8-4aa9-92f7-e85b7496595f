package com.qiyi.vip.domain.domainservice;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

import com.qiyi.vip.domain.paychannel.PayChannel;
import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.dto.data.RoutePaymentInfoDTO;

/**
 * <AUTHOR>
 * @description: 支付方式路由上下文信息
 */
@Data
@Builder
public class RoutePaymentContext {

    /**
     * 请求信息
     */
    private RoutePaymentInfoDTO routePaymentInfo;
    /**
     * 本次请求涉及到的支付渠道信息
     */
    private Map<Long, PayChannel> payChannelMap;

    /**
     * 本次请求涉及到的支付方式信息
     */
    private Map<Integer, List<PaymentType>> payChannelToPaymentTypes;

    /**
     * 本次请求可能会用到的路由信息
     */
    private List<PaymentRoute> paymentRoutes;
}
