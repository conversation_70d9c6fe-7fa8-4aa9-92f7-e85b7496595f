package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;

/**
 * 协议模板价格信息
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementTempPrice implements Serializable {

    private static final int DEFAULT_PERIOD_NO = 1;

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 协议编号
     */
    @Deprecated
    private Integer agreementNo;
    /**
     * 协议模板code
     */
    private String agreementCode;
    /**
     * 支付渠道
     */
    @Deprecated
    private Integer payChannel;
    /**
     * 第*期
     */
    private Integer periodNo;
    /**
     * 扣费金额，单位：分
     */
    private Integer price;
    /**
     * 原价，单位：分
     */
    private Integer originalPrice;
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;

    private AgreementGateway agreementGateway;

    public static AgreementTempPrice of() {
        return new AgreementTempPrice();
    }

    public AgreementGateway getAgreementGateway() {
        if (this.agreementGateway == null) {
            this.agreementGateway = ApplicationContextHelper.getBean(AgreementGateway.class);
        }
        return agreementGateway;
    }

    /**
     * 获取首次续费价格
     */
    public AgreementTempPrice getFirstRenewPrice(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        List<AgreementTempPrice> priceList = getAgreementGateway().getTemplatePrice(agreementNo);
        if (CollectionUtils.isEmpty(priceList)) {
            return null;
        }
        AgreementTempPrice agreementTempPrice = priceList.stream()
            .filter(item -> Objects.equals(DEFAULT_PERIOD_NO, item.getPeriodNo()))
            .findFirst().orElse(null);
        if (agreementTempPrice != null) {
            return agreementTempPrice;
        }
        return priceList.stream()
            .filter(item -> item.getPeriodNo() == null || item.getPeriodNo() == 0)
            .findFirst().orElse(null);
    }

}