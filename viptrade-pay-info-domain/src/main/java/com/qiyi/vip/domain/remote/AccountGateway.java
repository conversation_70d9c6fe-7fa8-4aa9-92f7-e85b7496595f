package com.qiyi.vip.domain.remote;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 15:59 2021/3/10
 */
public interface AccountGateway {

    /**
     * 查询账户的签约关系
     * @param userId 用户id
     * @param dutType
     */
    public Optional<String> getContractNo(Long userId, Integer dutType);

    /**
     * 查询签约号
     */
    public Boolean isAccountBind(Long userId,Integer dutType);
}
