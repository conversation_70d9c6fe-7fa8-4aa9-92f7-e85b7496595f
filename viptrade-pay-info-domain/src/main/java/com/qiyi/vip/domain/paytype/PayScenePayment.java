package com.qiyi.vip.domain.paytype;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.paytype.gateway.PayScenePaymentGateway;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayScenePayment implements Serializable {



    private Integer id;

    /**
     * 支付场景code
     */
    private String sceneCode;
    /**
     * 支付场景名称
     */
    private String sceneName;
    /**
     * 支付环境
     */
    private String sceneEnv;
    /**
     * 拉起支付场景：APP、JSAPI、H5、MINI_PROGRAM
     */
    private String scenario;

    private Integer payChannel;
    /**
     * 普通支付方式
     */
    private Integer basicPayType;
    /**
     * 签约支付方式
     */
    private Integer signPayType;

    private Integer status;

    PayScenePaymentGateway payScenePaymentGateway;

    public static PayScenePayment of() {
        return new PayScenePayment();
    }

    private PayScenePaymentGateway getPayScenePaymentGateway() {
        if (this.payScenePaymentGateway == null) {
            this.payScenePaymentGateway = ApplicationContextHelper.getBean(PayScenePaymentGateway.class);
        }
        return this.payScenePaymentGateway;
    }

    public List<PayScenePayment> getBySceneCode(String sceneCode) {
        return getPayScenePaymentGateway().getBySceneCode(sceneCode);
    }

    public Map<Integer, List<PayScenePayment>> getPayChannelMapBySceneCode(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return Collections.emptyMap();
        }
        List<PayScenePayment> payScenePayments = getPayScenePaymentGateway().getBySceneCode(sceneCode);
        return payScenePayments.stream().collect(Collectors.groupingBy(PayScenePayment::getPayChannel));
    }

}