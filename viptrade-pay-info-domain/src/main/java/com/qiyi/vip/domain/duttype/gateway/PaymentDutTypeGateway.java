package com.qiyi.vip.domain.duttype.gateway;

import com.qiyi.vip.domain.duttype.AgreementInfo;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;

import java.util.List;
import java.util.SortedSet;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 10:50 2021/3/8
 */
public interface PaymentDutTypeGateway {

    /**
     * 该查询不限制有效期时间，按指定dut_type返回相关信息
     * @param vipType 会员类型
     * @param amount 时长
     * @param actCode 活动code
     * @param dutType dutType
     */
    List<PaymentDutType> paymentDutTypeList(Long vipType, Integer amount,
        String actCode, List<Integer> dutType);
    List<PaymentDutType> getPaymentDutTypesByDutType(Integer dutType);

    List<PaymentDutType> getDutTypeByVipTypeAndAmountExcludeActCode(Long vipType, Integer amount,String partnerId);

    PaymentDutType getDutTypeByActCode(String actCode);

    List<PaymentDutType> getPayTypeByPayChannel(Integer payChannel, Integer vipType);

    List<PaymentDutType> getDutTypeByPayType(Integer payType);

    SortedSet<Integer> getDutTypeWithPayChannel(Integer payChannel,
                                                Long sourceVipType,
                                                Long vipType,
                                                Integer amount,
                                                String actCode,
                                                String agreementActCode,
                                                Integer renewPrice,
                                                String partnerId);

    List<AgreementInfo> getAgreementInfoWithPayChannel(Integer payChannel,
                                                Long sourceVipType,
                                                Long vipType,
                                                Integer amount,
                                                String actCode,
                                                String agreementActCode,
                                                Integer renewPrice,
                                                String partnerId);

    SortedSet<Integer> getDutTypeWithPayType(Long payType,
                                             Long sourceVipType,
                                             Long vipType,
                                             Integer amount,
                                             String actCode,
                                             String agreementActCode,
                                             Integer renewPrice,
                                             String partnerId);

    List<AgreementInfo> getAgreementInfoWithPayType(Long payType,
        Long sourceVipType,
        Long vipType,
        Integer amount,
        String actCode,
        String agreementActCode,
        Integer renewPrice,
        String partnerId);

    /**
     * 该查询不限制有效期时间，目前只提供自动续费使用
     * @param payType 支付方式
     * @param vipType 会员类型
     * @param amount 时长
     */
    List<PaymentDutType> getDutTypes(Long payType, Integer vipType, Integer amount);

    boolean addPaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO);

    /**通过几个维度确定一个唯一的映射关系
     * @param payType
     * @param dutType
     * @param agreementNo
     * @param amount
     * @param vipType
     * @param renewPrice
     * @param actCode
     * @return
     */
    List<PaymentDutType> getUniquePaymentDutType(Integer payType, Integer dutType, Integer agreementNo, Integer amount, Long vipType, Integer renewPrice, String actCode, String agreementActCode);

    boolean updatePaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO);

    List<PaymentDutType> getDutTypeByAgreementActCode(String agreementActCode);

    AgreementInfo getAgreementInfo(Integer payChannel, Integer payType, String skuId);
}
