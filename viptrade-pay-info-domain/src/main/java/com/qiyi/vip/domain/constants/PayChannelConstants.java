package com.qiyi.vip.domain.constants;

import com.google.common.collect.Sets;

import java.util.Set;

public class PayChannelConstants {

    /**
     * 支付宝
     */
    public static final int ALIPAY = 1;
    /**
     * 微信
     */
    public static final int WECHAT = 2;
    /**
     * 百度钱包
     */
    public static final int BAIDU = 3;
    /**
     * 手机话费
     */
    public static final int MOBILE = 5;
    /**
     * 苹果应用内支付
     */
    public static final int IAP = 9;
    /**
     * 台湾智付通
     */
    public static final int SPGATEWAY = 10;
    /**
     * Google Billing
     */
    public static final int GOOGLE_BILLING = 11;
    /**
     * PayPal
     */
    public static final int PAYPAL = 12;

    /**
     * 支付渠道类型-会员发起扣款
     */
    public static final int PAY_CHANNEL_TYPE_VIP_DUT_PAY = 1;
    /**
     * 支付渠道类型-由第三方扣款
     */
    public static final int PAY_CHANNEL_TYPE_THIRD_DUT_PAY = 2;

    public static final int APPLE_IAP_DUT = 304; //苹果应用内大陆代扣支付

    public static final String YHK_PAY="YHKPAY";

    /**
     * 扫码支付
     */
    public static final String SCAN_PAY="OTHER";

    public static final Set<Integer> THIRD_PAY_CHANNEL_SET = Sets.newHashSet(IAP, GOOGLE_BILLING, PAYPAL);

}
