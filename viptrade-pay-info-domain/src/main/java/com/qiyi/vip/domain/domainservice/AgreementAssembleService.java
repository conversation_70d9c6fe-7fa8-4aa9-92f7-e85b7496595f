package com.qiyi.vip.domain.domainservice;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.domain.agreement.AgreementNoInfo;
import com.qiyi.vip.domain.agreement.AgreementTempPrice;
import com.qiyi.vip.domain.agreement.AgreementTemplate;
import com.qiyi.vip.domain.agreement.AutoRenewDutType;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.domain.constants.PayChannelConstants;
import com.qiyi.vip.domain.remote.Commodity;
import com.qiyi.vip.domain.remote.VipType;
import com.qiyi.vip.enums.AgreementTypeEnum;
import com.qiyi.vip.exception.BizException;
import com.qiyi.vip.utils.AgreementUtils;

/**
 * @author: guojing
 * @date: 2024/6/24 17:48
 */
@Slf4j
@Component
public class AgreementAssembleService {

    @Resource
    private AgreementGateway agreementGateway;

    public Integer addIos(Integer dutType, Commodity commodity, VipType vipTypeObj) {
        Integer agreementNo = agreementGateway.getDefaultAgreementNoByDutType(dutType, commodity.getAmount(), commodity.getVipType());
        if (agreementNo != null) {
            return agreementNo;
        }

        // 获取协议模板
        AgreementTemplate targetTemplate;
        if (StringUtils.isNotEmpty(commodity.getAgreementTemplateCode())) {
            // 优先使用配置在商品上的协议模板
            targetTemplate = agreementGateway.getAgreementTemplateByCode(commodity.getAgreementTemplateCode());
        } else {
            Long sourceSubType = commodity.getSourceSubType();
            Long vipType = commodity.getVipType();
            Integer amount = commodity.getAmount();
            Integer renewPrice = commodity.getIntPrice();

            List<AgreementTemplate> templates = agreementGateway.getDefaultAgreementTemplate(AgreementTypeEnum.AUTO_RENEW.getValue(), sourceSubType, vipType, amount);
            if (CollectionUtils.isEmpty(templates)) {
                templates = agreementGateway.getDefaultAgreementTemplate(null, sourceSubType, vipType, amount);
                log.info("query other agreement template, sourceSubType:{}, vipType:{}, amount:{}, templates:{}", sourceSubType, vipType, amount, templates);
            }

            targetTemplate = templates.stream()
                .filter(template -> {
                    // code & period_no = 0
                    AgreementTempPrice templatePrice = agreementGateway.getTemplatePriceByCode(template.getCode());
                    return templatePrice != null && templatePrice.getPrice().equals(renewPrice);
                }).findFirst().orElse(null);
        }

        if (targetTemplate == null) {
            throw BizException.newException(ErrorCodeEnum.INSERT_ERR.getCode(), "未找到对应的协议模板");
        }

        // 获取最大优先级
        // 优先取iap渠道的协议中的最大优先级
        List<AgreementNoInfo> agreementNoInfos = agreementGateway.getAgreementNoInfosByTemplateCode(targetTemplate.getCode());
        short maxPriority = agreementNoInfos.stream()
            .filter(agreementNoInfo -> Objects.equals(PayChannelConstants.IAP, agreementNoInfo.getPayChannel()))
            .map(AgreementNoInfo::getPriority)
            .max(Comparator.naturalOrder())
            .orElseGet(() -> agreementNoInfos.stream()
                .map(AgreementNoInfo::getPriority)
                .max(Comparator.naturalOrder())
                .orElse((short) 0));

        Integer amount = commodity.getAmount();
        Integer renewPrice = commodity.getIntPrice();
        String name = AgreementUtils.buildIosAgreementName(vipTypeObj.getName(), amount, renewPrice);

        AutoRenewDutType autoRenewDutType = agreementGateway.getByDutType(dutType);
        if (autoRenewDutType == null) {
            autoRenewDutType = AutoRenewDutType.buildIosFrom(dutType, name, maxPriority, targetTemplate);
        }

        AgreementNoInfo agreementNoInfo = AgreementNoInfo.buildIosFrom(dutType, name, maxPriority, targetTemplate);

        return agreementGateway.addIos(autoRenewDutType, agreementNoInfo);
    }
}
