package com.qiyi.vip.domain.paytype.gateway;

import java.util.List;

import com.qiyi.vip.domain.paytype.PaymentRoute;
import com.qiyi.vip.domain.paytype.PaymentType;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 10:50 2021/3/8
 */
public interface PaymentTypeGateway {
    PaymentType getById(Long id);

    List<PaymentType> getByIds(List<Long> payTypes);

    List<PaymentType> getPasswordFreeSignPayTypesByIds(List<Long> payTypes);

    List<PaymentType> getPayTypeByChannel(Integer channelId, Integer subChannelId, String version);

    List<PaymentType> getPayTypeByChannel(Integer channelId);

    List<PaymentType> getPayTypeByChannels(List<Integer> channelIds);

    boolean add(AddPayTypeDTO addPayTypeDTO);

    Boolean update(UpdatePayTypeDTO updatePayTypeDTO);

    List<PaymentType> getByProperties(QueryPayTypeInfoDTO queryPayTypeInfoDTO);

    List<PaymentType> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryAdminPayTypeInfoDTO);

    Integer countAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO queryAdminPayTypeInfoDTO);

    List<PaymentRoute> getPaymentRoutesByPlatform(String platform);

    List<PaymentType> getAll();
}
