package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 代扣营销活动
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementDutMkt {

    private Integer id;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 活动类型,1:首X期每期Y元
     * @see com.qiyi.vip.enums.DutMktType
     */
    private Integer type;
    /**
     * 活动期数
     */
    private Integer periods;
    /**
     * 代扣活动产品code
     */
    private String pid;
    /**
     * 代扣时长
     */
    private Integer amount;

    private Integer status;

}