package com.qiyi.vip.domain.agreement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.qiyi.vip.constant.StatusEnum;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.domain.constants.PayChannelConstants;
import com.qiyi.vip.utils.AgreementUtils;
import com.qiyi.vip.utils.DateHelper;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AgreementNoInfo implements Serializable {

    /**
     * 协议编号
     */
    private Integer id;
    /**
     * 协议名称
     */
    private String name;
    /**
     * 协议类型
     * @see com.qiyi.vip.enums.AgreementTypeEnum
     */
    private Integer type;
    /**
     * 协议模版code
     */
    private String templateCode;
    /**
     * 是否为兜底协议(正价)，无促销价格的
     * 1：是
     * 0：否
     */
    private Integer defaultNo;
    /**
     * 原会员类型
     */
    private Long sourceVipType;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 签约时长
     */
    private Integer amount;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 支付渠道名称
     */
    private String payChannelName;
    /**
     * 支付渠道类型
     */
    private Integer payChannelType;
    /**
     * 协议优先级
     */
    private Short priority;
    /**
     * 协议对应的代扣方式
     */
    private Integer dutType;
    /**
     * 代扣方式对应的支付方式
     */
    private Integer dutPayType;
    /**
     * 是否支持时长切换
     */
    private Integer changeAmount;
    /**
     * 是否支持直接取消自动续费(1:支持,0:不支持)
     */
    private Integer directCancel;
    /**
     * 是否支持纯签约(1:支持,0:不支持)
     */
    private Integer supportPureSign;
    /**
     * 取消自动续费时解绑支付账号(0:不解绑，1:解绑)
     */
    private Integer cancelAutorenwUnbind;

    private Timestamp validStartTime;

    private Timestamp validEndTime;
    /**
     * 业务方id
     */
    private String partnerId;

    private Integer status;

    private AgreementGateway agreementGateway;

    public static AgreementNoInfo of() {
        return new AgreementNoInfo();
    }

    public AgreementGateway getAgreementGateway() {
        if (this.agreementGateway == null) {
            this.agreementGateway = ApplicationContextHelper.getBean(AgreementGateway.class);
        }
        return agreementGateway;
    }

    public Integer getDefaultAgreementNoByDutType(Integer dutType, Integer amount, Long vipType) {
        return getAgreementGateway().getDefaultAgreementNoByDutType(dutType, amount, vipType);
    }

    public AgreementNoInfo getAgreementNoByCodeAndPayChannel(String templateCode, Integer payChannel, String partnerId, Integer dutType) {
        return getAgreementGateway().getAgreementNoByCodeAndPayChannel(templateCode, payChannel, partnerId, dutType);
    }

    public List<AgreementNoInfo> getAgreementListByDutType(Integer dutType) {
        return getAgreementGateway().getAgreementListByDutType(dutType);
    }

    public AgreementNoInfo getAgreementNoById(Integer id) {
        return getAgreementGateway().getAgreementNoInfo(id);
    }

    public List<AgreementNoInfo> getAgreementNoInfosByType(int type) {
        return getAgreementGateway().getAgreementNoInfosByType(type);
    }

    /**
     * 根据类型、会员类型和是否默认协议获取最高优先级协议模板code
     * @param type 协议类型
     * @param vipType 会员类型
     * @param defaultNo 是否为默认协议
     */
    public List<String> getMaxPriorityTemplateCodeByVipType(Integer type, Long vipType, Integer defaultNo) {
        return getAgreementGateway().getMaxPriorityTemplateCodeByVipType(type, vipType, defaultNo);
    }

    public boolean thirdDut() {
        return PayChannelConstants.PAY_CHANNEL_TYPE_THIRD_DUT_PAY == payChannelType;
    }

    public boolean supportDirectCancel() {
        return directCancel != null && directCancel == 1;
    }

    public boolean isAppleChannel() {
        return this.payChannel != null && this.payChannel == PayChannelConstants.IAP;
    }

    public boolean isGoogleBillingChannel() {
        return this.payChannel != null && this.payChannel == PayChannelConstants.GOOGLE_BILLING;
    }

    public boolean isPayPalChannel() {
        return this.payChannel != null && this.payChannel == PayChannelConstants.PAYPAL;
    }

    public boolean isSpGatewayChannel() {
        return this.payChannel != null && this.payChannel == PayChannelConstants.SPGATEWAY;
    }

    public boolean isMobileChannel() {
        return this.payChannel != null && this.payChannel == PayChannelConstants.MOBILE;
    }

    public boolean isMainlandAlipayChannel() {
        return getPayChannel() != null && getPayChannel() == PayChannelConstants.ALIPAY;
    }

    public boolean supportUnBindWhenCancelAutoRenew() {
        return cancelAutorenwUnbind != null && cancelAutorenwUnbind == 1;
    }

    public boolean supportChangeAmount() {
        return changeAmount != null && changeAmount == 1;
    }

    public boolean supportPureSign() {
        return supportPureSign != null && supportPureSign == 1;
    }

    public boolean invalid(){
        return status == StatusEnum.INVALID.getStatus();
    }

    public boolean online() {
        Timestamp currentTimestamp = DateHelper.getCurrentTime();
        return status == 1 && (validStartTime == null || validStartTime.before(currentTimestamp))
            && (validEndTime == null || validEndTime.after(currentTimestamp));
    }

    public boolean offline() {
        return !online();
    }

    /**
     * 是否为正价协议
     */
    public boolean defaultNo() {
        return defaultNo != null && defaultNo == 1;
    }

    public static AgreementNoInfo buildIosFrom(Integer dutType, String name, Short priority, AgreementTemplate agreementTemplate) {
        Integer agreementNo = AgreementUtils.genAgreementNoByDutType(dutType, agreementTemplate.getAmount());
        return AgreementNoInfo.builder()
            .id(agreementNo)
            .dutType(dutType)
            .name(name)
            .type(agreementTemplate.getType())
            .templateCode(agreementTemplate.getCode())
            .defaultNo(agreementTemplate.getDiscountType() == 0 ? 1 : 0)
            .sourceVipType(agreementTemplate.getSourceVipType())
            .vipType(agreementTemplate.getVipType())
            .amount(agreementTemplate.getAmount())
            .payChannel(PayChannelConstants.IAP)
            .payChannelName("苹果")
            .payChannelType(PayChannelConstants.PAY_CHANNEL_TYPE_THIRD_DUT_PAY)
            .priority(priority)
            .dutType(dutType)
            .dutPayType(PayChannelConstants.APPLE_IAP_DUT)
            .changeAmount(0)
            .directCancel(0)
            .supportPureSign(0)
            .cancelAutorenwUnbind(0)
            .status(1)
            .validStartTime(DateHelper.getCurrentTime())
            .build();
    }

}