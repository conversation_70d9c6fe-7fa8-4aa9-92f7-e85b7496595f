<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.qiyi.vip</groupId>
    <artifactId>viptrade-pay-info</artifactId>
    <version>1.3.50</version>
    <packaging>pom</packaging>
    <name>viptrade-pay-info</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <mybatis-starter.version>2.3.0</mybatis-starter.version>
        <spring-boot.version>2.7.6</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <cola.components.version>4.0.1</cola.components.version>
        <guava.version>25.1-jre</guava.version>
        <commons-lang.version>2.5</commons-lang.version>
        <vip-commons.version>1.0.50-RELEASE</vip-commons.version>
        <jacoco.version>0.8.6</jacoco.version>
        <sentinel.version>1.8.0-iqiyi-4</sentinel.version>
        <aggregate.report.dir>test-report/target/site/jacoco-aggregate/jacoco.xml</aggregate.report.dir>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <mysql-dal-version>1.3.2</mysql-dal-version>
    </properties>

    <modules>
        <module>viptrade-pay-info-client</module>
        <module>viptrade-pay-info-api</module>
        <module>viptrade-pay-info-app</module>
        <module>viptrade-pay-info-domain</module>
        <module>viptrade-pay-info-infrastructure</module>
        <module>viptrade-pay-info-component-dto</module>
        <module>viptrade-pay-info-component-exception</module>
        <module>viptrade-pay-info-component-catchlog</module>
        <module>viptrade-pay-info-commons</module>
        <module>viptrade-pay-info-component-domain</module>
        <module>test-report</module>
        <module>viptrade-pay-info-admin-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.8.0</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.8.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--COLA Components-->
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-component-exception</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-component-dto</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-component-catchlog</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-component-domain</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-commons</artifactId>
                <version>1.3.50</version>
            </dependency>
            <!--COLA Components End-->

            <!--Project modules-->
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-client</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-app</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-domain</artifactId>
                <version>1.3.50</version>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-infrastructure</artifactId>
                <version>1.3.50</version>
            </dependency>
            <!--Project modules End-->
            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-cloud-netflix-eureka-client</artifactId>
                <version>v-1.2.3-boot-2.7.x</version>
            </dependency>
            <!--Spring Boot End-->
            <!--Validation API-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.22.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.web</groupId>
                <artifactId>javax.el</artifactId>
                <version>2.2.6</version>
            </dependency>
            <!--Validation API End -->
            <!-- Misc -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.4</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>
            <!-- Misc End -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.config</groupId>
                <artifactId>config-client</artifactId>
                <version>3.15.7</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>6.1.0-iqiyi-6</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-boot-starter-eagle</artifactId>
                <version>0.2.22-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>2.7.3</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>3.1.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                <version>2.2.10.RELEASE</version>
            </dependency>

            <!-- 核心依赖，必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 簇点链路功能 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 配置中心动态规则管理 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-apollo</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 对接全链路平台Prometheus指标监控 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-metric-prometheus</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel 热点参数限流必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel切面，配合@SentinelResource注解使用 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-httpclient-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- Spring WebMvc jar引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webmvc-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.db</groupId>
                <artifactId>mysql-dal-spring-boot-starter</artifactId>
                <version>${mysql-dal-version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <!-- 注意以下两项的参数配置 -->
                        <source>1.8</source>
                        <target>1.8</target>
                        <!-- 编译参数写在arg内 -->
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <path>
                                <artifactId>lombok</artifactId>
                                <groupId>org.projectlombok</groupId>
                                <version>1.18.24</version>
                            </path>
                            <!-- additional annotation processor required as of Lombok 1.18.16 -->
                            <path>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <groupId>org.projectlombok</groupId>
                                <!-- 如果是0.1.0 有可能出现生成了maptruct的实现类，但该类只创建了对象，没有进行赋值 -->
                                <version>0.2.0</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.1.12.RELEASE</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <includeSystemScope>true</includeSystemScope>
                        <mainClass>com.qiyi.vip.Application</mainClass>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.7.0.1746</version>
                </plugin>

                <!--检查代码覆盖率的插件配置-->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <!-- 增加repository -->
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>libs-release</id>
            <name>libs-release</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>libs-snapshot</id>
            <name>libs-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
        </repository>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>cloudservice-releases</id>
            <name>cloudservice-releases</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>cloudservice-snapshots</id>
            <name>cloudservice-snapshots</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>iqiyi-maven-release</id>
            <name>iqiyi-maven-release</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-release</url>
        </repository>
        <snapshotRepository>
            <id>iqiyi-maven-snapshot</id>
            <name>iqiyi-maven-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-snapshot</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>coverage</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
