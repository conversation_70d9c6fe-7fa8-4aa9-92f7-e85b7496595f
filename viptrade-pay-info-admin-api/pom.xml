<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>viptrade-pay-info</artifactId>
        <groupId>com.qiyi.vip</groupId>
        <version>1.3.50</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>viptrade-pay-info-admin-api</artifactId>
    <packaging>jar</packaging>

    <properties>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/../${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <dependencies>

        <!--springcloud-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iqiyi.v</groupId>
            <artifactId>v-spring-cloud-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qiyi.vip</groupId>
            <artifactId>viptrade-pay-info-app</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iqiyi.v</groupId>
            <artifactId>v-spring-boot-starter-eagle</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>1.0.3-SNAPSHOT</version>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <springmvc>true</springmvc>
                            <locations>
                                <!-- 要扫描的代码位置,跟代码中保持一致 -->
                                <location>com.qiyi</location>
                            </locations>
                            <!-- 一些额外信息，相当于 swagger 中的 ApiInfo -->
                            <info>
                                <title>${project.artifactId}</title>
                                <version>${project.version}</version>
                                <description>${project.artifactId}</description>
                            </info>
                            <outputFormats>json</outputFormats> <!-- 输出格式，json / yaml 或者 json,yaml -->
                            <swaggerDirectory>./target/generated-swagger-api/</swaggerDirectory> <!-- 输出位置 -->
                            <swaggerFileName>${project.artifactId}</swaggerFileName> <!-- 输出的文件名 -->
                            <swaggerApiReader>com.iqiyi.vip.docgen.reader.SpringMvcApiReader</swaggerApiReader>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>