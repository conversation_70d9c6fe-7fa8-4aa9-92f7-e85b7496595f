package com.qiyi.vip.web;

import com.qiyi.vip.api.PaymentDutTypeServiceI;
import com.qiyi.vip.constant.ErrorCodeEnum;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;
import com.qiyi.vip.dto.req.SavePaymentDutTypeReq;
import com.qiyi.vip.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/2 上午 10:02
 */
@RestController
@RequestMapping("/admin/payChannel/paymentDutType")
@Slf4j
public class PaymentDutTypeController {

    @Resource
    PaymentDutTypeServiceI paymentDutTypeServiceI;

    @PostMapping(value = "/add")
    public MultiResponse add(@RequestBody @Validated PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        log.info("[添加支付方式映射关系],请求参数:addPayTypeDTO={}", paymentDutTypeAdminDTO);
        if (paymentDutTypeAdminDTO.getDutType() == null && paymentDutTypeAdminDTO.getAgreementNo() == null) {
            throw new BizException(ErrorCodeEnum.PARAMETER_ERR.getCode(), "dutType和agreementNo不能同时为空");
        }
        return paymentDutTypeServiceI.addPaymentDutType(paymentDutTypeAdminDTO);
    }

    @PostMapping(value = "/save")
    public Response save(@RequestBody @Validated SavePaymentDutTypeReq req) {
        log.info("[savePaymentDutTypeReq],请求参数:req={}", req);
        return paymentDutTypeServiceI.savePaymentDutType(req);
    }

}
