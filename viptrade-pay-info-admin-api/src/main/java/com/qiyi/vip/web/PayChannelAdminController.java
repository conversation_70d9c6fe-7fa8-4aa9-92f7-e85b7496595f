package com.qiyi.vip.web;

import com.qiyi.vip.api.PayChannelServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.data.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description: 支付渠道后台管理相关接口
 * @date 2021/3/4 10:01 PM
 */
@RestController
@RequestMapping("/admin/payChannel")
@Api(value = "支付渠道管理接口")
@Slf4j
public class PayChannelAdminController {
    @Resource
    PayChannelServiceI payChannelService;

    @GetMapping(value = "/list")
    public MultiResponse<PayChannelDTO> list() {
        return payChannelService.list();
    }

    @GetMapping(value = "/listByIdOrNameOrCode")
    public PageResponse<PayChannelDTONew> list(ListByConditionsDTO listByConditionsDTO) {
        log.info("[/admin/payChannel/listByIdOrNameOrCode], 请求参数: {}", listByConditionsDTO);
        return payChannelService.list(listByConditionsDTO);
    }

    @GetMapping(value = "/listTopPayChannels")
    public MultiResponse<PayChannelDTONew> listTopPayChannels() {
        log.info("/admin/payChannel/listTopPayChannels");
        return payChannelService.listTopPayChannels();
    }

    @PostMapping(value = "/add")
    public Response add(@RequestBody AddPayChannelDTO addPayChannelDTO) {
        log.info("[添加支付渠道], 请求参数: {}", addPayChannelDTO);
        return payChannelService.addPayChannel(addPayChannelDTO);
    }

    @PostMapping(value = "/update")
    public Response update(@RequestBody UpdatePayChannelDTO updatePayChannelDTO) {
        log.info("[修改支付渠道], 请求参数: {}", updatePayChannelDTO);
        return payChannelService.updatePayChannel(updatePayChannelDTO);
    }

    @ApiOperation(value = "按业务条件查询渠道接口", notes = "按业务查询支付渠道", httpMethod = "GET")
    @GetMapping(value = "/findByCondition")
    public MultiResponse<PayChannelDTONew> findByCondition(PayChannelByConditionsDTO payChannelByConditionsDTO) {
        return payChannelService.getPayChannelsByBusiness(payChannelByConditionsDTO.getBusiness());
    }
}
