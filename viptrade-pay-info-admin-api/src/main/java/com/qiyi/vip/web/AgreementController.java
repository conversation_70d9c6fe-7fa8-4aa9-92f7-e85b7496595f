package com.qiyi.vip.web;

import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.req.AgreementNoInfoReq;
import com.qiyi.vip.dto.req.AgreementTemplateQueryReq;
import com.qiyi.vip.dto.req.IosAgreementCreateReq;

/**
 * <AUTHOR> @date 2024/1/15 0:04
 */
@Slf4j
@RestController
@RequestMapping("/admin/payChannel/agreement")
public class AgreementController {
    @Resource
    private AgreementServiceI agreementService;

    @GetMapping(value = "/getAgreementInfosByCondition")
    public MultiResponse<AgreementNoInfoDTO> getAgreementInfosByCondition(AgreementNoInfoReq req) {
        log.info("[PayInfoAdmin][getAgreementsByConditions], 请求参数: {}", req);
        return agreementService.getAgreementInfosByCondition(req);
    }

    @PostMapping("/ios/add")
    public Response addIos(@RequestBody @Validated IosAgreementCreateReq createParam) {
        log.info("[addIos], param: {}", createParam);
        return agreementService.addIos(createParam);
    }

    @GetMapping(value = "/getAgreementTemplateBySkuIdentifier")
    public MultiResponse<AgreementTemplateDTO> queryBySkuIdentifier(AgreementTemplateQueryReq req) {
        log.info("[query agreementTemplate by skuIdentifier], param: {}", req);
        return agreementService.queryBySkuIdentifier(req.getSkuIdentifier(), req.getVipType());
    }
}
