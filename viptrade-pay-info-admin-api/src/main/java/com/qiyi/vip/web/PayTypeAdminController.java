package com.qiyi.vip.web;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeByIdsDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import com.qiyi.vip.paytype.PaymentTypeServiceImpl;
import com.qiyi.vip.utils.URLDecodeUtil;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:25 2021/3/8
 */
@RestController
@RequestMapping("/admin/payChannel/payType")
@Slf4j
public class PayTypeAdminController {
    @Resource
    PaymentTypeServiceImpl paymentTypeService;

    @PostMapping(value = "/add")
    public Response add(@RequestBody AddPayTypeDTO addPayTypeDTO) {
        log.info("[添加支付方式],请求参数:addPayTypeDTO={}", addPayTypeDTO);
        return paymentTypeService.addPayType(addPayTypeDTO);
    }

    @PostMapping(value = "/update")
    public Response update(@RequestBody UpdatePayTypeDTO updatePayTypeDTO) {
        log.info("[修改支付方式信息],请求参数:updatePayTypeDTO={}", updatePayTypeDTO);
        return paymentTypeService.updatePayType(updatePayTypeDTO);
    }

    @PostMapping(value = "/list")
    public MultiResponse<PaymentTypeDTO> list() {
        return paymentTypeService.getPaymentTypes();
    }

    @PostMapping(value = "/infos")
    public MultiResponse<PaymentTypeDTO> infos(@RequestBody QueryPayTypeByIdsDTO queryPayTypeByIdsDTO) {
        log.info("[查询支付方式信息],请求参数:queryPayTypeByIdsDTO={}", queryPayTypeByIdsDTO);
        return paymentTypeService.getAdminPaymentTypes(queryPayTypeByIdsDTO.getPayTypes());
    }

    @GetMapping(value = "/infosByCondition")
    public PageResponse<PaymentTypeDTO> infosByCondition(QueryAdminPayTypeInfoDTO conditionDTO) {
        log.info("[按条件查询支付方式信息],请求参数: conditionsDTO={}", conditionDTO);
        if (StringUtils.isNotEmpty(conditionDTO.getName())) {
            conditionDTO.setName(URLDecodeUtil.decodeIfEncoded(conditionDTO.getName()));
        }
        return paymentTypeService.getAdminPayTypesByCondition(conditionDTO);
    }
}
