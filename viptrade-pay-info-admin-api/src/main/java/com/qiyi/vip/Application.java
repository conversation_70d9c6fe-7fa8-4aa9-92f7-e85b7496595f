package com.qiyi.vip;

import com.alibaba.csp.sentinel.init.InitExecutor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import com.qiyi.vip.commons.component.UserTagApi;

/**
 * <AUTHOR>
 * @date 2021/11/25 4:55 下午
 */
@EnableDiscoveryClient
@SpringBootApplication
@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {UserTagApi.class})})
@EnableCaching
public class Application {
    public static void main(String[] args) {
        triggerSentinelInit();
        SpringApplication.run(Application.class, args);
    }

    private static void triggerSentinelInit() {
        new Thread(InitExecutor::doInit).start();
    }
}
