package com.qiyi.vip.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

import com.qiyi.vip.commons.constant.Constants;
import com.qiyi.vip.constant.WebConstants;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.request.RequestWrapper;
import com.qiyi.vip.sign.ParameterSignGenerator;
import com.qiyi.vip.sign.ParameterSignProperties;
import com.qiyi.vip.utils.RequestUtil;

/**
 * <AUTHOR>
 * @date 2021/3/10 9:29 PM
 */
@Setter
@Slf4j
public class SignCheckInterceptor extends HandlerInterceptorAdapter implements Ordered {

    private int order;

    private Map<String, String> channelSignKeyMap = Maps.newHashMap();

    private boolean checkSign = true;


    @Value("${qiyue.gw.sign.key:b27a8e8bba874858893c191f633278c4}")
    private String qiyueGwSignKey;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!checkSign) {
            return true;
        }

        if(isQiyueGwRequest(request)){
            return true;
        }

        if (isGWRequest(request)) {
            return true;
        }

        String signKey = "";
        Map<String, String> paramMap = null;
        if (request.getMethod().equals(HttpMethod.POST.name())) {
            String jsonParam = new RequestWrapper(request).getBodyString();

            ObjectMapper objectMapper = new ObjectMapper();
            //从body里获取的参数
            try {
                paramMap = objectMapper.readValue(jsonParam, Map.class);
                //从配置中心获取key
                String channel = paramMap.get(WebConstants.PARAM_CHANNEL_CODE);
                signKey = getSignKey(channel);
            }catch (Exception e){
                log.info("接口={},签名验证失败,参数转换错误, 请求参数:{}", request.getRequestURI(), jsonParam);
            }
        } else {
            //从配置中心获取key
            String channel = request.getParameter(WebConstants.PARAM_CHANNEL_CODE);
            signKey = getSignKey(channel);
            paramMap = RequestUtil.buildRequestParamMap(request);
        }
        Gson gson = new Gson();
        if(StringUtils.isEmpty(signKey) || null == paramMap || paramMap.isEmpty()){
            log.info("接口={},签名验证失败, 请求参数:{}", request.getRequestURI(), gson.toJson(RequestUtil.buildRequestParamMap(request)));
//            throw ExceptionFactory.bizException("Q00302","签名错误");
            renderString(response);
            return false;
        }

        //为测试录制回放做的需插桩方法
        signKey = getKey(signKey);

        //生成签名
        ParameterSignProperties properties = new ParameterSignProperties();
        properties.setKey(signKey);
//        Set<String> skippedFields = new HashSet<>();
//        skippedFields.add("sign");
//        skippedFields.add("systemCode");
//        skippedFields.add("sys");
//        skippedFields.add("timestamp");
        //properties.setSkippedFields(skippedFields);
        ParameterSignGenerator parameterSignGenerator = new ParameterSignGenerator(properties);
        String sign = parameterSignGenerator.generate(paramMap);

        if (!checkSign(paramMap, sign)) {
            log.info("接口={},签名验证失败, 请求参数:{},正确sign={}",
                    request.getRequestURI(), gson.toJson(RequestUtil.buildRequestParamMap(request)),sign);
//            throw ExceptionFactory.bizException("Q00302","签名错误");
            renderString(response);
            return false;
        }
        return true;
    }

    /**
     * 为测试录制回放做的需插桩方法
     * @param signKey
     * @return
     */
    public String getKey(String signKey){
        return signKey;
    }


    public static boolean checkSign(Map<String, String> params, String sign) {
        if (!MapUtils.isEmpty(params) && !StringUtils.isBlank((CharSequence) params.get("sign"))) {
            String signParam = params.get("sign");
            return signParam.equals(sign);
        } else {
            return false;
        }
    }

    /**
     * 根据渠道获取签名的key
     *
     * @param channel
     * @return
     */
    private String getSignKey(String channel) {
        if (StringUtils.isBlank(channel)) {
            return "";
        }
        if (MapUtils.isEmpty(channelSignKeyMap)) {
            return "";
        }
        return channelSignKeyMap.get(channel);
    }

    private boolean isQiyueGwRequest(HttpServletRequest request) {
        String sys = request.getParameter("sys");
        String paramSign = request.getParameter("sign");
        String timestamp = request.getParameter("timestamp");
        String outId = request.getParameter("outId");
        if (sys == null || paramSign == null || timestamp == null || outId == null) {
            return false;
        }
        String sign = DigestUtils.md5Hex(outId + sys + timestamp + qiyueGwSignKey);
        return sign.equals(paramSign);
    }

    /**
     * 判断是否是网关来的请求
     *
     * @param servletRequest
     * @return
     */
    private boolean isGWRequest(HttpServletRequest servletRequest) {
        String gwHeader = servletRequest.getHeader(Constants.GATEWAY_Header);
        if (StringUtils.isBlank(gwHeader)) {
            return false;
        }
        return Constants.GATEWAY_PWD.equals(gwHeader);
    }

    @Override
    public int getOrder() {
        return order;
    }

    public void setChannelSignKeyMap(Map<String, String> channelSignKeyMap) {
        if (MapUtils.isNotEmpty(channelSignKeyMap)) {
            this.channelSignKeyMap.clear();
            this.channelSignKeyMap.putAll(channelSignKeyMap);
        }
    }

    public void setCheckSign(boolean checkSign) {
        this.checkSign = checkSign;
    }

    public static String renderString(HttpServletResponse response) {
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");

            Response responseR = new Response();
            responseR.setSuccess(false);
            responseR.setCode("Q00302");
            responseR.setErrMessage("签名错误");
            ObjectMapper objectMapper = new ObjectMapper();
            response.flushBuffer();
            response.getWriter().print(objectMapper.writeValueAsString(responseR));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}

