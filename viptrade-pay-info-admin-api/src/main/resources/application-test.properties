application.name=viptrade-pay-info
application.env=TEST
application.region=default

DBM_CONFIG_APPID=qpaas-db-viptrade-pay-info-admin-TEST
APOLLO_PAAS_TOKEN=e3772ab3-d32c-3c0d-5c00-953d4912e36f

mybatis.config-location=classpath:mybatis/mybatis-config.xml

qiyi.domain=http://i.vip.qiyi.com

spring.application.name=viptrade-pay-info-admin-test

# eureka config
eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# 租期更新时间间隔（默认30秒）
eureka.instance.lease-renewal-interval-in-seconds=5
# 租期到期时间（默认90秒）
eureka.instance.lease-expiration-duration-in-seconds=10
# 是否注册到注册中心，如果不需要可以设置为false
eureka.client.register-with-eureka=true
# 注册中心配置
eureka.client.serviceUrl.defaultZone=http://test-eureka.vip.qiyi.domain:8080/eureka/
# 开启健康检查（需要spring-boot-starter-actuator依赖）
eureka.client.healthcheck.enabled=false
spring.cloud.netflix.metrics.enabled=false

# actuator config
management.metrics.enable.all=false
management.endpoint.health.enabled=false
management.endpoint.health.show-details=always
management.endpoints.web.exposure.exclude=
management.health.db.enabled=false
management.health.redis.enabled= false
management.health.defaults.enabled= false
management.info.git.mode= full


paycenter.pwdFree.queryUrl=http://inter-test.account.qiyi.domain/account/dut/pwdFreePayQuery.action
payCenter.sign.key=**********

commodity.center.domain=http://VIP-COMMODITY-CENTER-TEST
commodity.center.sign.key=123456

basic.data.url=http://VIP-BASIC-DATA-TEST/basic-data
basic.data.signKey=123456
