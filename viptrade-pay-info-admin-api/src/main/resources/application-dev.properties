spring.jackson.default-property-inclusion=non_null

DBM_CONFIG_APPID=qpaas-db-viptrade-pay-info-admin-TEST
APOLLO_PAAS_TOKEN=e3772ab3-d32c-3c0d-5c00-953d4912e36f

mybatis.config-location=classpath:mybatis/mybatis-config.xml

qiyi.domain=http://i.vip.qiyi.com

#CloudConfiguration
application.name=viptrade-pay-info
application.env=dev
application.region=default

# eureka config
eureka.instance.hostname=${spring.cloud.client.ip-address}
eureka.instance.non-secure-port=8080
eureka.instance.lease-renewal-interval-in-seconds=3
eureka.instance.lease-expiration-duration-in-seconds=5
#eureka.client.service-url.defaultZone=http://test-eureka.vip.qiyi.domain:8080/eureka/
eureka.client.serviceUrl.defaultZone=http://************:8080/eureka/
ureka.client.healthcheck.enabled=true
hystrix.metrics.enabled=false
#management.metrics.enable.all=false
management.endpoint.health.enabled=false
management.endpoint.health.show-details=always
management.endpoints.web.exposure.exclude=
management.health.db.enabled=false
management.health.redis.enabled= false
management.health.defaults.enabled= false
management.info.git.mode= full

paycenter.pwdFree.queryUrl=http://inter-test.account.qiyi.domain/account/dut/pwdFreePayQuery.action
payCenter.sign.key=**********

commodity.center.domain=http://VIP-COMMODITY-CENTER-TEST
commodity.center.sign.key=123456

basic.data.url=http://VIP-BASIC-DATA-TEST/basic-data
basic.data.signKey=123456
