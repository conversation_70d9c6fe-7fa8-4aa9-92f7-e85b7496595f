image: docker-registry.qiyi.virtual/library/ci-env-jdk8-maven:3.6.0-iqiyi-2
sonarqube_master_job:
  stage: test
  only:
    - master
    - tags
  script:
    - mvn clean --batch-mode verify sonar:sonar  -Dmaven.test.skip=false -Dmaven.test.failure.ignore=true -Dsonar.host.url=http://sonarqube.cloud.qiyi.domain/ -Dsonar.login=**************************************** -Dsonar.projectKey=vip-pay-info -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME


push_atlas_job:
  stage: test
  only:
    - pushes
    - master

  script:
    - echo "enter push_atlas_job"
    - |
      SYSTEM_ID="624fd9cb5908010010e6d96e"  # 填写atlas系统systemId
      ATLAS_API_KEY="0d79ff2fdf82f16ad175373c5e5b7bce" # 填写组内分配的key
      SYSTEM_NAME="viptrade-pay-info-api" # 填写配置生成的json文件名
      FILE_NAME="./target/generated-swagger-api/$SYSTEM_NAME.json"
      # $CI_COMMIT_REF_NAME 为提交分支名
      json_string=$(printf '{"from":"swaggerJson","branch":"%s","conflictStrategy":"skip","systemId":"%s"}' $CI_COMMIT_REF_NAME $SYSTEM_ID)
      
      yum install -y perl
      encoded_json_string=$(echo -n "$json_string" | perl -pe 's/([^-_.~A-Za-z0-9])/sprintf("%%%02X", ord($1))/seg')
      
      mvn clean install -Dmaven.test.skip=true
      response=$(curl --location --request POST "http://api.atlas.qiyi.domain/atlas-sysview/api/interface/interface_import_from_file?contentParam=$encoded_json_string" \
      --header "X-atlas-api-key: $ATLAS_API_KEY" \
      --form "file=@$FILE_NAME")
      echo "HTTP响应结果是: $response"
      
      rm -f "$FILE_NAME"