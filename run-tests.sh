#!/bin/bash

echo "=== 运行协议服务单元测试 ==="
echo "基于当前分支和master的改动，测试新增的isPureSign功能"
echo ""

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}1. 运行AgreementServiceImpl单元测试...${NC}"
mvn test -Dtest=AgreementServiceImplTest -pl viptrade-pay-info-app

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ AgreementServiceImpl单元测试通过${NC}"
else
    echo -e "${RED}✗ AgreementServiceImpl单元测试失败${NC}"
fi

echo ""
echo -e "${YELLOW}2. 运行AgreementQryExe单元测试...${NC}"
mvn test -Dtest=AgreementQryExeTest -pl viptrade-pay-info-app

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ AgreementQryExe单元测试通过${NC}"
else
    echo -e "${RED}✗ AgreementQryExe单元测试失败${NC}"
fi

echo ""
echo -e "${YELLOW}3. 运行集成测试（需要数据库连接）...${NC}"
mvn test -Dtest=AgreementServiceIntegrationTest -pl viptrade-pay-info-app

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 集成测试通过${NC}"
else
    echo -e "${RED}✗ 集成测试失败（可能需要配置数据库连接）${NC}"
fi

echo ""
echo -e "${YELLOW}4. 运行所有协议相关测试...${NC}"
mvn test -Dtest="*Agreement*Test" -pl viptrade-pay-info-app

echo ""
echo "=== 测试完成 ==="
echo "主要测试点："
echo "1. queryBySkuIdentifier方法的isPureSign逻辑（skuIdentifier=5时isPureSign=true）"
echo "2. getAgreementTemplateBySkuIdentifier方法的isPureSign参数传递"
echo "3. 数据库查询的真实执行（集成测试）"
echo "4. 各种skuIdentifier场景的参数映射"