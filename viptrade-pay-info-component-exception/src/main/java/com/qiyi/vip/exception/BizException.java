package com.qiyi.vip.exception;

/**
 * BizException is known Exception, no need retry
 *
 * <AUTHOR>
 */
public class BizException extends BaseException {

    private static final long serialVersionUID = 1L;

    private static final String DEFAULT_ERR_CODE = "BIZ_ERROR";

    /**
     * 用户已签约
     */
    public static final String HAS_SIGN = "HAS_SIGN";
    /**
     * 用户已签约且已开通免密
     */
    public static final String HAS_SIGN_AND_OPEN_PASSWORD_FREE = "HAS_SIGN_AND_PASSWORD_OPEN";

    public BizException(String errMessage) {
        super(DEFAULT_ERR_CODE, errMessage);
    }

    public BizException(String errCode, String errMessage) {
        super(errCode, errMessage);
    }

    public BizException(String errMessage, Throwable e) {
        super(DEFAULT_ERR_CODE, errMessage, e);
    }

    public BizException(String errorCode, String errMessage, Throwable e) {
        super(errorCode, errMessage, e);
    }

    public static BizException newException(String errCode, String errMessage) {
        return new BizException(errCode, errMessage);
    }

}