package com.qiyi.vip.service;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qiyi.vip.sign.ParameterSignGenerator;
import com.qiyi.vip.sign.ParameterSignProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.support.HttpRequestWrapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


public class HttpRequestParameterSignInterceptor implements ClientHttpRequestInterceptor {
    private static final Logger log = LoggerFactory.getLogger(HttpRequestParameterSignInterceptor.class);
    private final ParameterSignProperties properties;
    private final ParameterSignGenerator parameterSignGenerator;

    private List<DataReader> dataReaders;

    private ObjectMapper objectMapper = new ObjectMapper();

    public HttpRequestParameterSignInterceptor(ParameterSignProperties properties) {
        this.properties = properties;
        this.parameterSignGenerator = new ParameterSignGenerator(properties);

        initDataReaders();
    }

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {

        String sign = null;
        if (!isWriteMethod(request)) {
            //get 请求
            Map map = parsURI(request);
            if (Objects.nonNull(map.get("sign"))) {
                return execution.execute(request, body);
            }
            sign = parameterSignGenerator.generate(map);
            if (null != sign) {
                request = new HttpRequestParameterSignInterceptor.SignAppendHttpRequestWrapper(request, sign);
            }

        } else {
            String rawString = new String(body, StandardCharsets.UTF_8);
            try {
                Object signData = null;

                for (DataReader dataReader : dataReaders) {
                    if (dataReader.canRead(request, rawString)) {
                        signData = dataReader.read(rawString);
                    }
                }
                if (signData == null) {
                    throw new IllegalStateException("Cannot find correct data reader for " + rawString);
                }
                sign = parameterSignGenerator.generate(signData);

                if (!StringUtils.isEmpty(sign) && "error".equals(sign)) {
                    log.warn("sign is not null,sign:{}" , sign);
                    return execution.execute(request, body);
                }

                ObjectMapper objectMapper = new ObjectMapper();
                Map map = objectMapper.readValue(rawString, Map.class);
                map.put("sign" , sign);

                if (sign != null) {
                    body = objectMapper.writeValueAsBytes(map);
                }
            } catch (Exception e) {
                log.warn("Generate Sign String failed", e);
            }
        }

        return execution.execute(request, body);
    }

    private Map parsURI(HttpRequest request) {
        URI uri = request.getURI();
        String qr = uri.getQuery();
        if (StringUtils.isEmpty(qr)) {
            return new HashMap();
        }
        String[] pram = qr.split("&");
        if (null == pram || pram.length == 0) {
            return new HashMap();
        }
        Map map = new HashMap();
        for (String p : pram) {
            if (null != p) {
                String[] keyV = p.split("=");
                if (null != keyV && keyV.length == 2) {
                    map.put(keyV[0], keyV[1]);
                } else if (null != keyV && keyV.length == 1){
                    map.put(keyV[0], null);
                }
            }
        }
        return map;
    }

    private boolean isWriteMethod(HttpRequest request) {
        return checkRequestMethod(request, HttpMethod.POST)
                || checkRequestMethod(request, HttpMethod.PUT)
                || checkRequestMethod(request, HttpMethod.DELETE);
    }

    private boolean checkRequestMethod(HttpRequest request, HttpMethod method) {
        return Objects.requireNonNull(request.getMethod()).name().equals(method.name());
    }

    private void initDataReaders() {
        dataReaders = new ArrayList<>();
        dataReaders.add(new HttpRequestParameterSignInterceptor.JsonArrayDataReader());
        dataReaders.add(new HttpRequestParameterSignInterceptor.JsonObjectDataReader());
    }

    private boolean isJsonRequest(HttpRequest request) {
        List<String> contentTypes = request.getHeaders().get(HttpHeaders.CONTENT_TYPE);
        if (CollectionUtils.isEmpty(contentTypes)) {
            return false;
        }

        for (String contentType : contentTypes) {
            if (contentType.contains("json")) {
                return true;
            }
        }

        return false;
    }

    interface DataReader {
        boolean canRead(HttpRequest request, String data);

        Object read(String data);
    }

    private static class SignAppendHttpRequestWrapper extends HttpRequestWrapper {
        private final String sign;

        SignAppendHttpRequestWrapper(HttpRequest request, String sign) {
            super(request);
            this.sign = sign;
        }

        @Override
        public URI getURI() {
            try {
                String uriString = this.getRequest().getURI().toString();
                if (uriString.contains("?")) {
                    uriString += "&sign=" + sign;
                } else {
                    uriString += "?sign=" + sign;
                }
                return new URI(uriString);
            } catch (URISyntaxException e) {
                throw new RuntimeException(e);
            }
        }
    }

    class JsonArrayDataReader implements HttpRequestParameterSignInterceptor.DataReader {
        @Override
        public boolean canRead(HttpRequest request, String data) {
            return isJsonRequest(request) && data.startsWith("[");
        }

        @Override
        public Object read(String data) {
            try {
                return objectMapper.readValue(data, List.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    class JsonObjectDataReader implements HttpRequestParameterSignInterceptor.DataReader {
        @Override
        public boolean canRead(HttpRequest request, String data) {
            return isJsonRequest(request) && data.startsWith("{");
        }

        @Override
        public Object read(String data) {
            try {
                return objectMapper.readValue(data, Map.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
