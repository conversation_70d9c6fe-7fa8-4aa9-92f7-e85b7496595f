package com.qiyi.vip.service;

import com.qiyi.vip.exception.SysException;
import com.qiyi.vip.sign.ParameterSignProperties;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.iqiyi.kit.http.client.spring.ApacheRestTemplateFactoryBean;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/11 5:02 PM
 */
@Slf4j
public abstract class PayInfoAbstractClient implements InitializingBean {

    public String rootUrl;
    public String adminRootUrl;
    public boolean enableSign;
    public String signKey;
    public String channel;
    public Integer connectTimeoutInMillis = 1000;
    public Integer readTimeoutInMillis = 1000;

    public RestTemplate restTemplate;

    @Override
    public void afterPropertiesSet() {
        init();
    }

    public void init() {
        createRestTemplate();
        addParameterSignInterceptor();
        this.cInit();
    }

    private void createRestTemplate() {
        if (null == restTemplate) {
            ApacheRestTemplateFactoryBean restTemplateFactoryBean = new ApacheRestTemplateFactoryBean();
            restTemplateFactoryBean.setConnectTimeoutInMillis(connectTimeoutInMillis);
            restTemplateFactoryBean.setReadTimeoutInMillis(readTimeoutInMillis);
            restTemplateFactoryBean.setMaxPoolSize(200);
            restTemplateFactoryBean.setEnableMonitor(true);
            try {
                restTemplate = restTemplateFactoryBean.getObject();
            } catch (Exception e) {
                log.error("init restTemplate config error", e);
            }
        }
    }

    public abstract void cInit();

    private void addParameterSignInterceptor() {
        ParameterSignProperties properties = new ParameterSignProperties();
        properties.setEnabled(enableSign);
        properties.setKey(signKey);
        restTemplate.getInterceptors().add(new HttpRequestParameterSignInterceptor(properties));
    }

    public <B, D> D request(String url, HttpMethod method, B body,
                            ParameterizedTypeReference<D> typeReference) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<B> httpEntity = new HttpEntity<>(body, headers);

            ResponseEntity<D> entity = restTemplate.exchange(url,
                    method, httpEntity, typeReference);
            return entity.getBody();
        } catch (ResourceAccessException e) {
            log.error("invoke vipTrade-pay-info failed", e);
            throw new SysException("invoke viptrade-pay-info system timeout");
        } catch (Exception e1) {
            log.error("invoke vipTrade-pay-info failed", e1);
            throw new SysException("pay info system error");
        }

    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public void setRootUrl(String rootUrl) {
        this.rootUrl = rootUrl;
    }

    public void setAdminRootUrl(String adminRootUrl) {
        this.adminRootUrl = adminRootUrl;
    }

    public void setEnableSign(boolean enableSign) {
        this.enableSign = enableSign;
    }

    public void setSignKey(String signKey) {
        this.signKey = signKey;
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void setConnectTimeoutInMillis(Integer connectTimeoutInMillis) {
        this.connectTimeoutInMillis = connectTimeoutInMillis;
    }

    public void setReadTimeoutInMillis(Integer readTimeoutInMillis) {
        this.readTimeoutInMillis = readTimeoutInMillis;
    }
}
