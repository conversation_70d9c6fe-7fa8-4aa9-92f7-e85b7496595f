package com.qiyi.vip.service;

import com.google.common.base.Joiner;
import com.qiyi.vip.api.PaymentDutTypeServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;
import com.qiyi.vip.dto.req.SavePaymentDutTypeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;

@Slf4j
public class PaymentDutTypeClient extends PayInfoAbstractClient implements PaymentDutTypeServiceI {

    private String renewPriceUrl;
    private String dutTypesUrl;
    private String dutTypesUrlV2;
    private String dutTypeUrl;

    private String batchDutTypeUrl;
    private String agreementInfoUrl;
    private String wechatAmountUrl;

    private String dutTypeExcludeActCodeUrl;
    private String dutTypeByActCodeUrl;
    private String dutTypeByPayTypeUrl;

    private String payTypeByPayChannelUrl;
    private String addPaymentDutTypeUrl;

    private String savePaymentDutTypeUrl;

    private void buildApiUrls() {
        renewPriceUrl = rootUrl + "payDutType/getRenewPrice";
        dutTypesUrl = rootUrl + "payDutType/getDutTypes";
        dutTypesUrlV2 = dutTypesUrlV2 + "payDutType/getDutTypes/v2";

        dutTypeUrl = rootUrl + "payDutType/getDutType";
        batchDutTypeUrl = rootUrl + "payDutType/batchGetDutType";
        agreementInfoUrl = rootUrl + "payDutType/getAgreementInfo";
        wechatAmountUrl = rootUrl + "payDutType/getWechatAmount";
        dutTypeExcludeActCodeUrl = rootUrl + "payDutType/getDutTypeExcludeActCode";
        dutTypeByActCodeUrl = rootUrl + "payDutType/getDutTypeByActCode";
        dutTypeByPayTypeUrl = rootUrl + "payDutType/getDutTypeByPayType";
        payTypeByPayChannelUrl = rootUrl + "payDutType/getPayTypeByPayChannel";

        addPaymentDutTypeUrl = adminRootUrl + "paymentDutType/add";
        savePaymentDutTypeUrl = adminRootUrl + "paymentDutType/save";
    }

    @Override
    public SingleResponse<PaymentDutTypeDTO> getRenewPrice(Long vipType, Integer amount, String actCode, List<Integer> dutTypes) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(renewPriceUrl);
        urlBuilder.queryParam("vipType", vipType);
        urlBuilder.queryParam("amount", amount);
        urlBuilder.queryParam("actCode", actCode);
        urlBuilder.queryParam("dutTypes", Joiner.on(",").join(dutTypes));
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypes(PaymentDutTypeReqDTO dutTypeReqDTO) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dutTypesUrlV2);
        urlBuilder.queryParam("vipType", dutTypeReqDTO.getVipType());
        urlBuilder.queryParam("amount", dutTypeReqDTO.getAmount());
        urlBuilder.queryParam("actCode", dutTypeReqDTO.getActCode());
        urlBuilder.queryParam("dutTypes", Joiner.on(",").join(dutTypeReqDTO.getDutTypes()));
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public SingleResponse<Integer> getWechatAmountByDutType(Integer dutType) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(wechatAmountUrl);
        urlBuilder.queryParam("dutType", dutType);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<Integer>>() {
                });
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypeExcludeActCode(Long vipType, Integer amount,String partnerId) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dutTypeExcludeActCodeUrl);
        urlBuilder.queryParam("vipType", vipType);
        urlBuilder.queryParam("amount", amount);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public SingleResponse<PaymentDutTypeDTO> getDutTypeByActCode(String actCode) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dutTypeByActCodeUrl);
        urlBuilder.queryParam("actCode", actCode);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(payTypeByPayChannelUrl);
        urlBuilder.queryParam("payChannel", payChannel);
        urlBuilder.queryParam("vipType", vipType);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypeByPayType(Integer payType) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dutTypeByPayTypeUrl);
        urlBuilder.queryParam("payType", payType);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public SingleResponse<Integer> getDutType(PaymentDutTypeReqDTO dutTypeReqDTO) {
        //设置访问参数
        HashMap<String, Object> params = new HashMap<>();
        params.put("_channel", channel);
        params.put("amount", dutTypeReqDTO.getAmount());
        params.put("payChannel", dutTypeReqDTO.getPayChannel());
        params.put("payType", dutTypeReqDTO.getPayType());
        params.put("sourceVipType", dutTypeReqDTO.getSourceVipType());
        params.put("vipType", dutTypeReqDTO.getVipType());
        params.put("actCode", dutTypeReqDTO.getActCode());
        params.put("agreementActCode", dutTypeReqDTO.getAgreementActCode());
        params.put("partnerId", dutTypeReqDTO.getPartnerId());

        return request(dutTypeUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<SingleResponse<Integer>>() {
                });
    }

    @Override
    public MultiResponse<Integer> batchGetDutType(List<PaymentDutTypeReqDTO> dutTypeReqDTOs) {
        if (CollectionUtils.isEmpty(dutTypeReqDTOs)) {
            return MultiResponse.of(Collections.emptyList());
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("_channel", channel);
        List<Map<String, Object>> dutTypeReqList = new ArrayList<>();
        for (PaymentDutTypeReqDTO dutTypeReqDTO : dutTypeReqDTOs) {
            Map<String, Object> dutTypeReq = new HashMap<>();
            dutTypeReq.put("amount", dutTypeReqDTO.getAmount());
            dutTypeReq.put("payChannel", dutTypeReqDTO.getPayChannel() != null ? dutTypeReqDTO.getPayChannel() : null);
            dutTypeReq.put("payType", dutTypeReqDTO.getPayType());
            dutTypeReq.put("sourceVipType", dutTypeReqDTO.getSourceVipType() != null ? dutTypeReqDTO.getSourceVipType() : null);
            dutTypeReq.put("vipType", dutTypeReqDTO.getVipType());
            dutTypeReq.put("actCode", dutTypeReqDTO.getActCode() != null ? dutTypeReqDTO.getActCode() : null);
            dutTypeReq.put("agreementActCode", dutTypeReqDTO.getAgreementActCode() != null ? dutTypeReqDTO.getAgreementActCode() : null);
            dutTypeReq.put("partnerId", dutTypeReqDTO.getPartnerId() != null ? dutTypeReqDTO.getPartnerId() : null);
            dutTypeReqList.add(dutTypeReq);
        }
        params.put("dutTypeReqList", dutTypeReqList);

        return request(batchDutTypeUrl, HttpMethod.POST, params
            , new ParameterizedTypeReference<MultiResponse<Integer>>() {
            });
    }

    @Override
    public SingleResponse<AgreementInfoDTO> getAgreementInfo(PaymentDutTypeReqDTO dutTypeReqDTO) {
        //设置访问参数
        HashMap<String, Object> params = new HashMap<>();
        params.put("_channel", channel);
        params.put("amount", dutTypeReqDTO.getAmount());
        params.put("payChannel", dutTypeReqDTO.getPayChannel());
        params.put("payType", dutTypeReqDTO.getPayType());
        params.put("vipType", dutTypeReqDTO.getVipType());
        if (dutTypeReqDTO.getSourceVipType() != null) {
            params.put("sourceVipType", dutTypeReqDTO.getSourceVipType());
        }
        if (dutTypeReqDTO.getActCode() != null) {
            params.put("actCode", dutTypeReqDTO.getActCode());
        }
        if (dutTypeReqDTO.getAgreementActCode() != null) {
            params.put("agreementActCode", dutTypeReqDTO.getAgreementActCode());
        }
        if (dutTypeReqDTO.getPartnerId() != null) {
            params.put("partnerId", dutTypeReqDTO.getPartnerId());
        }

        return request(agreementInfoUrl, HttpMethod.POST, params, new ParameterizedTypeReference<SingleResponse<AgreementInfoDTO>>() {});
    }

    @Override
    public MultiResponse<PaymentDutTypeDTO> getDutTypes(Long payType, Integer vipType, Integer amount) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dutTypesUrl);
        urlBuilder.queryParam("payType", payType);
        urlBuilder.queryParam("_channel", channel);
        urlBuilder.queryParam("vipType", vipType);
        urlBuilder.queryParam("amount", amount);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<PaymentDutTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse addPaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO) {
        //设置访问参数
        HashMap<String, Object> params = new HashMap<>();
        params.put("_channel", channel);
        params.put("payType", paymentDutTypeAdminDTO.getPayType());
        params.put("dutType", paymentDutTypeAdminDTO.getDutType());
        params.put("agreementNo", paymentDutTypeAdminDTO.getAgreementNo());
        params.put("payChannel", paymentDutTypeAdminDTO.getPayChannel());
        params.put("serviceCode", paymentDutTypeAdminDTO.getServiceCode());
        params.put("amount", paymentDutTypeAdminDTO.getAmount());
        params.put("sourceVipType", paymentDutTypeAdminDTO.getSourceVipType());
        params.put("vipType", paymentDutTypeAdminDTO.getVipType());
        params.put("actCode", paymentDutTypeAdminDTO.getActCode());
        params.put("agreementActCode", paymentDutTypeAdminDTO.getAgreementActCode());
        params.put("renewPrice", paymentDutTypeAdminDTO.getRenewPrice());
        params.put("validStartTime", paymentDutTypeAdminDTO.getValidStartTime());
        params.put("validEndTime", paymentDutTypeAdminDTO.getValidEndTime());
        return request(addPaymentDutTypeUrl, HttpMethod.POST, params
            , new ParameterizedTypeReference<MultiResponse<Integer>>() {
            });
    }

    @Override
    public Response savePaymentDutType(SavePaymentDutTypeReq req) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("agreementNo", req.getAgreementNo());
        params.put("priceActCode", req.getPriceActCode());
        params.put("type", req.getType());
        params.put("validStartTime", req.getValidStartTime());
        params.put("validEndTime", req.getValidEndTime());
        params.put("_channel", channel);

        return request(savePaymentDutTypeUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<Response>() {
                });
    }


    @Override
    public void cInit() {
        buildApiUrls();
    }
}
