package com.qiyi.vip.service;

import com.qiyi.vip.api.PayChannelServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.*;
import com.qiyi.vip.util.MapObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class PayChannelClient extends PayInfoAbstractClient implements PayChannelServiceI {
    private String payInfoUrl;
    private String payChannelListUrl;
    private String smartPayInfoUrl;
    private String routePaymentInfoUrl;
    private String payChannelListByIdOrNameOrCodeURL;
    private String payChannelListTopPayChannelsURL;
    private String payChannelAddURL;
    private String payChannelUpdateURL;

    private String findPayChannelByCondition;

    @Override
    public void cInit() {
        buildApiUrls();
    }

    private void buildApiUrls() {
        payInfoUrl = rootUrl + "payInfo";
        smartPayInfoUrl = rootUrl + "smartPayInfo";
        routePaymentInfoUrl = rootUrl + "routePaymentInfo";

        payChannelListUrl = adminRootUrl + "list";
        payChannelListByIdOrNameOrCodeURL = adminRootUrl + "listByIdOrNameOrCode";
        payChannelListTopPayChannelsURL = adminRootUrl + "listTopPayChannels";
        payChannelAddURL = adminRootUrl + "add";
        payChannelUpdateURL = adminRootUrl + "update";
        findPayChannelByCondition = adminRootUrl + "findByCondition";
    }

    @Override
    public SingleResponse<PayInfoResDTO> payInfo(PayInfoReqDTO payInfoReqDTO) {
        HashMap<String, Object> params = new HashMap<>(6);
        params.put("_channel", channel);
        params.put("userAgent", payInfoReqDTO.getUserAgent());
        params.put("interfaceType", payInfoReqDTO.getInterfaceType());
        params.put("hasPayType", payInfoReqDTO.getHasPayType());
        params.put("products", payInfoReqDTO.getProducts());
        return request(payInfoUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<SingleResponse<PayInfoResDTO>>() {
                });
    }

    @Override
    public SingleResponse<PayInfoResDTO> smartPayInfo(SmartPayInfoReqDTO smartPayInfoReqDTO) {
        HashMap<String, Object> params = new HashMap<>(6);
        params.put("_channel", channel);
        params.put("userAgent", smartPayInfoReqDTO.getUserAgent());
        params.put("storeCode", smartPayInfoReqDTO.getStoreCode());
        params.put("sceneCode", smartPayInfoReqDTO.getSceneCode());
        params.put("clientVersion", smartPayInfoReqDTO.getClientVersion());
        params.put("products", smartPayInfoReqDTO.getProducts());
        return request(smartPayInfoUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<SingleResponse<PayInfoResDTO>>() {
                });
    }

    @Override
    public SingleResponse<RoutePaymentInfoResult> routePaymentInfo(RoutePaymentInfoDTO routePaymentInfo) {
        HashMap<String, Object> params = new HashMap<>(6);
        params.put("_channel", channel);
        params.put("payPlatform", routePaymentInfo.getPayPlatform());
        params.put("clientVersion", routePaymentInfo.getClientVersion());
        params.put("abTestGroup", routePaymentInfo.getAbTestGroup());
        params.put("products", routePaymentInfo.getProducts());
        return request(routePaymentInfoUrl, HttpMethod.POST, params
            , new ParameterizedTypeReference<SingleResponse<RoutePaymentInfoResult>>() {
            });
    }


    @Override
    public MultiResponse<PayChannelDTO> list() {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(payChannelListUrl);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null,
                new ParameterizedTypeReference<MultiResponse<PayChannelDTO>>() {
                });
    }

    @Override
    public PageResponse<PayChannelDTONew> list(ListByConditionsDTO listByConditionsDTO) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(payChannelListByIdOrNameOrCodeURL);
        urlBuilder.queryParam("_channel", channel);
        urlBuilder.queryParam("id", listByConditionsDTO.getId());
        urlBuilder.queryParam("name", listByConditionsDTO.getName());
        urlBuilder.queryParam("code", listByConditionsDTO.getCode());
        urlBuilder.queryParam("pageNo", listByConditionsDTO.getPageNo());
        urlBuilder.queryParam("pageSize", listByConditionsDTO.getPageSize());
        return request(urlBuilder.toUriString(), HttpMethod.GET, null,
                new ParameterizedTypeReference<PageResponse<PayChannelDTONew>>() {
                });
    }

    @Override
    public MultiResponse<PayChannelDTONew> listTopPayChannels() {
        log.info("PayChannelClient.listTopPayChannels");
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(payChannelListTopPayChannelsURL);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null,
                new ParameterizedTypeReference<MultiResponse<PayChannelDTONew>>() {
                });
    }

    @Override
    public Response addPayChannel(AddPayChannelDTO addPayChannelDTO) {
        Map<String, String> params = MapObjUtil.object2Map(addPayChannelDTO);
        params.put("_channel", channel);
        return request(payChannelAddURL, HttpMethod.POST, params,
                new ParameterizedTypeReference<Response>() {
                });
    }

    @Override
    public Response updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO) {
        Map<String, String> params = MapObjUtil.object2Map(updatePayChannelDTO);
        params.put("_channel", channel);
        return request(payChannelUpdateURL, HttpMethod.POST, params,
                new ParameterizedTypeReference<Response>() {
                });
    }

    @Override
    public MultiResponse<PayChannelDTONew> getPayChannelsByBusiness(String business) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(findPayChannelByCondition);
        urlBuilder.queryParam("_channel", channel);
        urlBuilder.queryParam("business", business);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null,
            new ParameterizedTypeReference<MultiResponse<PayChannelDTONew>>() {
            });
    }
}
