package com.qiyi.vip.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementInfoReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.req.AgreementNoInfoReq;
import com.qiyi.vip.dto.req.IosAgreementCreateReq;
import com.qiyi.vip.util.MapObjUtil;

/**
 * @auther: guojing
 * @date: 2023/3/9 5:47 PM
 * @description:
 */
@Slf4j
public class AgreementClient extends PayInfoAbstractClient implements AgreementServiceI {

    private String defaultAgreementNoByDutType;
    private String getAgreementNoByCodeAndPayType;
    private String getAgreementNoInfosURL;
    private String getAgreementInfoURL;
    private String getDutTypeAndIsFirstSignByIdURL;
    private String getAgreementListByDutTypeURL;
    private String getAgreementTemplateBySkuIdentifierURL;
    private String getTemplateByAgreementNo;
    private String getTemplateByCode;

    private String addIosURL;

    @Override
    public void cInit() {
        buildApiUrls();
    }

    private void buildApiUrls() {
        defaultAgreementNoByDutType = rootUrl + "agreement/getDefaultAgreementNoByDutType";
        getAgreementNoByCodeAndPayType = rootUrl + "agreement/getAgreementNoByCodeAndPayType";
        getAgreementListByDutTypeURL = rootUrl + "agreement/getAgreementListByDutType";
        getDutTypeAndIsFirstSignByIdURL = rootUrl + "agreement/getDutTypeAndIsFirstSignById";
        getTemplateByCode = rootUrl + "agreement/getTemplateByCode";
        getTemplateByAgreementNo = rootUrl + "agreement/getTemplateByAgreementNo";
        getAgreementInfoURL = rootUrl + "agreement/getAgreementInfo";

        getAgreementNoInfosURL = adminRootUrl + "agreement/getAgreementInfosByCondition";
        addIosURL = adminRootUrl + "agreement/ios/add";

        getAgreementTemplateBySkuIdentifierURL = adminRootUrl + "agreement/getAgreementTemplateBySkuIdentifier";
    }

    @Override
    public SingleResponse<Integer> getDefaultAgreementNoByDutType(QueryAgreementNoReqDTO param) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(defaultAgreementNoByDutType);
        urlBuilder.queryParam("dutType", param.getDutType());
        urlBuilder.queryParam("amount", param.getAmount());
        if (param.getVipType() != null) {
            urlBuilder.queryParam("vipType", param.getVipType());
        }
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<Integer>>() {
                });
    }

    @Override
    public SingleResponse<AgreementNoInfoDTO> getAgreementNoByCodeAndPayType(QueryAgreementNoByCodeReqDTO param) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getAgreementNoByCodeAndPayType);
        urlBuilder.queryParam("templateCode", param.getTemplateCode());
        urlBuilder.queryParam("payType", param.getPayType());
        urlBuilder.queryParam("dutType", param.getDutType());
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
            , new ParameterizedTypeReference<SingleResponse<AgreementNoInfoDTO>>() {
            });
    }

    @Override
    public MultiResponse<AgreementNoInfoDTO> getAgreementListByDutType(QueryAgreementNoReqDTO param) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getAgreementListByDutTypeURL);
        urlBuilder.queryParam("dutType", param.getDutType());
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
            , new ParameterizedTypeReference<MultiResponse<AgreementNoInfoDTO>>() {
            });
    }

    @Override
    public MultiResponse<AgreementNoInfoDTO> getAgreementInfosByCondition(AgreementNoInfoReq req) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getAgreementNoInfosURL);
        urlBuilder.queryParam("type", req.getType());
        urlBuilder.queryParam("priceActCode", req.getPriceActCode());
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<AgreementNoInfoDTO>>() {
                });
    }

    @Override
    public SingleResponse<TransformResDTO> getDutTypeAndIsFirstSignById(Long userId, Integer agreementNo) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getDutTypeAndIsFirstSignByIdURL);
        urlBuilder.queryParam("userId", userId);
        urlBuilder.queryParam("agreementNo", agreementNo);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<TransformResDTO>>() {
                });
    }

    @Override
    public SingleResponse<AgreementInfoResDTO> getAgreementInfo(QueryAgreementInfoReqDTO param) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getAgreementInfoURL);
        urlBuilder.queryParam("_channel", channel);
        urlBuilder.queryParam("agreementNo", param.getAgreementNo());
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
            , new ParameterizedTypeReference<SingleResponse<AgreementInfoResDTO>>() {
            });
    }

    @Override
    public SingleResponse<Integer> addIos(IosAgreementCreateReq createParam) {
        Map<String, String> params = MapObjUtil.object2Map(createParam);
        params.put("_channel", channel);
        return request(addIosURL, HttpMethod.POST, params,
            new ParameterizedTypeReference<SingleResponse<Integer>>() {});
    }

    @Override
    public MultiResponse<AgreementTemplateDTO> queryBySkuIdentifier(Integer skuIdentifier, Long vipType) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getAgreementTemplateBySkuIdentifierURL);
        urlBuilder.queryParam("skuIdentifier", skuIdentifier);
        if (vipType != null) {
            urlBuilder.queryParam("vipType", vipType);
        }
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<MultiResponse<AgreementTemplateDTO>>() {
                });
    }

    @Override
    public SingleResponse<AgreementTemplateDTO> getTemplateByAgreementNo(Integer agreementNo) {
        if (agreementNo == null) {
            return SingleResponse.of(null);
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getTemplateByAgreementNo);
        urlBuilder.queryParam("agreementNo", agreementNo);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
            , new ParameterizedTypeReference<SingleResponse<AgreementTemplateDTO>>() {
            });
    }

    @Override
    public SingleResponse<AgreementTemplateDTO> getTemplateByCode(String templateCode) {
        if (templateCode == null) {
            return SingleResponse.of(null);
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(getTemplateByCode);
        urlBuilder.queryParam("templateCode", templateCode);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.toUriString(), HttpMethod.GET, null
            , new ParameterizedTypeReference<SingleResponse<AgreementTemplateDTO>>() {
            });
    }
}
