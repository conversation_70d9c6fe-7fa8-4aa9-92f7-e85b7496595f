package com.qiyi.vip.service;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.qiyi.vip.api.PaymentTypeServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import com.qiyi.vip.util.MapObjUtil;

/**
 * <AUTHOR>
 */
public class PaymentTypeClient extends PayInfoAbstractClient implements PaymentTypeServiceI {

    private String infoUrl;
    private String routeUrl;
    private String getPasswordFreeSignInfoUrl;
    private String transformUrl;
    private String addPayTypeUrl;
    private String updatePayTypeUrl;
    private String infosUrl;
    private String payTypeList;
    private String adminPayTypeInfosUrl;
    private String adminPayTypesByConditionUrl;

    @Override
    public void cInit() {
        buildApiUrls();
    }

    private void buildApiUrls() {
        infoUrl = rootUrl + "payType/byIds";
        infosUrl = rootUrl + "payType/infos";
        routeUrl = rootUrl + "payType/route";
        getPasswordFreeSignInfoUrl = rootUrl + "payType/getPasswordFreeSignInfo";
        transformUrl = rootUrl + "payType/transform";

        addPayTypeUrl = adminRootUrl + "payType/add";
        updatePayTypeUrl = adminRootUrl + "payType/update";
        payTypeList = adminRootUrl + "payType/list";
        adminPayTypeInfosUrl = adminRootUrl + "payType/infos";
        adminPayTypesByConditionUrl = adminRootUrl + "payType/infosByCondition";
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes() {
        HashMap<String, Object> params = new HashMap<>(2);
        params.put("_channel", channel);
        return request(payTypeList, HttpMethod.POST, params
                , new ParameterizedTypeReference<MultiResponse<PaymentTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getAdminPaymentTypes(List<Long> payTypes) {
        HashMap<String, Object> params = new HashMap<>(5);
        params.put("_channel", channel);
        params.put("payTypes", payTypes);
        return request(adminPayTypeInfosUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<MultiResponse<PaymentTypeDTO>>() {
                });
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes(List<Long> payTypes) {
        HashMap<String, Object> params = new HashMap<>(2);
        params.put("_channel", channel);
        params.put("payTypes", payTypes);
        return request(infoUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<MultiResponse<PaymentTypeDTO>>() {
                });
    }

    @Override
    public SingleResponse<Long> routePayType(String userAgent, Integer autoRenew) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(routeUrl);
        urlBuilder.queryParam("userAgent", userAgent);
        urlBuilder.queryParam("autoRenew", autoRenew);
        urlBuilder.queryParam("_channel", channel);
        return request(urlBuilder.build().toUriString(), HttpMethod.GET, null
                , new ParameterizedTypeReference<SingleResponse<Long>>() {
                });
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPasswordFreeSignPayTypes(List<Long> payTypes) {
        HashMap<String, Object> params = new HashMap<>(3);
        params.put("_channel", channel);
        params.put("payTypes", payTypes);
        return request(getPasswordFreeSignInfoUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<MultiResponse<PaymentTypeDTO>>() {
                });
    }

    @Override
    public SingleResponse<TransformResDTO> transform(PayTypeTransforReqDTO payTypeTransforReqDTO) {
        HashMap<String, Object> params = new HashMap<>(15);
        params.put("_channel", channel);
        params.put("payType", payTypeTransforReqDTO.getPayType());
        params.put("amount", payTypeTransforReqDTO.getAmount());
        params.put("sourceVipType", payTypeTransforReqDTO.getSourceVipType());
        params.put("vipType", payTypeTransforReqDTO.getVipType());
        params.put("actCode", payTypeTransforReqDTO.getActCode());
        params.put("agreementActCode", payTypeTransforReqDTO.getAgreementActCode());
        params.put("autoRenew", payTypeTransforReqDTO.getAutoRenew());
        params.put("isPasswordFree", payTypeTransforReqDTO.getIsPasswordFree());
        params.put("userId", payTypeTransforReqDTO.getUserId());
        params.put("useSDK", payTypeTransforReqDTO.getUseSDK());
        params.put("partnerId", payTypeTransforReqDTO.getPartnerId());
        params.put("skuId", payTypeTransforReqDTO.getSkuId());
        params.put("agreementNo", payTypeTransforReqDTO.getAgreementNo());
        return request(transformUrl, HttpMethod.POST, params, new ParameterizedTypeReference<SingleResponse<TransformResDTO>>() {
        });
    }

    @Override
    public Response addPayType(AddPayTypeDTO addPayTypeDTO) {
        Map<String, String> params = MapObjUtil.object2Map(addPayTypeDTO);
        params.put("_channel", channel);
        return request(addPayTypeUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<Response>() {
                });
    }

    @Override
    public Response updatePayType(UpdatePayTypeDTO updatePayTypeDTO) {
        Map<String, String> params = MapObjUtil.object2Map(updatePayTypeDTO);
        params.put("_channel", channel);
        return request(updatePayTypeUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<Response>() {
                });
    }

    @Override
    public MultiResponse<PaymentTypeDTO> getPaymentTypes(QueryPayTypeInfoDTO queryPayTypeInfoDTO) {
        Map<String, String> params = MapObjUtil.object2Map(queryPayTypeInfoDTO);
        params.put("_channel", channel);
        return request(infosUrl, HttpMethod.POST, params
                , new ParameterizedTypeReference<MultiResponse<PaymentTypeDTO>>() {
                });
    }

    @Override
    public PageResponse<PaymentTypeDTO> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO conditionDTO) {
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(adminPayTypesByConditionUrl);
        urlBuilder.queryParam("_channel", channel);
        urlBuilder.queryParam("id", conditionDTO.getId());
        urlBuilder.queryParam("name", conditionDTO.getName());
        urlBuilder.queryParam("payCenterCode", conditionDTO.getPayCenterCode());
        urlBuilder.queryParam("isChargeback", conditionDTO.getIsChargeback());
        urlBuilder.queryParam("isChargeauto", conditionDTO.getIsChargeauto());
        urlBuilder.queryParam("status", conditionDTO.getStatus());
        urlBuilder.queryParam("isBackground", conditionDTO.getIsBackground());
        urlBuilder.queryParam("payChannel", conditionDTO.getPayChannel());
        urlBuilder.queryParam("pageNo", conditionDTO.getPageNo());
        urlBuilder.queryParam("pageSize", conditionDTO.getPageSize());
        urlBuilder.queryParam("offset", conditionDTO.getOffset());
        urlBuilder.queryParam("limit", conditionDTO.getLimit());
        return request(urlBuilder.toUriString(), HttpMethod.GET, null,
            new ParameterizedTypeReference<PageResponse<PaymentTypeDTO>>() {
            });
    }
}
