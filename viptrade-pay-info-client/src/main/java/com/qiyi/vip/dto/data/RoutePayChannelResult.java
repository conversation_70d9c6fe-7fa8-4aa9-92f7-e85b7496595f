package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 路由支付方式的结果
 */
@Data
@NoArgsConstructor
public class RoutePayChannelResult {
    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer payChannelId;
    @ApiModelProperty(value = "支付渠道code", required = true)
    private String payChannelCode;
    @ApiModelProperty(value = "子渠道id", required = true)
    private Integer subPayChannelId;
    @ApiModelProperty(value = "渠道名称", required = true)
    private String name;
    @ApiModelProperty(value = "图标")
    private String icon;
    @ApiModelProperty(value = "支付方式", required = true)
    private Long payType;

    /**
     * 支付中心的支付方式编码
     */
    @ApiModelProperty(value = "支付中心编码")
    private String payCenterCode;

    /**
     * 免密支付弹窗上描述文案
     */
    @ApiModelProperty(value = "弹窗描述文案")
    private String tips;
    /**
     * 是否支持免密签约支付
     */
    @ApiModelProperty(value = "是否支持免密支付,true:支持，false:不支持")
    private Boolean isSupportPasswordFreeSign;

    /**
     * 是否支持纯签约支付
     */
    @ApiModelProperty(value = "是否支持纯签约，true:支持，false:不支持")
    private Boolean isSupportPureSign;

    @ApiModelProperty(value = "代扣协议名称")
    private String dutAgreementName;
    @ApiModelProperty(value = "代扣协议地址")
    private String dutAgreementUrl;
    @ApiModelProperty(value = "文案")
    private String promotionText;
    @ApiModelProperty(value = "渠道扩展信息")
    private String channelProperties;


    /**
     * 响应结果类型,兼容过滤写死，不要扩展
     */
    @ApiModelProperty(value = "响应结果类型,兼容过滤写死，不要扩展")
    private String resultType = "json";

    /**
     * ALI  WECHAT
     */
    @ApiModelProperty(value = "扫码支付时支持的渠道，不要扩展，兼容过渡，ALI,WECHAT")
    private String supportType;

    /**
     * 协议信息
     */
    @ApiModelProperty(value = "协议信息")
    private AgreementInfoResDTO agreementInfo;

}
