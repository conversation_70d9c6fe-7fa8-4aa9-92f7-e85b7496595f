package com.qiyi.vip.dto.data;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:53 2021/3/11
 */
@Data
public class ProductReqDTO {
    @ApiModelProperty(value = "唯一标识", required = true)
    private String skuId;
    @ApiModelProperty(value = "自动续费状态", required = true)
    private Integer autoRenew;
    @ApiModelProperty(value = "金额")
    private Integer fee;
    private Integer amount;
    private Long sourceVipType;
    private Long vipType;
    private String priceActCode;
    @ApiModelProperty(value = "渠道信息", required = true)
    private List<PayChannelReqDTO> payChannels;
}
