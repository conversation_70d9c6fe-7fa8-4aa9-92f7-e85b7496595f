package com.qiyi.vip.dto.data;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PayInfoReqDTO {
    /**
     * 客户端标识
     */
    @ApiModelProperty(value = "客户端标识", required = true)
    private String userAgent;
    /**
     * 要返回的接口类型 dopay,scanpay
     */
    @ApiModelProperty(value = "要返回的接口类型", required = false)
    private String interfaceType;

    /**
     * 是否返回payType
     */
    @ApiModelProperty(value = "是否返回payType", required = false, hidden = true)
    private Boolean hasPayType = false;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<ProductReqDTO> products;

}
