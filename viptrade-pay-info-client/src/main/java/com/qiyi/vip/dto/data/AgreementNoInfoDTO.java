package com.qiyi.vip.dto.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/1/15 0:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgreementNoInfoDTO {

    /**
     * 协议编号
     */
    private Integer id;
    /**
     * 协议名称
     */
    private String name;
    /**
     * 协议类型
     */
    private Integer type;
    /**
     * 协议模版code
     */
    private String templateCode;
    /**
     * 是否为兜底协议(正价)，无促销价格的
     * 1：是
     * 0：否
     */
    private Integer defaultNo;
    /**
     * 原会员类型
     */
    private Long sourceVipType;
    /**
     * 会员类型
     */
    private Long vipType;
    /**
     * 签约时长
     */
    private Integer amount;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 支付渠道名称
     */
    private String payChannelName;
    /**
     * 支付渠道类型
     */
    private Integer payChannelType;
    /**
     * 协议优先级
     */
    private Short priority;
    /**
     * 协议对应的代扣方式
     */
    private Integer dutType;
    /**
     * 代扣方式对应的支付方式
     */
    private Integer dutPayType;
    /**
     * 是否支持时长切换
     */
    private Integer changeAmount;
    /**
     * 是否支持直接取消自动续费(1:支持,0:不支持)
     */
    private Integer directCancel;
    /**
     * 是否支持纯签约(1:支持,0:不支持)
     */
    private Integer supportPureSign;
    /**
     * 取消自动续费时解绑支付账号(0:不解绑，1:解绑)
     */
    private Integer cancelAutorenwUnbind;

    private Timestamp validStartTime;

    private Timestamp validEndTime;

    /**
     * 业务方id
     */
    private String partnerId;

    private Integer status;

    /**
     * 首次续费时间，firstRenewDays天后发起代扣。单位：天
     * 下单时传递到支付渠道
     */
    private Integer firstRenewDays;

}
