package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RoutePaymentInfoResult {

    @ApiModelProperty(value = "支付平台")
    private String payPlatform;

    @ApiModelProperty(value = "客户端版本号")
    private String clientVersion;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<RouteProductResResult> products;

    public RoutePaymentInfoResult(String payPlatform, String clientVersion) {
        this.payPlatform = payPlatform;
        this.clientVersion = clientVersion;
    }
}
