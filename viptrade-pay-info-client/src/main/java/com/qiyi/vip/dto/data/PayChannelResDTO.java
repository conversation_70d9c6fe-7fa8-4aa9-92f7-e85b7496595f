package com.qiyi.vip.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class PayChannelResDTO {
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer payChannelId;
    @ApiModelProperty(value = "支付渠道code", required = true)
    private String payChannelCode;
    @ApiModelProperty(value = "子渠道id", required = true)
    private Integer subPayChannelId;
    @ApiModelProperty(value = "渠道名称", required = true)
    private String name;
    @ApiModelProperty(value = "版本", required = true)
    private String version;
    @ApiModelProperty(value = "下单地址", required = true)
    private String url;
    @ApiModelProperty(value = "图标地址")
    private String icon;
    @ApiModelProperty(value = "支付方式", required = true)
    private Long payType;
    /**
     * 支付中心的支付方式编码
     */
    private String payCenterCode;
    /**
     * 免密支付弹窗上描述文案
     */
    private String tips;
    /**
     * 是否支持免密签约支付
     */
    private Boolean isSupportPasswordFreeSign;

    private String scenario;
    private String dutAgreementName;
    private String dutAgreementUrl;
    private String promotionText;
    private String env;
    /**
     * 协议信息
     */
    private AgreementInfoResDTO agreementInfo;


    public PayChannelResDTO(Integer payChannelId, Integer subPayChannelId, String name) {
        this.payChannelId = payChannelId;
        this.subPayChannelId = subPayChannelId;
        this.name = name;
    }
}
