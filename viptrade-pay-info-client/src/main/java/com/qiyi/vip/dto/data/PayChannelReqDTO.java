package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/5 12:30 PM
 */
@Data
public class PayChannelReqDTO {
    /**
     * 渠道id
     */
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer payChannelId;
    /**
     * 子渠道id
     */
    @ApiModelProperty(value = "子渠道id", required = true)
    private Integer subPayChannelId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String version;
    /**
     * 场景
     */
    @ApiModelProperty(value = "场景（拉起方式）", required = true)
    private String scenario;
}
