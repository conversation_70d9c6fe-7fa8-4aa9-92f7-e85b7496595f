package com.qiyi.vip.dto.data;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 11:01 2021/3/8
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaymentTypeExtendsDTO {
    /**
     * id
     */
    protected Long id;
    /**
     * 支付方式
     */
    private Long payType;
    /**
     * 版本号
     */
    private String version;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    public static PaymentTypeExtendsDTO of() {
        return new PaymentTypeExtendsDTO();
    }
}
