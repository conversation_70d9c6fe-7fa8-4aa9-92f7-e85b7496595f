package com.qiyi.vip.dto.data;

import com.google.common.base.Splitter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaymentTypeDTO {

    private static ConcurrentMap<Long, Map<String, String>> propertiesCache = new ConcurrentHashMap<>();
    private static Map<String, String> emptyProperties = new HashMap<>();
    public static final int CHARGEBACK_CAN = 1;
    public static final int CHARGEAUTO_CAN = 1;

    /**
     * id
     */
    protected Long id;
    /**
     * 是否支持退单
     */
    private Integer isChargeback;
    /**
     * 是否支持自动退款
     */
    private Integer isChargeauto;
    /**
     * 是否支持后台方式退款
     */
    private Integer isBackground;
    /**
     * 表明该支付方式是否支持签约支付，即自动续费
     */
    private Boolean isSupportSign = false;
    /**
     * 是否支持纯签约
     */
    private Boolean isSupportPureSign = false;
    /**
     * 是否支持免密支付签约
     */
    private Boolean isSupportPasswordFreeSign = false;

    /**
     * 支付方式对应的支付中心编码
     */
    private String payCenterCode;
    /**
     * 类别,1:在线购买,2:手机支付,3:OTT,4:其他
     */
    private Integer type;


    /**
     * 如果是签约支付，则对应同类型的基本支付
     */
    private Long basicPayTypeId;

    /**
     * 如果是普通支付，则对应同类型的签约支付
     */
    private Long signPayTypeId;
    /**
     * 所对应的纯签约支付方式
     **/
    private Long pureSigningPayTypeId;

    /**
     * 对应的免密支付方式
     */
    private Long passwordFreePayType;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 子支付渠道
     */
    private Integer subPayChannel;
    /**
     * 渠道方支持的最大退款时间，以天为单位
     */
    private Integer refundExpireOffset;

    /**
     * 支付方式属性拓展字段
     */
    private String properties;

    /**
     * 申请人
     */
    private String operator;

    private PaymentTypeExtendsDTO paymentTypeExtendsDTO;

    private PaymentTypeItemDTO paymentTypeItemDTO;

    private PayChannelDTONew payChannelDTO;

    public static PaymentTypeDTO of() {
        return new PaymentTypeDTO();
    }

    /**
     * Not thread-safe
     */
    public Map<String, String> properties() {
        if (Objects.isNull(properties)) {
            return emptyProperties;
        }

        if (StringUtils.isEmpty(properties)) {
            return emptyProperties;
        }

        return propertiesCache.computeIfAbsent(id, (id) -> Splitter.on(",").withKeyValueSeparator("=").split(properties));
    }


    public boolean supportRefund() {
        return isChargeback == CHARGEBACK_CAN;
    }

    /**
     * 是否经过支付中心退款
     * @return
     */
    public boolean isRefundThroughPayCenter() {
        return isChargeauto == CHARGEAUTO_CAN;
    }

}
