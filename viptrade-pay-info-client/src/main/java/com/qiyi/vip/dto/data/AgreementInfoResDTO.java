package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AgreementInfoResDTO {

    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议号")
    private Integer agreementNo;
    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型,1:自动续费 2:芝麻GO 3:微信先看后付")
    private Integer agreementType;
    /**
     * 拉起方式，1:下单; 2:纯签约
     */
    @ApiModelProperty(value = "拉起方式，1下单 2纯签约")
    private Integer pullUp;
    /**
     * 协议描述文案
     */
    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "详情页地址")
    private String detailUrl;

}
