package com.qiyi.vip.dto.data;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TransformResDTO {
    private Integer payChannel;
    /**
     * payType.奇悦支付方式编码
     */
    private Long payType;

    private Integer dutType;
    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 是否首次签约
     */
    private Boolean isFirstSign;

    /**
     * 其次获取signcode，这个免密签约使用，如果没有signcode也会认为无签约关系
     */
    private String contractNo;

    public TransformResDTO(Integer payChannel, Long payType) {
        this.payChannel = payChannel;
        this.payType = payType;
    }

    public TransformResDTO() {
    }
}
