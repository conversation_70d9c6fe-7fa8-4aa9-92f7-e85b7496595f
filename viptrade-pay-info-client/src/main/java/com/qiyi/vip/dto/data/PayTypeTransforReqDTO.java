package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PayTypeTransforReqDTO {


    /**
     * payType.奇悦支付方式编码
     */
    @ApiModelProperty(value = "支付方式", required = true)
    private Long payType;

    /**
     * 商品的sku
     */
    private String skuId;

//    /**
//     * 支付渠道
//     */
//    private Integer payChannel;

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    @ApiModelProperty(value = "数量", required = true)
    private Integer amount;

    /**
     * 升级自动续费源会员类型
     */
    @ApiModelProperty(value = "源会员类型")
    private Long sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    @ApiModelProperty(value = "会员类型", required = true)
    private Long vipType;

    /**
     * 活动编码（fs值）
     */
    @ApiModelProperty(value = "活动编码")
    private String actCode;
    /**
     * 协议活动code
     */
    private String agreementActCode;
    /**
     * 协议编号
     */
    private Integer agreementNo;

    @ApiModelProperty(value = "自动续费状态", required = true)
    private Integer autoRenew;

    /**
     * 是否免密
     */
    @ApiModelProperty(value = "是否免密")
    private String isPasswordFree;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户id", required = true)
    private Long userId;


    /**
     * 需要支付的金额
     */
    @ApiModelProperty(value = "需要支付的金额")
    private Integer fee;

    private String partnerId;

    @ApiModelProperty(value = "是否装了sdk")
    private String useSDK;
}
