package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RoutePaymentInfoDTO {

    @ApiModelProperty(value = "支付平台")
    private String payPlatform;

    @ApiModelProperty(value = "客户端版本号")
    private String clientVersion;

    @ApiModelProperty(value = "ab测试分组")
    private String abTestGroup;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<RouteProductReqDTO> products;

}
