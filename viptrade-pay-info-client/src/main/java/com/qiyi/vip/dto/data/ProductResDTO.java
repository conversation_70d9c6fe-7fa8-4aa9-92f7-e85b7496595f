package com.qiyi.vip.dto.data;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:53 2021/3/11
 */
@Data
@NoArgsConstructor
public class ProductResDTO {
    @ApiModelProperty(value = "唯一标识", required = true)
    private String skuId;
    @ApiModelProperty(value = "自动续费状态", required = true)
    private Integer autoRenew;
    @ApiModelProperty(value = "金额")
    private Integer fee;
    @ApiModelProperty(value = "渠道列表", required = true)
    private List<PayChannelResDTO> payChannels;

    public ProductResDTO(String skuId, Integer autoRenew, Integer fee) {
        this.skuId = skuId;
        this.autoRenew = autoRenew;
        this.fee = fee;
    }

}
