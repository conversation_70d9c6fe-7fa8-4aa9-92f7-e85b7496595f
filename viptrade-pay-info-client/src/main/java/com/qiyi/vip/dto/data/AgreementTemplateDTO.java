package com.qiyi.vip.dto.data;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/10/16 14:41
 */
@Data
public class AgreementTemplateDTO {
    private Integer id;

    private Integer agreementNo;

    private String code;

    private String name;

    private Integer type;

    private Long sourceVipType;

    private Long vipType;

    private String pid;

    private String skuId;

    private String settlementOrderPid;

    private String completeOrderPid;

    private String completeOrderSkuId;

    private Integer amount;

    private Integer periodType;

    private Integer periods;

    private Integer discountType;

    private Integer discountPeriods;

    private Integer periodDuration;

    private Integer periodUnit;

    private Integer pricingStrategy;

    private Integer status;

    private String operator;

    private String attributes;

    private Timestamp createTime;

    private Timestamp updateTime;
}
