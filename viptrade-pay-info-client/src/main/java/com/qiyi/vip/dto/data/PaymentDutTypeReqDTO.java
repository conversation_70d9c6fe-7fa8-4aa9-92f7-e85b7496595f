package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PaymentDutTypeReqDTO {

    /**
     * payType.奇悦支付方式编码
     *
     * @return
     */
    @ApiModelProperty(value = "支付方式", required = true)
    private Long payType;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道id", required = true)
    private Integer payChannel;

    /**
     * 商品的sku
     */
    private String skuId;

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    @ApiModelProperty(value = "数量", required = true)
    private Integer amount;

    /**
     * 升级自动续费源会员类型
     */
    @ApiModelProperty(value = "源会员类型")
    private Long sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    @ApiModelProperty(value = "会员类型", required = true)
    private Long vipType;

    /**
     * 活动编码（fs值）
     */
    @ApiModelProperty(value = "活动编码")
    private String actCode;
    /**
     * 协议活动code
     */
    private String agreementActCode;

    /**
     * 签约价，单位为分
     */
    private Integer renewPrice;

    private String partnerId;

    private List<Integer> dutTypes;
}
