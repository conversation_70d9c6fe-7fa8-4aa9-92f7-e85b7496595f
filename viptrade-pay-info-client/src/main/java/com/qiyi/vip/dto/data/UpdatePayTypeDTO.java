package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UpdatePayTypeDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id", required = true)
    protected Long id;
    /**
     * 是否支持退单
     */
    @ApiModelProperty(value = "是否支持退单", required = true)
    private Integer isChargeback;

    /**
     * 是否支持自动退款
     */
    @ApiModelProperty(value = "是否支持自动退款", required = true)
    private Integer isChargeauto;

    /**
     * 是否支持后台方式退款
     */
    @ApiModelProperty(value = "是否支持后台方式退款", required = true)
    private Integer isBackground;

    /**
     * 表明该支付方式是否支持签约支付，即自动续费
     */
    @ApiModelProperty(value = "该支付方式是否支持签约支付", required = true)
    private Boolean isSupportSign = false;

    /**
     * 是否支持免密支付签约
     */
    @ApiModelProperty(value = "是否支持免密支付签约", required = true)
    private Boolean isSupportPasswordFreeSign = false;

    /**
     * 支付方式状态，0 ： 已下线 1：正常
     */
    @ApiModelProperty(value = "支付方式状态")
    private Integer status;

    /**
     * 支付方式对应的支付中心编码
     */
    @ApiModelProperty(value = "支付方式对应的支付中心编码")
    private String payCenterCode;

    /**
     * 类别,1:在线购买,2:手机支付,3:OTT,4:其他
     */
    @ApiModelProperty(value = "类别")
    private Integer type;

    /**
     * 如果是签约支付，则对应同类型的基本支付
     */
    @ApiModelProperty(value = "如果是签约支付，则对应同类型的基本支付")
    private Long basicPayTypeId;

    /**
     * 如果是普通支付，则对应同类型的签约支付
     */
    @ApiModelProperty(value = "如果是普通支付，则对应同类型的签约支付")
    private Long signPayTypeId;

    /**
     * 所对应的纯签约支付方式
     **/
    @ApiModelProperty(value = "所对应的纯签约支付方式")
    private Long pureSigningPayTypeId;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道", required = true)
    private Integer payChannel;

    /**
     * 子支付渠道
     */
    @ApiModelProperty(value = "子支付渠道")
    private Integer subPayChannel;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 免密支付弹窗上描述文案
     */
    @ApiModelProperty(value = "免密支付弹窗上描述文案")
    private String passwordFreeOpenTips;

    /**
     * 付款授权服务协议名称
     */
    @ApiModelProperty(value = "付款授权服务协议名称")
    private String dutAgreementName;

    /**
     * 付款授权服务协议url
     */
    @ApiModelProperty(value = "付款授权服务协议url")
    private String dutAgreementUrl;
    /**
     * 图标url
     */
    @ApiModelProperty(value = "图标url")
    private String iconUrl;

    /**
     * 支付方式属性拓展字段
     */
    @ApiModelProperty(value = "支付方式属性拓展字段")
    private String properties;

    /**
     * 渠道方支持的最大退款时间，以天为单位
     */
    private Integer refundExpireOffset;

    /**
     * 操作人
     */
    private String operator;
}
