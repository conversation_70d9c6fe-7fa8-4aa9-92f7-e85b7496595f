package com.qiyi.vip.dto.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:53 2021/3/11
 */
@Data
@NoArgsConstructor
public class RouteProductResResult {
    @ApiModelProperty(value = "唯一标识", required = true)
    private String skuId;
    @ApiModelProperty(value = "渠道信息", required = true)
    private List<RoutePayChannelResult> payChannels;

    public RouteProductResResult(String skuId) {
        this.skuId = skuId;
    }
}
