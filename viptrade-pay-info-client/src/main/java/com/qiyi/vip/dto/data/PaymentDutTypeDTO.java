package com.qiyi.vip.dto.data;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/8 7:25 PM
 */
@Data
public class PaymentDutTypeDTO {

    /**
     * id.
     */
    protected Long id;

    /**
     * payType.奇悦支付方式编码
     *
     * @return
     */
    private Integer payType;

    /**
     * dutType.代扣方式编码
     *
     * @return3
     */
    private Integer dutType;
    /**
     * 协议编号
     */
    private Integer agreementNo;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * serviceCode
     *
     * @return
     */
    private String serviceCode;

    /**
     * 续费时长，1：包月；3：包季；12：包年
     */
    private Integer amount;

    /**
     * 升级自动续费源会员类型
     */
    private Long sourceVipType;

    /**
     * 会员类型：1：黄金；3：白银；4：钻石；5：奇异果；6：台湾黄金
     */
    private Long vipType;

    /**
     * 活动编码（fs值）
     */
    private String actCode;
    /**
     * 协议活动code
     */
    private String agreementActCode;

    /**
     * 签约价，单位为分
     */
    private Integer renewPrice;

    /**
     * 有效开始时间
     */
    private Timestamp validStartTime;

    /**
     * 有效结束时间
     */
    private Timestamp validEndTime;

    private String partnerId;
}
