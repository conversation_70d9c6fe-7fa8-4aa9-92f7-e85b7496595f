package com.qiyi.vip.api;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeAdminDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;
import com.qiyi.vip.dto.req.SavePaymentDutTypeReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/8 7:35 PM
 */
public interface PaymentDutTypeServiceI {
    /**
     * 获取签约价，不限制dutType时间，只提供给自动续费使用
     *
     * @param vipType 会员类型
     * @param amount  时长
     * @param actCode 活动code
     */
    SingleResponse<PaymentDutTypeDTO> getRenewPrice(Long vipType, Integer amount, String actCode, List<Integer> dutType);

    /**
     * 获取相关 dutType信息
     */
    MultiResponse<PaymentDutTypeDTO> getDutTypes(PaymentDutTypeReqDTO dutTypeReqDTO);

    /**
     * 获取微信代扣时长
     *
     * @param dutType
     * @return
     */
    SingleResponse<Integer> getWechatAmountByDutType(Integer dutType);

    /**
     * 查询代扣方式排除活动code
     *
     * @param vipType
     * @param amount
     * @return
     */
    MultiResponse<PaymentDutTypeDTO> getDutTypeExcludeActCode(Long vipType, Integer amount,String partnerId);

    /**
     * 根据活动code查询一条代扣信息
     *
     * @param actCode
     * @return
     */
    SingleResponse<PaymentDutTypeDTO> getDutTypeByActCode(String actCode);

    /**
     * 根据频道和会员类型获取代扣信息
     *
     * @param payChannel
     * @param vipType
     * @return
     */
    MultiResponse<PaymentDutTypeDTO> getPayTypeByPayChannel(Integer payChannel, Integer vipType);

    /**
     * 通过paytype获取duttype
     *
     * @param payType
     * @return
     */
    MultiResponse<PaymentDutTypeDTO> getDutTypeByPayType(Integer payType);

    /**
     * * 通过 payType，payChannel，amount，sourceVipType，vipType等维度定位相应的dutType，会限制dut_type的有效期
     */
    SingleResponse<Integer> getDutType(PaymentDutTypeReqDTO dutTypeReqDTO);

    MultiResponse<Integer> batchGetDutType(List<PaymentDutTypeReqDTO> dutTypeReqDTOs);

    SingleResponse<AgreementInfoDTO> getAgreementInfo(PaymentDutTypeReqDTO dutTypeReqDTO);

    /**
     * 通过payType、vipType、amount获取duttype，不限制dutType时间，只提供给自动续费使用
     */
    MultiResponse<PaymentDutTypeDTO> getDutTypes(Long payType, Integer vipType, Integer amount);

    /**
     * 目前提供给国际站使用，用于自动化创建支付方式与dutType映射关系
     * @param paymentDutTypeAdminDTO
     * @return
     */
    MultiResponse addPaymentDutType(PaymentDutTypeAdminDTO paymentDutTypeAdminDTO);

    Response savePaymentDutType(SavePaymentDutTypeReq req);

}
