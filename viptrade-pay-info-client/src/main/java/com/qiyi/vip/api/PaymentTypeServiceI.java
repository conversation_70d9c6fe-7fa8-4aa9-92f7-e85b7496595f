package com.qiyi.vip.api;

import java.util.List;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryAdminPayTypeInfoDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:34 2021/3/8
 */
public interface PaymentTypeServiceI {
    MultiResponse<PaymentTypeDTO> getPaymentTypes(List<Long> payTypes);

    MultiResponse<PaymentTypeDTO> getAdminPaymentTypes(List<Long> payTypes);

    MultiResponse<PaymentTypeDTO> getPaymentTypes();

    MultiResponse<PaymentTypeDTO> getPaymentTypes(QueryPayTypeInfoDTO queryPayTypeInfoDTO);

    PageResponse<PaymentTypeDTO> getAdminPayTypesByCondition(QueryAdminPayTypeInfoDTO conditionDTO);

    /**
     * 支付方式路由
     *
     * @param userAgent 客户端标识
     * @param autoRenew 自动续费状态
     * @return 支付方式
     */
    Response routePayType(String userAgent, Integer autoRenew);

    MultiResponse<PaymentTypeDTO> getPasswordFreeSignPayTypes(List<Long> payTypes);

    SingleResponse<TransformResDTO> transform(PayTypeTransforReqDTO payTypeTransforReqDTO);

    Response addPayType(AddPayTypeDTO addPayTypeDTO);

    Response updatePayType(UpdatePayTypeDTO updatePayTypeDTO);
}
