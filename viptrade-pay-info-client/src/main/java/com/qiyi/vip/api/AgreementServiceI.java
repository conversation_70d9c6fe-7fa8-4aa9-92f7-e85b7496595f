package com.qiyi.vip.api;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementInfoReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.req.AgreementNoInfoReq;
import com.qiyi.vip.dto.req.IosAgreementCreateReq;

/**
 * @auther: guojing
 * @date: 2023/3/9 5:45 PM
 * @description:
 */
public interface AgreementServiceI {

    /**
     * 根据dutType查询正价协议号
     * @param param
     */
    SingleResponse<Integer> getDefaultAgreementNoByDutType(QueryAgreementNoReqDTO param);

    SingleResponse<AgreementNoInfoDTO> getAgreementNoByCodeAndPayType(QueryAgreementNoByCodeReqDTO param);

    MultiResponse<AgreementNoInfoDTO> getAgreementListByDutType(QueryAgreementNoReqDTO param);

    MultiResponse<AgreementNoInfoDTO> getAgreementInfosByCondition(AgreementNoInfoReq req);

    SingleResponse<TransformResDTO> getDutTypeAndIsFirstSignById(Long userId, Integer agreementNo);

    SingleResponse<AgreementInfoResDTO> getAgreementInfo(QueryAgreementInfoReqDTO param);

    SingleResponse<Integer> addIos(IosAgreementCreateReq createParam);

    MultiResponse<AgreementTemplateDTO> queryBySkuIdentifier(Integer skuIdentifier, Long vipType);

    SingleResponse<AgreementTemplateDTO> getTemplateByAgreementNo(Integer agreementNo);

    SingleResponse<AgreementTemplateDTO> getTemplateByCode(String templateCode);

}
