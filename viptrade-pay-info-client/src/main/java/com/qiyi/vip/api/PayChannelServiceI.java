package com.qiyi.vip.api;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.PageResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.*;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/4 9:22 PM
 */
public interface PayChannelServiceI {

    SingleResponse<PayInfoResDTO> payInfo(PayInfoReqDTO payInfoReqDTO);

    SingleResponse<PayInfoResDTO> smartPayInfo(SmartPayInfoReqDTO smartPayInfoReqDTO);

    SingleResponse<RoutePaymentInfoResult> routePaymentInfo(RoutePaymentInfoDTO routePaymentInfo);

    MultiResponse<PayChannelDTO> list();

    PageResponse<PayChannelDTONew> list(ListByConditionsDTO listByConditionsDTO);

    MultiResponse<PayChannelDTONew> listTopPayChannels();

    Response addPayChannel(AddPayChannelDTO addPayChannelDTO);

    Response updatePayChannel(UpdatePayChannelDTO updatePayChannelDTO);

    MultiResponse<PayChannelDTONew> getPayChannelsByBusiness(String business);
}