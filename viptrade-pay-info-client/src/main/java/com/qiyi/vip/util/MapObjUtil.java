package com.qiyi.vip.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class MapObjUtil {
    /**
     * 实体对象转成Map
     *
     * @param obj 实体对象
     */
    public static Map<String, String> object2Map(Object obj) {
        Map<String, String> map = new HashMap<>();
        if (obj == null) {
            return map;
        }
        Class clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                if (Objects.nonNull(field.get(obj))) {
                    map.put(field.getName(), String.valueOf(field.get(obj)));
                }
            }
        } catch (Exception e) {
            log.error("convert object to to map failed,", e);
        }
        return map;
    }
}
