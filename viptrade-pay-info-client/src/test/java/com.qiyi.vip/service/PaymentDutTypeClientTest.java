package com.qiyi.vip.service;

import com.google.common.collect.Lists;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/10 5:32 PM
 */
public class PaymentDutTypeClientTest {

    @Ignore
    @Test
    public void renewPrice() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8089/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();
        SingleResponse<PaymentDutTypeDTO> singleResponse = paymentDutTypeServiceClient.getRenewPrice(1L, 1,
                null, Lists.newArrayList(6));
        Assert.assertFalse(!singleResponse.isSuccess());
    }

    @Ignore
    @Test
    public void getDutTypes() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8089/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        MultiResponse<PaymentDutTypeDTO> multiResponse = paymentDutTypeServiceClient.getDutTypes(65L, 1, 1);
        Assert.assertFalse(!multiResponse.isSuccess());

    }

    @Ignore
    @Test
    public void getDutType() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();
        PaymentDutTypeReqDTO dutTypeReqDTO = new PaymentDutTypeReqDTO();
        dutTypeReqDTO.setActCode("iqiyi_vip_iphone_video_autorenew_tw");
        dutTypeReqDTO.setAmount(127);
        dutTypeReqDTO.setPayType(303L);
        dutTypeReqDTO.setPayChannel(9);
        dutTypeReqDTO.setSourceVipType(127L);
        dutTypeReqDTO.setVipType(1L);
        SingleResponse<Integer> singleResponse = paymentDutTypeServiceClient.getDutType(dutTypeReqDTO);
        Assert.assertFalse(!singleResponse.isSuccess());
    }

    @Test
    public void getAgreementInfo() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        PaymentDutTypeReqDTO dutTypeReqDTO = new PaymentDutTypeReqDTO();
        dutTypeReqDTO.setActCode(null);
        dutTypeReqDTO.setAmount(1);
        dutTypeReqDTO.setPayType(85L);
        dutTypeReqDTO.setPayChannel(1);
        dutTypeReqDTO.setSourceVipType(null);
        dutTypeReqDTO.setVipType(1L);
        SingleResponse<AgreementInfoDTO> agreementInfoResp = paymentDutTypeServiceClient.getAgreementInfo(dutTypeReqDTO);
        SingleResponse<Integer> dutTypeResp = paymentDutTypeServiceClient.getDutType(dutTypeReqDTO);
        Assert.assertTrue(agreementInfoResp.isSuccess());
        Assert.assertNotNull(agreementInfoResp.getData());
        Assert.assertTrue(dutTypeResp.isSuccess());
        Assert.assertNotNull(dutTypeResp.getData());
        Assert.assertEquals(agreementInfoResp.getData().getDutType(), dutTypeResp.getData());

        dutTypeReqDTO.setActCode("2017082341203");
        agreementInfoResp = paymentDutTypeServiceClient.getAgreementInfo(dutTypeReqDTO);
        dutTypeResp = paymentDutTypeServiceClient.getDutType(dutTypeReqDTO);
        Assert.assertTrue(agreementInfoResp.isSuccess());
        Assert.assertTrue(dutTypeResp.isSuccess());
        Assert.assertEquals(agreementInfoResp.getData().getDutType(), dutTypeResp.getData());
    }

    @Ignore
    @Test
    public void getWechatAmountByDutType() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        SingleResponse<Integer> singleResponse = paymentDutTypeServiceClient.getWechatAmountByDutType(15);
        Assert.assertFalse(!singleResponse.isSuccess());
    }

    @Ignore
    @Test
    public void getDutTypeExcludeActCode() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        MultiResponse<PaymentDutTypeDTO> multiResponse = paymentDutTypeServiceClient.getDutTypeExcludeActCode(1L, 1, null);
        Assert.assertFalse(!multiResponse.isSuccess());
    }

    @Ignore
    @Test
    public void getDutTypeByActCode() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        SingleResponse<PaymentDutTypeDTO> singleResponse = paymentDutTypeServiceClient.getDutTypeByActCode("first_renew_tv");
        Assert.assertFalse(!singleResponse.isSuccess());
    }

    @Ignore
    @Test
    public void getPayTypeByPayChannel() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        MultiResponse<PaymentDutTypeDTO> multiResponse = paymentDutTypeServiceClient.getPayTypeByPayChannel(2, 5);
        Assert.assertFalse(!multiResponse.isSuccess());
    }

    @Ignore
    @Test
    public void getDutTypeByPayType() {
        PaymentDutTypeClient paymentDutTypeServiceClient = new PaymentDutTypeClient();
        paymentDutTypeServiceClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentDutTypeServiceClient.setSignKey("1111");
        paymentDutTypeServiceClient.setChannel("asdf");
        paymentDutTypeServiceClient.init();

        MultiResponse<PaymentDutTypeDTO> multiResponse = paymentDutTypeServiceClient.getDutTypeByPayType(65);
        Assert.assertFalse(!multiResponse.isSuccess());
    }
}
