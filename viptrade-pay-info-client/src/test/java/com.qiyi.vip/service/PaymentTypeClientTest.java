package com.qiyi.vip.service;

import java.util.ArrayList;
import java.util.List;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.Response;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AddPayTypeDTO;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.dto.data.UpdatePayTypeDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 13:45 2021/3/18
 */
public class PaymentTypeClientTest {
    PaymentTypeClient paymentTypeClient = new PaymentTypeClient();

    @Before
    public void setUp() {
        paymentTypeClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        paymentTypeClient.setAdminRootUrl("http://127.0.0.1:8089/admin/payChannel/");
        paymentTypeClient.setSignKey("1111");
        paymentTypeClient.setChannel("asdf");
        paymentTypeClient.init();
    }

    @Test
    public void getPaymentTypes() {
        List<Long> payTypes = new ArrayList<>();
        payTypes.add(326L);
        payTypes.add(327L);
        payTypes.add(10021L);
        payTypes.add(10010L);
        payTypes.add(10013L);
        MultiResponse<PaymentTypeDTO> paymentTypes = paymentTypeClient.getPaymentTypes(payTypes);
        Assert.assertTrue(paymentTypes.isSuccess());
        Assert.assertNotNull(paymentTypes.getData());
    }

    @Test
    public void routePayType() {
        SingleResponse<Long> singleResponse = paymentTypeClient.routePayType("micromessenger", 3);
        Assert.assertTrue(singleResponse.isSuccess());
        Assert.assertNotNull(singleResponse.getData());
    }

    @Test
    public void getPasswordFreeSignPayTypes() {
        List<Long> payTypes = new ArrayList<>();
        payTypes.add(420L);
        MultiResponse<PaymentTypeDTO> multiResponse = paymentTypeClient.getPasswordFreeSignPayTypes(payTypes);
        Assert.assertTrue(multiResponse.isSuccess());
        Assert.assertNotNull(multiResponse.getData());
    }

    @Test
    public void transform() {
        PayTypeTransforReqDTO transforReqDTO = new PayTypeTransforReqDTO();
        transforReqDTO.setPayType(301L);
        transforReqDTO.setAmount(1);
        transforReqDTO.setVipType(5L);
        transforReqDTO.setUserId(1L);
        transforReqDTO.setAutoRenew(1);
        transforReqDTO.setPartnerId("PaytoTCL");

        SingleResponse<TransformResDTO> response = paymentTypeClient.transform(transforReqDTO);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getData());
    }

    @Test
    public void transformNew() {
        PayTypeTransforReqDTO transforReqDTO = new PayTypeTransforReqDTO();
        transforReqDTO.setPayType(384L);
        transforReqDTO.setAmount(1);
        transforReqDTO.setVipType(54L);
        transforReqDTO.setSourceVipType(null);
        transforReqDTO.setActCode(null);
        transforReqDTO.setAgreementActCode(null);
        transforReqDTO.setUserId(1480392753L);
        transforReqDTO.setAutoRenew(3);

        SingleResponse<TransformResDTO> response = paymentTypeClient.transform(transforReqDTO);
        System.out.println(response);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getData());
    }

    @Test
    public void addPayType() {
        AddPayTypeDTO addPayTypeDTO = new AddPayTypeDTO();
        addPayTypeDTO.setPayChannel(6);

        Response response = paymentTypeClient.addPayType(addPayTypeDTO);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void updatePayType() {
        UpdatePayTypeDTO updatePayTypeDTO = new UpdatePayTypeDTO();

        Response response = paymentTypeClient.updatePayType(updatePayTypeDTO);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void getPaymentTypesByPayCenterCode() {
        QueryPayTypeInfoDTO queryPayTypeInfoDTO = new QueryPayTypeInfoDTO();
        queryPayTypeInfoDTO.setPayCenterCode("PAYPALAPP_DUT");
        MultiResponse<PaymentTypeDTO> response = paymentTypeClient.getPaymentTypes(queryPayTypeInfoDTO);
        Assert.assertTrue(response.isSuccess());
    }


    @Test
    public void paytypeList() {
        MultiResponse<PaymentTypeDTO> response = paymentTypeClient.getPaymentTypes();
        Assert.assertTrue(response.isSuccess());
    }

}