package com.qiyi.vip.service;

import java.util.ArrayList;
import java.util.List;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.PayChannelDTONew;
import com.qiyi.vip.dto.data.PayChannelReqDTO;
import com.qiyi.vip.dto.data.PayInfoReqDTO;
import com.qiyi.vip.dto.data.PayInfoResDTO;
import com.qiyi.vip.dto.data.ProductReqDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 13:45 2021/3/18
 */
public class PayChannelClientTest {
    PayChannelClient payChannelClient = new PayChannelClient();

    @Before
    public void setUp() {
        payChannelClient.setRootUrl("http://127.0.0.1:8080/api/payChannel/");
        payChannelClient.setAdminRootUrl("http://127.0.0.1:8080/admin/payChannel/");
        payChannelClient.setSignKey("1111");
        payChannelClient.setChannel("asdf");
        payChannelClient.init();
    }

    @Test
    public void payInfo() {
        PayInfoReqDTO payInfoReqDTO = new PayInfoReqDTO();
        payInfoReqDTO.setUserAgent("micromessenger");
        payInfoReqDTO.setHasPayType(true);
        List<PayChannelReqDTO> payChannelReqDTOS = new ArrayList<>();
        PayChannelReqDTO payChannelReqDTO = new PayChannelReqDTO();
        payChannelReqDTO.setScenario("wap");
        payChannelReqDTO.setVersion("1.0");
        payChannelReqDTO.setPayChannelId(2);
        payChannelReqDTOS.add(payChannelReqDTO);

        List<ProductReqDTO> productReqDTOList = new ArrayList<>();
        ProductReqDTO productReqDTO = new ProductReqDTO();
        productReqDTO.setAutoRenew(0);
        productReqDTO.setSkuId("test");
        productReqDTO.setPayChannels(payChannelReqDTOS);

        productReqDTOList.add(productReqDTO);

        payInfoReqDTO.setProducts(productReqDTOList);

        SingleResponse<PayInfoResDTO> response = payChannelClient.payInfo(payInfoReqDTO);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getData());
    }

    @Test
    public void getPayChannelsByBusiness() {
        String business = "VIP";

        MultiResponse<PayChannelDTONew> response = payChannelClient.getPayChannelsByBusiness(business);

        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getData());
        Assert.assertEquals("A00000", response.getCode());

        List<PayChannelDTONew> payChannels = response.getData();
        Assert.assertEquals(1, payChannels.size());
        PayChannelDTONew payChannelDTO = payChannels.get(0);
        Assert.assertEquals(Long.valueOf("1"), payChannelDTO.getId());
    }

}