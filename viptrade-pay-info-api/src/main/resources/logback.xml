<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/viptrade-pay-info/error.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/viptrade-pay-info/error-%d{yyyy-MM-dd_HH}-%i.log.gz</fileNamePattern>
            <!-- 当天的日志大小 超过${log.max.size}时,压缩日志并保存 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>300MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多7GB-->
            <totalSizeCap>7GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/viptrade-pay-info/viptrade-pay-info.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/viptrade-pay-info/viptrade-pay-info-%d{yyyy-MM-dd_HH}-%i.log.gz</fileNamePattern>
            <!-- 当天的日志大小 超过${log.max.size}时,压缩日志并保存 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>300MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>360</maxHistory>
            <!--保留15天的历史记录，但最多7GB-->
            <totalSizeCap>7GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="ASYNC_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <appender-ref ref="DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
    </appender>

    <root level="INFO">
<!--        <appender-ref ref="CONSOLE"/>-->
        <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
        <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
    </root>
</configuration>
