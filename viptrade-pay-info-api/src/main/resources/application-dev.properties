spring.jackson.default-property-inclusion=non_null

DBM_CONFIG_APPID=qpaas-db-viptrade-pay-info-api-TEST
APOLLO_PAAS_TOKEN=ad60174d-60be-82fd-2ebe-08bd9c35057d

mybatis.config-location=classpath:mybatis/mybatis-config.xml

qiyi.domain=http://i.vip.qiyi.com

#CloudConfiguration
application.name=viptrade-pay-info
application.env=dev
application.region=default

# eureka config
eureka.instance.hostname=${spring.cloud.client.ip-address}
eureka.instance.non-secure-port=8080
eureka.instance.lease-renewal-interval-in-seconds=3
eureka.instance.lease-expiration-duration-in-seconds=5
eureka.client.service-url.defaultZone=http://************:8080/eureka/
eureka.client.healthcheck.enabled=true
eureka.client.register-with-eureka=false
hystrix.metrics.enabled=false
#management.metrics.enable.all=false
management.endpoint.health.enabled=false
management.endpoint.health.show-details=always
management.endpoints.web.exposure.exclude=
management.health.db.enabled=false
management.health.redis.enabled= false
management.health.defaults.enabled= false
management.info.git.mode=full

##优雅上线开关
v.spring.cloud.service-registry.auto-registration.graceful-start-enabled=false
##优雅下线开关，打开以下配置则生效
v.spring.cloud.service-registry.graceful-shutdown.enabled=false

server.tomcat.accesslog.buffered= true
server.tomcat.accesslog.enabled= true
server.tomcat.accesslog.request-attributes-enabled= true
server.tomcat.accesslog.file-date-format= .yyyy-MM-dd_HH
server.tomcat.accesslog.pattern= '%a:%A:%l:%u:%t:%r:%s:%b:%D:%H:%q:%U:%{Referer}i:%{P00001}c:%{X-Real-Ip}i:%{X-Forwarded-For}i:%{User-Agent}i'
server.tomcat.accesslog.directory= /data/logs/${spring.application.name}
server.tomcat.accesslog.check-exists= true


paycenter.pwdFree.queryUrl=http://inter-test.account.qiyi.domain/account/dut/pwdFreePayQuery.action
payCenter.sign.key=**********


account.dutquery.url=http://inter-test.account.qiyi.domain/pay/dut/query.action

commodity.center.domain=http://VIP-COMMODITY-CENTER-TEST
commodity.center.sign.key=123456

basic.data.url=http://VIP-BASIC-DATA-TEST/basic-data
basic.data.signKey=123456