spring.jackson.default-property-inclusion=non_null

mybatis.config-location=classpath:mybatis/mybatis-config.xml

qiyi.domain=http://i.vip.qiyi.com

#CloudConfiguration
application.name=viptrade-pay-info
application.env=PRO
application.region=default

spring.application.name=viptrade-pay-info

# eureka config
eureka.instance.port=${PORT_8080}
eureka.instance.hostname=${HOST}
eureka.instance.ip-address = ${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${HOST}:${spring.application.name}:${eureka.instance.non-secure-port}
eureka.instance.prefer-ip-address =false
eureka.instance.initial-status=up
eureka.instance.metadata-map.zone=zone-pre
eureka.client.healthcheck.enabled=false
eureka.client.register-with-eureka=true
eureka.client.region=region-bj
eureka.client.availability-zones.region-bj=zone-pre
eureka.client.service-url.zone-pre=http://pre-eureka.vip.qiyi.domain:8080/eureka/

spring.cloud.netflix.metrics.enabled=false
eureka.instance.lease-renewal-interval-in-seconds=5
eureka.instance.lease-expiration-duration-in-seconds=5

# actuator config
endpoints.health.sensitive=false
endpoints.enabled = false
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.redis.enabled=false
management.health.mail.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=simple

account.dutquery.key=707bae914efa48cb87f1c505c0a26c1a

payCenter.sign.key=f1a34dc464325d35f6dc90c1f3

commodity.center.domain=http://VIP-COMMODITY-CENTER-ONLINE
commodity.center.sign.key=ca5bbcdf426ddd69dfdc9ad2100d04c8

basic.data.url=http://VIP-BASIC-DATA-ONLINE/basic-data
basic.data.signKey=c1b21d0f32a0835a