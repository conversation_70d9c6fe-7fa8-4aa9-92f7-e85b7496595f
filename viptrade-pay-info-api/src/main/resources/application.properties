spring.application.name=viptrade-pay-info
spring.profiles.active=dev
project.name=viptrade-pay-info
server.port=8080
management.server.port=8099
management.endpoints.web.exposure.include=health,prometheus
# 优雅上下线相关配置，允许实例覆盖
spring.main.allow-bean-definition-overriding=true
spring.jackson.serialization.write-dates-as-timestamps=true

#jetCache config
jetcache.penetrationProtect=true
jetcache.areaInCacheName=false
jetcache.statIntervalMinutes=3
jetcache.hidePackages=com.alibaba
jetcache.local.default.type=caffeine
jetcache.local.default.keyConvertor=jackson
jetcache.local.default.limit=1000
jetcache.local.default.expireAfterWriteInMillis=300000


#优雅上线开关
v.spring.cloud.service-registry.auto-registration.graceful-start-enabled=true
#优雅下线开关，打开以下配置则生效
v.spring.cloud.service-registry.graceful-shutdown.enabled=true
#延迟注册时间
v.spring.cloud.service-registry.auto-registration.delay-registry-millis=10000
#停机等待超时时间
v.spring.cloud.service-registry.graceful-shutdown.wait-timeout-millis=20000
#eureka client刷新本地缓存时间,默认30s
eureka.client.registryFetchIntervalSeconds=5
#优雅上线预热接口path，spring-boot 2.x版本配置 形如:[/testwu,/testwu2?name=123]
#v.spring.cloud.service-registry.auto-registration.warm-up-path-list=/testwu,/testwu2?name=123
#优雅下线拦截器不拦截请求
v.spring.cloud.service-registry.graceful-shutdown.exclude-path-patterns=/health,/*error*,/index
#优雅下线拦截器拦截请求
v.spring.cloud.service-registry.graceful-shutdown.include-path-patterns=/**
#通过ribbon进行负载均衡
spring.cloud.loadbalancer.ribbon.enabled=true
ribbon.eureka.enabled=true
#eureka客户端ribbon刷新时间,默认30s
ribbon.ServerListRefreshInterval=5000

management.metrics.distribution.percentiles.[http.server.requests]=0.5, 0.9, 0.95, 0.99, 0.999
management.metrics.distribution.percentiles.[http.client.requests]=0.5, 0.9, 0.95, 0.99, 0.999

spring.mvc.pathmatch.matching-strategy=ant_path_matcher