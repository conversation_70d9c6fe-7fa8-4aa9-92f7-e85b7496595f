spring.jackson.default-property-inclusion=non_null

DBM_CONFIG_APPID=qpaas-db-viptrade-pay-info-api-TEST
APOLLO_PAAS_TOKEN=ad60174d-60be-82fd-2ebe-08bd9c35057d

application.name=viptrade-pay-info
application.env=TEST
application.region=default

mybatis.config-location=classpath:mybatis/mybatis-config.xml

qiyi.domain=http://i.vip.qiyi.com

# 命名格式：应用名-test
spring.application.name=viptrade-pay-info
server.port=8080

eureka.instance.hostname=${HOST}
eureka.instance.non-secure-port=${PORT_8080}
eureka.instance.instance-id=${eureka.instance.hostname}:${eureka.instance.non-secure-port}

# 租期更新时间间隔（默认30秒）
eureka.instance.lease-renewal-interval-in-seconds=5
# 租期到期时间（默认90秒）
eureka.instance.lease-expiration-duration-in-seconds=10
# 是否注册到注册中心，如果不需要可以设置为false
eureka.client.register-with-eureka=true
# 注册中心配置
eureka.client.serviceUrl.defaultZone=http://test-eureka.vip.qiyi.domain:8080/eureka/
# 开启健康检查（需要spring-boot-starter-actuator依赖）
eureka.client.healthcheck.enabled=false
spring.cloud.netflix.metrics.enabled=false

# actuator config
endpoints.health.sensitive=false
endpoints.enabled=false
management.endpoints.web.exposure.exclude=
management.security.enabled=true
management.health.defaults.enabled=true
management.health.mail.enabled=false
management.health.redis.enabled=false
management.health.eureka.enabled=false
management.endpoint.health.show-details=always
management.info.git.mode=full

paycenter.pwdFree.queryUrl=http://inter-test.account.qiyi.domain/account/dut/pwdFreePayQuery.action
payCenter.sign.key=**********

account.dutquery.url=http://inter-test.account.qiyi.domain/pay/dut/query.action

commodity.center.domain=http://VIP-COMMODITY-CENTER-TEST
commodity.center.sign.key=123456

basic.data.url=http://VIP-BASIC-DATA-TEST/basic-data
basic.data.signKey=123456