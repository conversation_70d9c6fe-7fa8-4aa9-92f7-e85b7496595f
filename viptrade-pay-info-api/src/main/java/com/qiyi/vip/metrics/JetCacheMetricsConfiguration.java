/*
 * Copyright (c) 2019-2029, Dreamlu 卢春梦 (<EMAIL> & www.dreamlu.net).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.qiyi.vip.metrics;

import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.SimpleCacheManager;
import com.alicp.jetcache.anno.support.SpringConfigProvider;
import com.alicp.jetcache.template.CacheBuilderTemplate;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jetcache metrics 配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(MeterRegistry.class)
@ConditionalOnProperty(
    prefix = JetCacheMetricsProperties.PREFIX,
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true
)
@EnableConfigurationProperties(JetCacheMetricsProperties.class)
public class JetCacheMetricsConfiguration {


    @Bean(name = "jcCacheManager")
    public CacheManager cacheManager(SpringConfigProvider springConfigProvider, MeterRegistry meterRegistry) {
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        CacheBuilderTemplate cacheBuilderTemplate = springConfigProvider.getCacheBuilderTemplate();
        cacheBuilderTemplate.getCacheMonitorInstallers().add(new JetCacheActuatorMonitorInstaller(meterRegistry));
        cacheManager.setCacheBuilderTemplate(cacheBuilderTemplate);
        return cacheManager;
    }
}
