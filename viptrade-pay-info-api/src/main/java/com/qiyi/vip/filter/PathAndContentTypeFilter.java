package com.qiyi.vip.filter;


import lombok.extern.slf4j.Slf4j;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR> @date 2024/10/21 21:00
 */
@Slf4j
public class PathAndContentTypeFilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;

        if (isForbiddenContentType(httpRequest.getContentType())) {
            log.warn("forbidden contentType: {}", httpRequest.getContentType());
            return;
        }

        if (!isAllowedPath(httpRequest.getRequestURI())) {
            log.warn("forbidden path: {}", httpRequest.getRequestURI());
            return;
        }

        filterChain.doFilter(servletRequest, servletResponse);
    }

    private boolean isForbiddenContentType(String contentType) {
        return contentType != null && contentType.startsWith("multipart/form-data");
    }

    private boolean isAllowedPath(String path) {
        return (path != null && path.startsWith("/api/payChannel/"))
            || "/".equals(path)
            || (path != null && path.startsWith("/v2"))
            || "/status".equals(path);
    }
}
