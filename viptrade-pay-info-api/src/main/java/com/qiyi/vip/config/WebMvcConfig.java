package com.qiyi.vip.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.Map;

import com.qiyi.vip.constant.DelimiterChars;
import com.qiyi.vip.interceptor.SignCheckInterceptor;
import com.qiyi.vip.utils.VipStringUtil;
import com.iqiyi.solar.config.client.CloudConfig;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/1 10:11 PM
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    CloudConfig cloudConfig;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] addPath = {"/api/payChannel/**"};
        registry.addInterceptor(signCheckInterceptor())
            .addPathPatterns(addPath)
            .excludePathPatterns("/actuator/prometheus", "/", "/status", "/v2/api-docs")
        ;
    }

    @Bean
    public HandlerInterceptor signCheckInterceptor() {
        SignCheckInterceptor signCheckInterceptor = new SignCheckInterceptor();
        signCheckInterceptor.setChannelSignKeyMap(channelSignKeyMap());
        signCheckInterceptor.setOrder(1);
        return signCheckInterceptor;
    }

    /**
     * 请求渠道和渠道签名的映射Map
     *
     * @return {@link Map}
     */
    @Bean
    public Map<String, String> channelSignKeyMap() {
        String channelSignKeys = cloudConfig.getProperty("channelSignKeys", "");
        return VipStringUtil.stringToMap(channelSignKeys, DelimiterChars.SEMICOLON, DelimiterChars.EQUALS);
    }

}
