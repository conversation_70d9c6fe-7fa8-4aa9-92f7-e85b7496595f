package com.qiyi.vip.config;

import com.iqiyi.solar.config.client.CloudConfigChange;
import com.iqiyi.solar.config.client.CloudConfigEvent;
import com.iqiyi.solar.config.client.CloudConfigListener;
import com.qiyi.vip.constant.DelimiterChars;
import com.qiyi.vip.domain.ApplicationContextHelper;
import com.qiyi.vip.interceptor.SignCheckInterceptor;
import com.qiyi.vip.utils.VipStringUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component(value = "signKeyListener")
public class ChannelSignKeyListener implements CloudConfigListener {

    @Resource
    ApplicationContextHelper applicationContextHelper;

    @Override
    public void onChange(CloudConfigEvent changeEvent) {
        CloudConfigChange configChange = changeEvent.getChange("channelSignKeys");
        String channelSignKeys = StringUtils.defaultIfBlank(configChange.getNewValue(), "");

        Map<String, String> channelSignKeyConfig = VipStringUtil.stringToMap(channelSignKeys, DelimiterChars.SEMICOLON, DelimiterChars.EQUALS);

        if (MapUtils.isNotEmpty(channelSignKeyConfig)) {
            //update
            SignCheckInterceptor signCheckInterceptor = applicationContextHelper.getBean(SignCheckInterceptor.class);
            signCheckInterceptor.setChannelSignKeyMap(channelSignKeyConfig);
        }
    }
}