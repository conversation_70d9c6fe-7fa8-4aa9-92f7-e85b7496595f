package com.qiyi.vip.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.qiyi.vip.filter.PathAndContentTypeFilter;
import com.qiyi.vip.filter.ReplaceStreamFilter;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/11 10:11 AM
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<PathAndContentTypeFilter> PathAndContentTypeFilterRegistration() {
        FilterRegistrationBean<PathAndContentTypeFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new PathAndContentTypeFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        registration.setName("PathAndContentTypeFilter");
        return registration;
    }

    @Bean
    public FilterRegistrationBean<ReplaceStreamFilter> ReplaceStreamFilterRegistration() {
        FilterRegistrationBean<ReplaceStreamFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ReplaceStreamFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(2);
        registration.setName("ReplaceStreamFilter");
        return registration;
    }

}
