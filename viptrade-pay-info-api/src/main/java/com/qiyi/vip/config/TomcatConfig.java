package com.qiyi.vip.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @className TomcatConfig
 * @description
 * @date 2023/6/19
 **/
@Configuration
public class TomcatConfig {
    @Bean
    public ConfigurableServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(connector -> {
            connector.setProperty("relaxedPathChars", "<>[\\]^`{|}"); // 添加需要的特殊符号
            connector.setProperty("relaxedQueryChars", "<>[\\]^`{|}");    // 添加需要的特殊符号
        });
        return factory;
    }
}
