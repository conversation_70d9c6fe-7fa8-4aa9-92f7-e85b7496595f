package com.qiyi.vip.config;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.SentinelWebInterceptor;
import com.alibaba.csp.sentinel.adapter.spring.webmvc.config.SentinelWebMvcConfig;
import com.alibaba.fastjson.JSON;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className SentinelInterceptorConfig
 * @description
 * @date 2023/6/19
 **/
@Configuration
public class SentinelInterceptorConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        SentinelWebMvcConfig config = new SentinelWebMvcConfig();
        // Enable the HTTP method prefix.
        config.setHttpMethodSpecify(true);
        config.setBlockExceptionHandler((request, response, e) -> {
            response.setHeader("Content-Type", "application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            Map<String, String> map = new HashMap<>();
            map.put("code", "Q00449");
            map.put("msg", "被限流了");
            map.put("message", "被限流了");
            out.print(JSON.toJSONString(map));
            out.flush();
            out.close();
        });
        // Add to the interceptor list.
        String[] addPaths = new String[]{"/api/payChannel/**"};
        registry.addInterceptor(new SentinelWebInterceptor(config)).addPathPatterns(addPaths);
    }
}
