package com.qiyi.vip.web;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import com.qiyi.vip.api.PaymentDutTypeServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.BatchGetDutTypeReqDto;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;

/**
 * <AUTHOR>
 * @date 2021/3/8 7:01 PM
 */
@Slf4j
@RestController
@Api(value = "代扣方式dut_type信息Controller")
@RequestMapping("/api/payChannel/payDutType")
public class PaymentDutTypeController {

    @Resource
    PaymentDutTypeServiceI paymentDutTypeService;

    /**
     * 该查询不限制有效期时间，按指定dut_type返回相关信息,提供给自动续费使用
     */
    @GetMapping(value = "/getRenewPrice")
    public SingleResponse<PaymentDutTypeDTO> getRenewPrice(Long vipType, Integer amount, String actCode, @RequestParam List<Integer> dutTypes) {
        log.info("[查询签约价],请求参数:vipType={},amount={},actCode={},dutType={}", vipType, amount, actCode, dutTypes);
        return paymentDutTypeService.getRenewPrice(vipType, amount, actCode, dutTypes);
    }

    @PostMapping(value = "/getDutType")
    public SingleResponse<Integer> getDutType(@RequestBody PaymentDutTypeReqDTO paymentDutTypeReqDTO) {
        log.info("[路由相应的dut_type],请求参数:paymentDutTypeReqDTO={}", paymentDutTypeReqDTO);
        return paymentDutTypeService.getDutType(paymentDutTypeReqDTO);
    }

    @PostMapping(value = "/batchGetDutType")
    public MultiResponse<Integer> batchGetDutType(@RequestBody BatchGetDutTypeReqDto batchGetDutTypeReqDto) {
        log.info("[路由相应的dut_type][batchGetDutType],请求参数:paymentDutTypeReqDTOs={}", batchGetDutTypeReqDto.getDutTypeReqList());
        return paymentDutTypeService.batchGetDutType(batchGetDutTypeReqDto.getDutTypeReqList());
    }

    @PostMapping(value = "/getAgreementInfo")
    public SingleResponse<AgreementInfoDTO> getAgreementInfo(@RequestBody PaymentDutTypeReqDTO paymentDutTypeReqDTO) {
        log.info("[路由相应的dut_type],请求参数:paymentDutTypeReqDTO={}", paymentDutTypeReqDTO);
        return paymentDutTypeService.getAgreementInfo(paymentDutTypeReqDTO);
    }

    /**
     * 该查询不限制有效期时间,提供给自动续费使用
     */
    @GetMapping(value = "/getDutTypes")
    public MultiResponse<PaymentDutTypeDTO> getDutTypes(Long payType, Integer vipType, Integer amount) {
        log.info("[路由相应的dut_type],请求参数:payType={},vipType={},amount={}", payType, vipType, amount);
        return paymentDutTypeService.getDutTypes(payType, vipType, amount);
    }

    /**
     * 该查询不限制有效期时间,提供给自动续费使用
     */
    @GetMapping(value = "/getDutTypes/v2")
    public MultiResponse<PaymentDutTypeDTO> getDutTypesV2(PaymentDutTypeReqDTO paymentDutTypeReqDTO) {
        log.info("[getDutTypesV2] paymentDutTypeReqDTO:{}", paymentDutTypeReqDTO);
        return paymentDutTypeService.getDutTypes(paymentDutTypeReqDTO);
    }

    @GetMapping(value = "/getWechatAmount")
    public SingleResponse<Integer> getWechatAmountByDutType(Integer dutType) {
        log.info("[查询微信代扣时长],请求参数:dutType={}", dutType);
        return paymentDutTypeService.getWechatAmountByDutType(dutType);
    }

    @GetMapping(value = "/getDutTypeExcludeActCode")
    public MultiResponse<PaymentDutTypeDTO> getDutTypeExcludeActCode(Long vipType, Integer amount, String partnerId) {
        log.info("[查询代扣方式,排除活动code],请求参数:vipType={},amount={},partnerId={}", vipType, amount, partnerId);
        return paymentDutTypeService.getDutTypeExcludeActCode(vipType, amount, partnerId);
    }

    @GetMapping(value = "/getDutTypeByActCode")
    public SingleResponse<PaymentDutTypeDTO> getDutTypeByActCode(String actCode) {
        log.info("[查询一条代扣方式信息,根据活动code],请求参数:actCode={}", actCode);
        return paymentDutTypeService.getDutTypeByActCode(actCode);
    }

    @GetMapping(value = "/getPayTypeByPayChannel")
    public MultiResponse<PaymentDutTypeDTO> getPayTypeByPayChannel(Integer payChannel, Integer vipType) {
        log.info("[查询代扣方式信息list,根据渠信息和会员类型],请求参数:payChannel={},vipType={}", payChannel, vipType);
        return paymentDutTypeService.getPayTypeByPayChannel(payChannel, vipType);
    }

    @GetMapping(value = "/getDutTypeByPayType")
    public MultiResponse<PaymentDutTypeDTO> getDutTypeByPayType(Integer payType) {
        log.info("[查询代扣方式信息list,根据支付类型],请求参数:payType={}", payType);
        return paymentDutTypeService.getDutTypeByPayType(payType);
    }
}
