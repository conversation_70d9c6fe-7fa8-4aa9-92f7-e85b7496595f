package com.qiyi.vip.web;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoResDTO;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoByCodeReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementInfoReqDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoReqDTO;
import com.qiyi.vip.dto.data.TransformResDTO;

/**
 * @auther: guojing
 * @date: 2023/3/10 10:09 AM
 */
@Api(value = "协议相关接口")
@Slf4j
@RestController
@RequestMapping("/api/payChannel/agreement")
public class AgreementController {

    @Resource
    private AgreementServiceI agreementService;

    @GetMapping(value = "/getDefaultAgreementNoByDutType")
    public SingleResponse<Integer> getDefaultAgreementNoByDutType(QueryAgreementNoReqDTO param) {
        log.info("getAgreementNoByDutType, param:{}", param);
        return agreementService.getDefaultAgreementNoByDutType(param);
    }

    @GetMapping(value = "/getAgreementNoByCodeAndPayType")
    public SingleResponse<AgreementNoInfoDTO> getAgreementNoByCodeAndPayType(QueryAgreementNoByCodeReqDTO param) {
        log.info("getAgreementNoByCodeAndPayChannel, param:{}", param);
        return agreementService.getAgreementNoByCodeAndPayType(param);
    }

    @GetMapping(value = "/getDutTypeAndIsFirstSignById")
    public SingleResponse<TransformResDTO> getDutTypeAndIsFirstSignById(Long userId, Integer agreementNo) {
        log.info("[getDutTypeAndIsFirstSignById],请求参数:agreementNo={}", agreementNo);
        return agreementService.getDutTypeAndIsFirstSignById(userId, agreementNo);
    }

    @GetMapping(value = "/getAgreementInfo")
    public SingleResponse<AgreementInfoResDTO> getAgreementInfo(QueryAgreementInfoReqDTO param) {
        log.info("[getAgreementInfo],请求参数:param={}", param);
        return agreementService.getAgreementInfo(param);
    }

    @GetMapping(value = "/getAgreementListByDutType")
    public MultiResponse<AgreementNoInfoDTO> getAgreementListByDutType(QueryAgreementNoReqDTO param) {
        log.info("[getAgreementListByDutType],param:{}", param);
        return agreementService.getAgreementListByDutType(param);
    }

    @GetMapping(value = "/getTemplateByCode")
    public SingleResponse<AgreementTemplateDTO> getTemplateByCode(String templateCode) {
        log.info("[getTemplateByAgreementNo],请求参数:templateCode={}", templateCode);
        return agreementService.getTemplateByCode(templateCode);
    }

    @GetMapping(value = "/getTemplateByAgreementNo")
    public SingleResponse<AgreementTemplateDTO> getTemplateByAgreementNo(Integer agreementNo) {
        log.info("[getTemplateByAgreementNo],请求参数:agreementNo={}", agreementNo);
        return agreementService.getTemplateByAgreementNo(agreementNo);
    }

}
