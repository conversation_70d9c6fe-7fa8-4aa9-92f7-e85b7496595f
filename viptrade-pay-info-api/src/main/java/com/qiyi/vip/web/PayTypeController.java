package com.qiyi.vip.web;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.PayTypeTransforReqDTO;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.dto.data.QueryPayTypeByIdsDTO;
import com.qiyi.vip.dto.data.QueryPayTypeInfoDTO;
import com.qiyi.vip.dto.data.TransformResDTO;
import com.qiyi.vip.paytype.PaymentTypeServiceImpl;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 17:25 2021/3/8
 */
@Api(value = "支付方式信息Controller")
@RestController
@RequestMapping("/api/payChannel/payType")
@Slf4j
public class PayTypeController {
    @Resource
    PaymentTypeServiceImpl paymentTypeService;

    @PostMapping(value = "/infos")
    public MultiResponse<PaymentTypeDTO> infos(@RequestBody QueryPayTypeInfoDTO queryPayTypeInfoDTO) {
        log.info("[查询支付方式信息],请求参数:queryPayTypeInfoDTO={}", queryPayTypeInfoDTO);
        return paymentTypeService.getPaymentTypes(queryPayTypeInfoDTO);
    }

    @PostMapping(value = "/byIds")
    public MultiResponse<PaymentTypeDTO> infos(@RequestBody QueryPayTypeByIdsDTO queryPayTypeByIdsDTO) {
        log.info("[查询支付方式信息],请求参数:queryPayTypeByIdsDTO={}", queryPayTypeByIdsDTO);
        return paymentTypeService.getPaymentTypes(queryPayTypeByIdsDTO.getPayTypes());
    }

    @GetMapping(value = "/route")
    public SingleResponse<Long> route(String userAgent, Integer autoRenew) {
        log.info("[路由支付方式],请求参数:userAgent={},autoRenew:{}", userAgent, autoRenew);
        return paymentTypeService.routePayType(userAgent, autoRenew);
    }

    @PostMapping(value = "/getPasswordFreeSignInfo")
    public MultiResponse<PaymentTypeDTO> getPasswordFreeSignPayTypes(@RequestBody QueryPayTypeByIdsDTO queryPayTypeByIdsDTO) {
        log.info("[查询免密签约支付方式],请求参数:payTypes={}", queryPayTypeByIdsDTO);
        return paymentTypeService.getPasswordFreeSignPayTypes(queryPayTypeByIdsDTO.getPayTypes());
    }

    @PostMapping(value = "/transform")
    public SingleResponse<TransformResDTO> transform(@RequestBody PayTypeTransforReqDTO payTypeTransforReqDTO) {
        log.info("[下单时对支付方式进行转换],请求参数:PaymentTypeDTO={}", payTypeTransforReqDTO);
        return paymentTypeService.transform(payTypeTransforReqDTO);
    }

}
