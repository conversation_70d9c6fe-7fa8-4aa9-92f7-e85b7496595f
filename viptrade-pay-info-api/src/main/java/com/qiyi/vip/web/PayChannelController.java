package com.qiyi.vip.web;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.qiyi.vip.api.PayChannelServiceI;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.PayInfoReqDTO;
import com.qiyi.vip.dto.data.PayInfoResDTO;
import com.qiyi.vip.dto.data.RoutePaymentInfoDTO;
import com.qiyi.vip.dto.data.RoutePaymentInfoResult;
import com.qiyi.vip.dto.data.SmartPayInfoReqDTO;

/**
 * <AUTHOR>
 * @Description: 支付渠道相关接口
 * @date 2021/3/4 10:01 PM
 */
@RestController
@Api(value = "支付渠道信息列表Controller")
@RequestMapping("/api/payChannel")
@Slf4j
public class PayChannelController {
    @Resource
    PayChannelServiceI payChannelService;

    /**
     * 路由支付信息
     * @param routePaymentInfo 支付信息请求参数
     * @return 路由结果
     */
    @PostMapping(value = "/routePaymentInfo")
    public SingleResponse<RoutePaymentInfoResult> routePaymentInfo(@RequestBody RoutePaymentInfoDTO routePaymentInfo) {
        log.info("[路由支付方式],请求参数:RoutePaymentInfoDTO={}", routePaymentInfo);
        SingleResponse<RoutePaymentInfoResult> response = payChannelService.routePaymentInfo(routePaymentInfo);
        log.info("[路由支付方式] 结果：{}", response.getData());
        return response;
    }

    @PostMapping(value = "/payInfo")
    public SingleResponse<PayInfoResDTO> payInfo(@RequestBody PayInfoReqDTO payInfoReqDTO) {
        log.info("[获取支付信息],请求参数:PayInfoReqDTO={}", payInfoReqDTO);
        return payChannelService.payInfo(payInfoReqDTO);
    }

    @PostMapping(value = "/smartPayInfo")
    public SingleResponse<PayInfoResDTO> smartPayInfo(@RequestBody SmartPayInfoReqDTO smartPayInfoReqDTO) {
        log.info("[获取smartPayInfo],请求参数:smartPayInfoReqDTO={}", smartPayInfoReqDTO);
        SingleResponse<PayInfoResDTO> response = payChannelService.smartPayInfo(smartPayInfoReqDTO);
        log.info("[获取smartPayInfo] 结果：{}", response.getData());
        return response;
    }
}
