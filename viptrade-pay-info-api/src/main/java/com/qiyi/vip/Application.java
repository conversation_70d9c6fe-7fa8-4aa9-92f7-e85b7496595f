package com.qiyi.vip;

import com.alibaba.csp.sentinel.init.InitExecutor;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;

import com.qiyi.vip.commons.component.UserTagApi;

/**
 * Spring Boot Starter
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
@ComponentScan(excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {UserTagApi.class})})
@EnableCaching
@EnableMethodCache(basePackages = "com.qiyi")
@EnableAspectJAutoProxy(exposeProxy = true)
public class Application {

    public static void main(String[] args) {
        triggerSentinelInit();
        SpringApplication.run(Application.class, args);
    }

    private static void triggerSentinelInit() {
        new Thread(InitExecutor::doInit).start();
    }

}
