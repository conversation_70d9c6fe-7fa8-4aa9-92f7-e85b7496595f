package com.qiyi.vip.api;

import java.util.ArrayList;

import com.qiyi.vip.domain.agreement.gateway.AgreementGateway;
import com.qiyi.vip.domain.duttype.AgreementRouteConfig;
import com.qiyi.vip.domain.duttype.PaymentDutType;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementInfoDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeDTO;
import com.qiyi.vip.dto.data.PaymentDutTypeReqDTO;
import com.qiyi.vip.duttype.PaymentDutTypeImpl;

import com.alicp.jetcache.Cache;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 20:14 2021/3/9
 */
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest
public class PaymentDutTypeServiceITest {

    @Autowired
    PaymentDutTypeImpl paymentDutTypeService;
    @Resource
    com.alicp.jetcache.CacheManager jetCacheManager;
    @Resource
    AgreementGateway agreementGateway;
    @Test
    public void getRenewPrice() {
        SingleResponse<PaymentDutTypeDTO> priceResponse = paymentDutTypeService.getRenewPrice(1L, 1, null, new ArrayList<>(6));
        Assert.assertTrue(priceResponse.isSuccess());
        Assert.assertNotNull(priceResponse.getData());
    }

    @Test
    public void getWechatAmountByDutType() {
        SingleResponse<Integer> dutTypeAmount = paymentDutTypeService.getWechatAmountByDutType(15);
        Assert.assertTrue(dutTypeAmount.isSuccess());
        Assert.assertNotNull(dutTypeAmount.getData());
    }

    @Test
    public void getDutTypeExcludeActCode() {
        MultiResponse<PaymentDutTypeDTO> paymentDutTypeDTO = paymentDutTypeService.getDutTypeExcludeActCode(1L, 1, null);
        Assert.assertTrue(paymentDutTypeDTO.isSuccess());
        Assert.assertNotNull(paymentDutTypeDTO.getData());
    }

    @Test
    public void getDutTypeByActCode() {
        SingleResponse<PaymentDutTypeDTO> dutTypeDTOSingleResponse = paymentDutTypeService.getDutTypeByActCode("first_renew_tv");
        Assert.assertTrue(dutTypeDTOSingleResponse.isSuccess());
        Assert.assertNotNull(dutTypeDTOSingleResponse.getData());
    }

    @Test
    public void getPayTypeByPayChannel() {
        MultiResponse<PaymentDutTypeDTO> payChannel = paymentDutTypeService.getPayTypeByPayChannel(2,5);
        Assert.assertTrue(payChannel.isSuccess());
        Assert.assertNotNull(payChannel.getData());
    }

    @Test
    public void getDutTypeByPayType() {
        MultiResponse<PaymentDutTypeDTO> paymentDutTypeDTO = paymentDutTypeService.getDutTypeByPayType(65);
        Assert.assertTrue(paymentDutTypeDTO.isSuccess());
        Assert.assertNotNull(paymentDutTypeDTO.getData());
    }

    @Test
    public void getDutType() {
        PaymentDutTypeReqDTO dutTypeReqDTO = new PaymentDutTypeReqDTO();
        dutTypeReqDTO.setActCode("main_9600211206172816931039,sub_9600220104102601244875");
        dutTypeReqDTO.setAmount(1);
        dutTypeReqDTO.setPayType(85L);
        dutTypeReqDTO.setPayChannel(1);
        dutTypeReqDTO.setSourceVipType(null);
        dutTypeReqDTO.setVipType(1L);
        SingleResponse<Integer> dutTypePresponse = paymentDutTypeService.getDutType(dutTypeReqDTO);
        Assert.assertTrue(dutTypePresponse.isSuccess());
        Assert.assertNotNull(dutTypePresponse.getData());
    }

    @Test
    public void getAgreementInfo() {
        PaymentDutTypeReqDTO dutTypeReqDTO = new PaymentDutTypeReqDTO();
        dutTypeReqDTO.setActCode(null);
        dutTypeReqDTO.setAmount(1);
        dutTypeReqDTO.setPayType(85L);
        dutTypeReqDTO.setPayChannel(1);
        dutTypeReqDTO.setSourceVipType(null);
        dutTypeReqDTO.setVipType(1L);
        SingleResponse<AgreementInfoDTO> agreementInfoResp = paymentDutTypeService.getAgreementInfo(dutTypeReqDTO);
        SingleResponse<Integer> dutTypeResp = paymentDutTypeService.getDutType(dutTypeReqDTO);
        Assert.assertTrue(agreementInfoResp.isSuccess());
        Assert.assertNotNull(agreementInfoResp.getData());
        Assert.assertTrue(dutTypeResp.isSuccess());
        Assert.assertNotNull(dutTypeResp.getData());
        Assert.assertEquals(agreementInfoResp.getData().getDutType(), dutTypeResp.getData());

        dutTypeReqDTO.setActCode("2017082341203");
        agreementInfoResp = paymentDutTypeService.getAgreementInfo(dutTypeReqDTO);
        dutTypeResp = paymentDutTypeService.getDutType(dutTypeReqDTO);
        Assert.assertTrue(agreementInfoResp.isSuccess());
        Assert.assertTrue(dutTypeResp.isSuccess());
        Assert.assertEquals(agreementInfoResp.getData().getDutType(), dutTypeResp.getData());


        PaymentDutTypeReqDTO dutTypeReqDTO1 = new PaymentDutTypeReqDTO();
        dutTypeReqDTO1.setActCode(null);
        dutTypeReqDTO1.setAgreementActCode("123456");
        dutTypeReqDTO1.setAmount(1);
        dutTypeReqDTO1.setPayType(412L);
        dutTypeReqDTO1.setPayChannel(1);
        dutTypeReqDTO1.setSourceVipType(null);
        dutTypeReqDTO1.setVipType(1L);
        dutTypeResp = paymentDutTypeService.getDutType(dutTypeReqDTO1);
        agreementInfoResp = paymentDutTypeService.getAgreementInfo(dutTypeReqDTO1);
        System.out.println(dutTypeResp);
        System.out.println(agreementInfoResp);
        Assert.assertNotNull(dutTypeResp);
        Assert.assertNotNull(agreementInfoResp);
        Assert.assertEquals(dutTypeResp.getData(), agreementInfoResp.getData().getDutType());

        PaymentDutTypeReqDTO dutTypeReqDTO2 = new PaymentDutTypeReqDTO();
        dutTypeReqDTO2.setActCode(null);
        dutTypeReqDTO2.setAgreementActCode("9600210114142918104283");
        dutTypeReqDTO2.setPartnerId("123");
        dutTypeReqDTO2.setAmount(1);
        dutTypeReqDTO2.setPayType(412L);
        dutTypeReqDTO2.setPayChannel(1);
        dutTypeReqDTO2.setSourceVipType(null);
        dutTypeReqDTO2.setVipType(1L);
        dutTypeResp = paymentDutTypeService.getDutType(dutTypeReqDTO2);
        agreementInfoResp = paymentDutTypeService.getAgreementInfo(dutTypeReqDTO2);
        System.out.println(dutTypeResp);
        System.out.println(agreementInfoResp);
        Assert.assertNotNull(dutTypeResp);
        Assert.assertNotNull(agreementInfoResp);
        Assert.assertEquals(dutTypeResp.getData(), agreementInfoResp.getData().getDutType());
    }

    @Test
    public void getDutTypes() {
        MultiResponse<PaymentDutTypeDTO> paytypes = paymentDutTypeService.getDutTypes(303L, 1, 127);
        Assert.assertTrue(paytypes.isSuccess());
        Assert.assertNotNull(paytypes.getData());
    }

    @Test
    public void gegAgreementInfoCache() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
//        AgreementInfo agreementInfo = PaymentDutType.of().getAgreementInfo(1, 412L, null, 1L, 1, null, "9600210114142918104283", null, null);
//        agreementTemplateGateway.getAgreementNoInfo(agreementInfo.getAgreementNo());
//        PaymentDutType.of().getPaymentDutTypeGateway().getPaymentDutTypesByDutType(agreementInfo.getDutType());
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayChannel(1, null, 1L, 1, null, null, null, null);
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayType(412L, null, 1L, 1, null, null, null, null);
        AgreementRouteConfig.of().getByPayChannelAndVipType(1, null, 1L, 1, "9600210114142918104283");
        Cache<String, Object> pdt = jetCacheManager.getCache("pdt");
        stopWatch.split();
        System.out.println("cost: " + stopWatch.getSplitTime());
//        agreementInfo = PaymentDutType.of().getAgreementInfo(1, 412L, null, 1L, 1, null, "9600210114142918104283", null, null);
//        agreementTemplateGateway.getAgreementNoInfo(agreementInfo.getAgreementNo());
//        PaymentDutType.of().getPaymentDutTypeGateway().getPaymentDutTypesByDutType(agreementInfo.getDutType());
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayChannel(1, null, 1L, 1, null, null, null, null);
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayType(412L, null, 1L, 1, null, null, null, null);
        AgreementRouteConfig.of().getByPayChannelAndVipType(1, null, 1L, 1, "9600210114142918104283");
        stopWatch.split();
        System.out.println("cost: " + stopWatch.getSplitTime());
//        agreementInfo = PaymentDutType.of().getAgreementInfo(1, 412L, null, 1L, 1, null, "9600210114142918104283", null, null);
//        agreementTemplateGateway.getAgreementNoInfo(agreementInfo.getAgreementNo());
//        PaymentDutType.of().getPaymentDutTypeGateway().getPaymentDutTypesByDutType(agreementInfo.getDutType());
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayChannel(1, null, 1L, 1, null, null, null, null);
        PaymentDutType.of().getPaymentDutTypeGateway().getAgreementInfoWithPayType(412L, null, 1L, 1, null, null, null, null);
        AgreementRouteConfig.of().getByPayChannelAndVipType(1, null, 1L, 1, "9600210114142918104283");
        stopWatch.stop();
        System.out.println("cost: " + stopWatch.getSplitTime());
        System.out.println("Total: " + stopWatch.getTime());
    }
}