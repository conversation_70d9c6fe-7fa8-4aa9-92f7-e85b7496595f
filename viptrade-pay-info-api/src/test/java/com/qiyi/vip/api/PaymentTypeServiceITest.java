package com.qiyi.vip.api;

import java.util.ArrayList;
import java.util.List;

import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.paytype.PaymentTypeServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 20:35 2021/3/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PaymentTypeServiceITest {
    @Autowired
    PaymentTypeServiceImpl paymentTypeService;

    @Test
    public void getPaymentType() {
        List<Long> payTypes=new ArrayList<>();
        payTypes.add(65L);
        MultiResponse<PaymentTypeDTO> paymentTypeDTOMultiResponse =
                paymentTypeService.getPaymentTypes(payTypes);
        Assert.assertNotNull(paymentTypeDTOMultiResponse);
        Assert.assertTrue(paymentTypeDTOMultiResponse.isSuccess());
        Assert.assertTrue(CollectionUtils.isNotEmpty(paymentTypeDTOMultiResponse.getData()));
    }

    @Test
    public void getPasswordFreeSignPaymentType() {
        List<Long> payTypes=new ArrayList<>();
        payTypes.add(420L);
        MultiResponse<PaymentTypeDTO> paymentTypeDTOMultiResponse =
                paymentTypeService.getPasswordFreeSignPayTypes(payTypes);
        Assert.assertNotNull(paymentTypeDTOMultiResponse);
        Assert.assertTrue(paymentTypeDTOMultiResponse.isSuccess());
        Assert.assertTrue(CollectionUtils.isNotEmpty(paymentTypeDTOMultiResponse.getData()));
    }
}