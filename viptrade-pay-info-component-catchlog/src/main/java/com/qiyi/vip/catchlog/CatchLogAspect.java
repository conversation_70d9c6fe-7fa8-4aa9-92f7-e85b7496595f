package com.qiyi.vip.catchlog;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.qiyi.vip.exception.BaseException;
import com.qiyi.vip.exception.BizException;
import com.qiyi.vip.exception.SysException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;


/**
 * @ Description   :  Catching and Logging
 * @ Author        :  <PERSON>
 * @ CreateDate    :  2020/11/09
 * @ Version       :  1.0
 */
@Aspect
@Slf4j
public class CatchLogAspect {

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    /**
     * The syntax of pointcut : https://blog.csdn.net/zhengchao1991/article/details/53391244
     */
    @Pointcut("@within(CatchAndLog) && execution(public * *(..))")
    public void pointcut(){
    }

    @Around(value = "pointcut()")
    public Object around(ProceedingJoinPoint joinPoint ) {
        long startTime =  System.currentTimeMillis();

        logRequest(joinPoint);

        Object response = null;
        try {
             response = joinPoint.proceed();
        }
        catch (Throwable e){
            response = handleException(joinPoint, e);
        }
        finally {
            logResponse(startTime, response);
        }

        return response ;
    }

    private Object handleException(ProceedingJoinPoint joinPoint, Throwable e) {
        MethodSignature ms = (MethodSignature)joinPoint.getSignature();
        Class returnType = ms.getReturnType();

        if (e instanceof BizException){
            log.warn("BIZ EXCEPTION : " + e.getMessage());
            //在Debug的时候，对于BizException也打印堆栈
            if(log.isDebugEnabled()){
                log.error(e.getMessage(), e);
            }
            return ResponseHandler.handle(returnType, (BaseException)e);
        }

        if (e instanceof SysException){
            log.error("SYS EXCEPTION :");
            log.error(e.getMessage(), e);
            return ResponseHandler.handle(returnType, (BaseException)e);
        }

        log.error("UNKNOWN EXCEPTION :");
        log.error(e.getMessage(), e);

        return ResponseHandler.handle(returnType, "UNKNOWN_ERROR", e.getMessage());
    }


    private void logResponse(long startTime, Object response) {
        try {
            long endTime = System.currentTimeMillis();
            log.info("RESPONSE :{} ", GSON.toJson(response));
            log.info("COST : " + (endTime - startTime) + "ms");
        } catch (Exception e) {
            //swallow it
            log.error("logResponse error", e);
        }
    }

    private void logRequest(ProceedingJoinPoint joinPoint) {
        try {
            log.info("START PROCESSING: " + joinPoint.getSignature().toShortString());
            Object[] args = joinPoint.getArgs();
            log.info("REQUEST : {}", GSON.toJson(args));
        } catch (Exception e) {
            //swallow it
            log.error("logReqeust error : ", e);
        }
    }

}
