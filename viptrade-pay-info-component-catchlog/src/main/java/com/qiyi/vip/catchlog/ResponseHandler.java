package com.qiyi.vip.catchlog;

import com.qiyi.vip.dto.Response;
import com.qiyi.vip.exception.BaseException;
import lombok.extern.slf4j.Slf4j;

/**
 * ResponseHandler
 *
 * <AUTHOR>
 * @date 2020-11-10 3:18 PM
 */
@Slf4j
public class ResponseHandler {

    public static Object handle(Class returnType, String errCode, String errMsg){
        if (isColaResponse(returnType)){
            return handleColaResponse(returnType, errCode, errMsg);
        }
        return null;
    }

    public static Object handle(Class returnType, BaseException e){
        return handle(returnType, e.getErrCode(), e.getMessage());
    }



    private static Object handleColaResponse(Class returnType, String errCode, String errMsg) {
        try {
            Response response = (Response)returnType.newInstance();
            response.setSuccess(false);
            response.setCode(errCode);
            response.setErrMessage(errMsg);
            return response;
        }
        catch (Exception ex){
            log.error(ex.getMessage(), ex);
            return  null;
        }
    }


    private static boolean isColaResponse(Class returnType) {
        return  returnType == Response.class || returnType.getGenericSuperclass() == Response.class;
    }
}
