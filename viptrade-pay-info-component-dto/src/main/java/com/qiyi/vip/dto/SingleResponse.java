package com.qiyi.vip.dto;

/**
 * Response with single record to return
 * <p/>
 * Created by <PERSON><PERSON> on 2017/11/1.
 */
public class SingleResponse<T> extends Response {

    private T data;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static SingleResponse buildSuccess() {
        SingleResponse response = new SingleResponse();
        response.setSuccess(true);
        response.setCode("A00000");
        return response;
    }

    public static SingleResponse buildFailure(String errCode, String errMessage) {
        SingleResponse response = new SingleResponse();
        response.setSuccess(false);
        response.setCode(errCode);
        response.setErrMessage(errMessage);
        return response;
    }

    public static <T> SingleResponse<T> of(T data) {
        SingleResponse<T> response = new SingleResponse<>();
        response.setSuccess(true);
        response.setCode("A00000");
        response.setData(data);
        return response;
    }

    public boolean returnSuccess() {
        return "A00000".equals(getCode());
    }

    public boolean returnFailed() {
        return !returnSuccess();
    }

}
