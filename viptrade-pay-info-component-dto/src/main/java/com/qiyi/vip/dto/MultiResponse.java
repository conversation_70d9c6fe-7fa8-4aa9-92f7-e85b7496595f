package com.qiyi.vip.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Response with batch record to return,
 * usually use in conditional query
 * <p/>
 * Created by <PERSON><PERSON> on 2017/11/1.
 */
public class MultiResponse<T> extends Response {

    private static final long serialVersionUID = 1L;

    private Collection<T> data;
    private Collection<T> dataList;

    public List<T> getData() {
        return null == data ? Collections.emptyList() : new ArrayList<>(data);
    }

    public void setData(Collection<T> data) {
        this.data = data;
    }

    public Collection<T> getDataList() {
        return null == dataList ? Collections.emptyList() : new ArrayList<>(dataList);
    }
    public void setDataList(Collection<T> dataList) {
        this.dataList = dataList;
    }

    public boolean isEmpty() {
        return data == null || data.size() == 0;
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }

    public static MultiResponse buildSuccess() {
        MultiResponse response = new MultiResponse();
        response.setCode("A00000");
        response.setSuccess(true);
        return response;
    }

    public static MultiResponse buildFailure(String errCode, String errMessage) {
        MultiResponse response = new MultiResponse();
        response.setSuccess(false);
        response.setCode(errCode);
        response.setErrMessage(errMessage);
        return response;
    }

    public static <T> MultiResponse<T> ofAdmin(Collection<T> data) {
        MultiResponse<T> response = new MultiResponse<>();
        response.setCode("A00000");
        response.setSuccess(true);
        response.setData(data);
        response.setDataList(data);
        return response;
    }

    public static <T> MultiResponse<T> of(Collection<T> data) {
        MultiResponse<T> response = new MultiResponse<>();
        response.setCode("A00000");
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

}
