package com.qiyi.vip.sign;

import lombok.Data;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

@Data
public class ParameterSignProperties {
    private static final Set<String> TIME_FIELD_NAME_PARTS = new HashSet<>();

    private Boolean enabled = true;
    private String key;
    private Boolean skipTimeField = true;
    private Set<String> skippedFields = new HashSet<>();

    public ParameterSignProperties() {
        TIME_FIELD_NAME_PARTS.add("time");
        TIME_FIELD_NAME_PARTS.add("date");
    }

    Boolean isSkippedField(String fieldName) {
        return isContains(fieldName, TIME_FIELD_NAME_PARTS) || isContains(fieldName, skippedFields);
    }

    private boolean isContains(String fieldName, Collection<String> collection) {
        if(fieldName.equals("sign")){
            return true;
        }
        for (String s : collection) {
            if (fieldName.toLowerCase().contains(s.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
}

