package com.qiyi.vip.sign;

import com.google.common.base.Joiner;
import com.qiyi.vip.utils.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;


public class ParameterSignGenerator {
    private static final String TEST_KEY = "test";

    private final ParameterSignProperties properties;

    public ParameterSignGenerator(ParameterSignProperties properties) {
        if (properties == null) {
            ParameterSignProperties testProperties = new ParameterSignProperties();
            testProperties.setEnabled(true);
            testProperties.setKey(TEST_KEY);
            this.properties = testProperties;
        } else {
            this.properties = properties;
        }
    }

    public String generate(Object arg) {
        SortedMap<String, String> parameterMap;
        if (Map.class.isAssignableFrom(arg.getClass())) {
            parameterMap = convertMapToParameterMap(arg);
        } else if (Iterable.class.isAssignableFrom(arg.getClass())) {
            parameterMap = convertIterableToParameterMap(arg);
        } else {
            parameterMap = convertObjectToParameterMap(arg);
        }

        if (Objects.nonNull(parameterMap.get("sign"))) {
            return "error";
        }

        //过滤null&&""
        SortedMap<String,String> parameterMapSort = new TreeMap<>();
        Set<Map.Entry<String,String>> entrys =parameterMap.entrySet();
        Iterator<Map.Entry<String,String>> it = entrys.iterator();
        while(it.hasNext()){
            Map.Entry<String, String> entry = it.next();
            String value = entry.getValue();
            if(!StringUtils.isEmpty(value)){
                parameterMapSort.put(entry.getKey(),entry.getValue());
            }
        }

        String paramString = Joiner.on("&").withKeyValueSeparator("=").join(parameterMapSort);
        paramString += properties.getKey();

        return doHex(paramString);
    }

    private SortedMap<String, String> convertMapToParameterMap(Object arg) {
        return convertMapToParameterMap(arg, null);
    }

    @SuppressWarnings("unchecked")
    private SortedMap<String, String> convertMapToParameterMap(Object arg, Integer index) {
        SortedMap<String, String> parameterMap = new TreeMap<>();
        Map<String, Object> mapArg = (Map<String, Object>) arg;
        for (Map.Entry<String, Object> propEntry : mapArg.entrySet()) {
            if (isSupported(propEntry.getKey(), propEntry.getValue())) {
                String key;
                if (index == null) {
                    key = propEntry.getKey();
                } else {
                    key = propEntry.getKey() + index;
                }

                parameterMap.put(key, propEntry.getValue().toString());
            }
        }
        return parameterMap;
    }

    private boolean isSupported(String key, Object value) {
        return isSupportedType(value) && isSupportedField(key);
    }

    private boolean isSupportedField(String key) {
        return !properties.isSkippedField(key);
    }

    private SortedMap<String, String> convertIterableToParameterMap(Object arg) {
        SortedMap<String, String> parameterMap = new TreeMap<>();
        Iterable<?> mapArg = (Iterable<?>) arg;
        int index = 0;
        for (Object o : mapArg) {
            SortedMap<String, String> current;
            if (Map.class.isAssignableFrom(o.getClass())) {
                current = convertMapToParameterMap(o, index++);
            } else if (Iterable.class.isAssignableFrom(o.getClass())) {
                // Skip the nested collection.
                continue;
            } else {
                current = convertObjectToParameterMap(o, index++);
            }

            parameterMap.putAll(current);
        }

        return parameterMap;
    }

    private SortedMap<String, String> convertObjectToParameterMap(Object arg) {
        return convertObjectToParameterMap(arg, null);
    }

    private SortedMap<String, String> convertObjectToParameterMap(Object arg, Integer index) {
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(arg.getClass());
        SortedMap<String, String> parameterMap = new TreeMap<>();
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            Object value = getPropertyValue(arg, propertyDescriptor);
            if (isSupported(propertyDescriptor.getName(), value)) {
                String key = index == null ? propertyDescriptor.getName() : propertyDescriptor.getName() + index;
                parameterMap.put(key, value.toString());
            }
        }
        return parameterMap;
    }

    private Object getPropertyValue(Object object, PropertyDescriptor propertyDescriptor) {
        Method getter = propertyDescriptor.getReadMethod();
        try {
            return getter.invoke(object);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean isSupportedType(Object value) {
        return value != null &&
                (value instanceof String
                        || value instanceof Integer
                        || value instanceof Long
                        || value instanceof Boolean
                        || value.getClass().isEnum()
                );
    }

    private String doHex(String signString) {
        return SecurityUtil.MD5(signString,StandardCharsets.UTF_8.toString());
    }
}
