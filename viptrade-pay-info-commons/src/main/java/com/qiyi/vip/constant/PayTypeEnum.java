package com.qiyi.vip.constant;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.Validate;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 14:36 2021/3/8
 */
public enum PayTypeEnum {


    WECHATPCDUTV4(3, "micromessenger", 384L),

    WECHAT_V3(0, "micromessenger", 74L),

    ALIPAYV3_H5(3, "aliapp", 301L),

    WAP_CLIENT(0, "aliapp", 36L),
    ;
    public static final List<Long> ONLY_SUPPORTED_IN_ALI_CLIENT = Arrays.asList(415L, 416L);

    /**
     * 自动续费状态 0普通，3签约
     */
    private Integer autoRenew;
    /**
     * 客户端标识
     */
    private String userAgent;
    /**
     * 支付方式
     */
    private Long payType;

    PayTypeEnum(Integer autoRenew, String userAgent, Long payType) {
        this.autoRenew = autoRenew;
        this.userAgent = userAgent;
        this.payType = payType;
    }

    /**
     * 支付方式路由
     */
    public static Long routePayType(String userAgent, Integer autoRenew) {
        Validate.notNull(userAgent, "userAgent is required for route!");
        Validate.notNull(autoRenew, "autoRenew is required for route!");
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()) {
            if (payTypeEnum.autoRenew.equals(autoRenew) && userAgent.toLowerCase().contains(payTypeEnum.userAgent)) {
                return payTypeEnum.payType;
            }
        }
        return null;
    }
}
