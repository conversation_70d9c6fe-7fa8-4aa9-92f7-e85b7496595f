package com.qiyi.vip.constant;

/**
 * <AUTHOR>
 * date: 2022/8/29 19:03
 */
public enum SceneEnum {

    IQIYI_IPHONE("NJw57JdAfV", null),
    IQIYI_IPAD("H29MW5hSu4", null),
    IQIYI_GPHONE("uo3QkzDqNo", null),
    IQIYI_GPAD("gRU1ao8LBi", null),
    WECHAT("SUX7ZMU3VN", 2),
    ALIPAY("ahPNrHHAum", 1),
    BAIDU("DLOIbse3iP", 3),
    H5("NUebQyLbGi", null),
    // 抖音
    DOUYIN("D11yWdJIs7", null),
    // 百度小程序
    BAIDU_APPLETS("DLOIbse3iP", null),
    // 快手小程序
    KUAISHOU_APPLETS("k3qAIuj8r5", null),
    // 快手app版本>=11.7.30时，收银台前端传递新的场景编码，用于拉起微信支付
    KUAISHOU_APPLETS_NEW("kHSZTK6V9x", null),

    // 支付宝小程序
    ALIPAY_APPLETS("88pZFKpTec", null);

    /**
     * 场景编码
     */
    private final String code;

    /**
     * 渠道id
     */
    private final Integer channelId;

    SceneEnum(String code, Integer channelId) {
        this.code = code;
        this.channelId = channelId;
    }

    public String getCode() {
        return this.code;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public static boolean isIosScene(String code) {
        return IQIYI_IPHONE.getCode().equals(code)
                || IQIYI_IPAD.getCode().equals(code);
    }

    public static boolean isAdrScene(String code) {
        return IQIYI_GPHONE.getCode().equals(code)
                || IQIYI_GPAD.getCode().equals(code);
    }

    public static boolean isKuaishouAppletsScene(String code) {
        return KUAISHOU_APPLETS.getCode().equals(code) || KUAISHOU_APPLETS_NEW.getCode().equals(code);
    }

    public static boolean isAlipayAppletsScene(String code) {
        return ALIPAY_APPLETS.getCode().equals(code);
    }


    /**
     * 通过userAgent判断露出的渠道
     * 1、微信内只展示微信支付渠道
     * 2、支付宝内只展示支付宝支付渠道
     * 3、百度浏览器不展示微信渠道
     *
     * @param sceneCode    场景编码
     * @param payChannelId 支付渠道id
     */
    public static boolean isNeedHandle(String sceneCode, Integer payChannelId) {
        if (WECHAT.getCode().equals(sceneCode)) {
            return WECHAT.getChannelId().equals(payChannelId);
        } else if (ALIPAY.getCode().equals(sceneCode)) {
            return ALIPAY.getChannelId().equals(payChannelId);
        }
        return true;
    }

    /**
     * Is in baidu use alipay.
     *
     * @param sceneCode    the scene code
     * @param payChannelId the pay channel id
     * @return the boolean
     */
    public static boolean isInBaiduUseAlipay(String sceneCode, Integer payChannelId) {
        return SceneEnum.BAIDU.getCode().equals(sceneCode) && SceneEnum.ALIPAY.getChannelId().equals(payChannelId);
    }

    public static boolean isInWechatEnv(String sceneCode, Integer payChannelId) {
        return WECHAT.getChannelId().equals(payChannelId) && WECHAT.getCode().equals(sceneCode);
    }
}
