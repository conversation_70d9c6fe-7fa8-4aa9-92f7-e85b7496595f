package com.qiyi.vip.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description:
 * @date Create in 14:36 2021/3/8
 */
public enum UserAgentEnum {


    WECHAT("micromessenger", 2),

    ALIPAY("aliapp", 1),

    BAIDUBOXAPP("baiduboxapp", 3),
    ;


    /**
     * 客户端标识
     */
    private String userAgent;

    /**
     * 渠道id
     */
    private Integer channelId;

    UserAgentEnum(String userAgent, Integer channelId) {
        this.userAgent = userAgent;
        this.channelId = channelId;
    }

    /**
     * 通过userAgent判断露出的渠道
     * 1、微信内只展示微信支付渠道
     * 2、支付宝内只展示支付宝支付渠道
     * 3、百度浏览器不展示微信渠道
     *
     * @param userAgent
     * @param payChannelId
     */
    public static boolean isNeedHandle(String userAgent, Integer payChannelId) {
        if (StringUtils.isBlank(userAgent)) {
            return true;
        }
        String agentLowerCase = userAgent.toLowerCase();
        if (agentLowerCase.contains(WECHAT.getUserAgent().toLowerCase())) {
            return WECHAT.getChannelId().equals(payChannelId);
        } else if (agentLowerCase.contains(ALIPAY.getUserAgent().toLowerCase())) {
            return ALIPAY.getChannelId().equals(payChannelId);
        } else if (agentLowerCase.contains(BAIDUBOXAPP.getUserAgent().toLowerCase())) {
            return !WECHAT.getChannelId().equals(payChannelId);
        }
        return true;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public static  boolean isInWechatEnv(String userAgent, Integer payChannelId){
        return WECHAT.getChannelId().equals(payChannelId) && userAgent.contains(WECHAT.getUserAgent());
    }

}
