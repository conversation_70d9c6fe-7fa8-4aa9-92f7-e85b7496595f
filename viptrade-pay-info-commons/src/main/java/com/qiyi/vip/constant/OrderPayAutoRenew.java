package com.qiyi.vip.constant;

/**
 * <AUTHOR>
 */

public enum OrderPayAutoRenew {
    // Buy a normal VIP product and select the auto renew check box.
    NORMAL_ORDER_AND_AUTO_RENEW(1),

    // Buy an one month auto renew VIP product.
    OPEN_AUTO_RENEW_ONE_MONTH(2),

    // The newest auto renew request type and the auto renew unit (how much months) will decide
    // by the amount value.
    OPEN_AUTO_RENEW(3);

    private Integer value;

    public Integer getValue() {
        return value;
    }

    OrderPayAutoRenew(Integer value) {
        this.value = value;
    }

    public static boolean isAutoRenewPayRequest(Integer payAutoRenew) {
        for (OrderPayAutoRenew orderPayAutoRenew : OrderPayAutoRenew.values()) {
            if (orderPayAutoRenew.value.equals(payAutoRenew)) {
                return true;
            }
        }
        return false;
    }
}