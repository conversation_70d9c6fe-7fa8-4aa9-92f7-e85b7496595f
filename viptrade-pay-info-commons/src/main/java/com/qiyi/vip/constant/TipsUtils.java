package com.qiyi.vip.constant;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @auther: guojing
 * @date: 2023/2/10 5:41 PM
 * @description:
 */
public class TipsUtils {

    public static final Map<Integer, String> AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS = Maps.newHashMap();
    static {
        AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS.put(1, "到期按每月%s元自动续费，可随时取消");
        AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS.put(3, "到期按每季%s元自动续费，可随时取消");
        AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS.put(12, "到期按每年%s元自动续费，可随时取消");
    }

}
