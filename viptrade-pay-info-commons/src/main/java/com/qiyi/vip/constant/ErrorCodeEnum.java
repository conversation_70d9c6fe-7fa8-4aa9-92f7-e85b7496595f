package com.qiyi.vip.constant;

/**
 * <AUTHOR>
 * @date 2021/3/22 5:44 PM
 */
public enum ErrorCodeEnum {
    PARAMETER_ERR("PARAM_ERROR","参数错误!"),
    DUPLICATE_ERR("DUPLICATE_ERROR","数据已存在!"),

    INSERT_ERR("INSERT_ERROR","插入数据失败!"),
    UPDATE_ERR("UPDATE_ERROR","更新数据失败!"),
    SAVE_ERR("SAVE_ERROR","保存数据失败!"),
    DATA_NOT_FOUND("DATA_NOT_FOUND","数据不存在");


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    private String code;
    private String name;

    ErrorCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
