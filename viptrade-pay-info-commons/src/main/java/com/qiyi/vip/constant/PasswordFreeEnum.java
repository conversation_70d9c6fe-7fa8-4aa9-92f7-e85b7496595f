package com.qiyi.vip.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 0，1，2的定义为老的免密交互方式
 * <AUTHOR>
 * @date 2020/10/10 2:16 PM
 */
public enum PasswordFreeEnum {
    PASSWORDFREESIGNPAY("免密签约支付","0"),
    BASICPAY("普通支付","2"),
    PASSWORDFREEPAY("免密支付","1"),

    PASSWORD_FREE_PAY_NEW("免密支付", "3"),
    BASIC_PAY_NEW("普通支付", "4");

    private String name;
    private String value;

    PasswordFreeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public boolean isPasswordFreePay(String isPasswordFree){
        if(StringUtils.isEmpty(isPasswordFree)){
            return false;
        }
        return this.value.equals(isPasswordFree);
    }
}
