package com.qiyi.vip.vo;

import org.apache.commons.lang3.StringUtils;

/**
 * 封装移动端版本号，用于比较版本，如7.11.1 Created by liuxueping on 2016/11/15.
 */
public class ClientVersion implements Comparable {

    private String[] valueArray;
    private String value;

    public ClientVersion(String value) {
        this.value = value;
        initValueArray();
    }

    private void initValueArray() {
        if (StringUtils.isNotBlank(value)) {
            valueArray = value.split("\\.");
        }
    }

    @Override
    public int compareTo(Object o) {
        ClientVersion clientVersion = (ClientVersion) o;
        int len1 = valueArray.length;
        int len2 = clientVersion.getValueArray().length;
        int lim = Math.min(len1, len2);
        String array1[] = valueArray;
        String array2[] = clientVersion.getValueArray();

        int k = 0;
        while (k < lim) {
            Integer v1 = Integer.parseInt(array1[k]);
            Integer v2 = Integer.parseInt(array2[k]);
            if (!v1.equals(v2)) {
                return v1 - v2;
            }
            k++;
        }
        return len1 - len2;

    }

    public static int compare(String clientVersion, String supportedVersion) {
        return new ClientVersion(clientVersion).compareTo(new ClientVersion(supportedVersion));
    }

    public String[] getValueArray() {
        return valueArray;
    }

    public void setValueArray(String[] valueArray) {
        this.valueArray = valueArray;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
