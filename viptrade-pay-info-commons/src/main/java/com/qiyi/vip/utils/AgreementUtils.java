package com.qiyi.vip.utils;

import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;

import com.qiyi.vip.constant.TipsUtils;

/**
 * @auther: guojing
 * @date: 2023/2/15 11:08 AM
 * @description:
 */
public class AgreementUtils {

    /**
     * 获取自动续费默认协议文案
     * @param amount
     * @param renewPrice 单位: 分
     */
    public static String getDefaultAgreementDesc(Integer amount, Integer renewPrice) {
        String renewPriceStr = NumberFormatUtils.formatToStr(renewPrice, "#.#");
        String descriptionTmp = TipsUtils.AGREEMENT_DESCRIPTION_OF_AUTO_RENEW_TIPS.get(amount);
        if (descriptionTmp == null) {
            return null;
        }
        return String.format(descriptionTmp, renewPriceStr);
    }

    public static Map<Integer, String> amountNameMap(){
        HashMap<Integer, String> amountNameMap = Maps.newHashMap();
        amountNameMap.put(1, "包月");
        amountNameMap.put(3, "包季");
        amountNameMap.put(12, "包年");
        return amountNameMap;
    }

    public static String buildIosAgreementName(String vipTypeName, Integer amount, Integer price) {
        String yyyyMM = DateHelper.formatYyyyMM(DateHelper.getCurrentTime());
        String amountText = AgreementUtils.amountNameMap().get(amount);
        String priceText = NumberFormatUtils.formatToStr(price, "#.#");
        return String.format("%s_苹果%s自动续费%s_%s", yyyyMM, vipTypeName, amountText, priceText);
    }

    public static Integer genAgreementNoByDutType(Integer dutType, Integer amount) {
        String dutTypeStr = String.valueOf(dutType);
        String amountStr = String.valueOf(amount);
        if (dutTypeStr.length() < 4) {
            dutTypeStr = String.format("%04d", dutType);
        }
        if (amountStr.length() < 2) {
            amountStr = String.format("%02d", amount);
        }
        String result = "2" + dutTypeStr + amountStr;
        return Integer.parseInt(result);
    }

}
