package com.qiyi.vip.utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @auther: guojing
 * @date: 2023/2/10 5:40 PM
 * @description:
 */
public class NumberFormatUtils {

    /**
     * 格式化数字
     * @param number 单位：分
     * @param pattern
     * @return 返回的数值单位为元
     */
    public static String formatToStr(Integer number, String pattern) {
        if (number == null) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat(pattern);
        df.setRoundingMode(RoundingMode.HALF_UP);
        return df.format(number * 1.0 / 100);
    }

}
