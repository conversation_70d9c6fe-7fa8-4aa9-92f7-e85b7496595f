package com.qiyi.vip.utils;


import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR> k<PERSON><PERSON>qiang
 * DateTime: 18-6-21 下午8:49
 * Mail:<EMAIL>
 * Description: desc
 */
public class VipStringUtil {
    /**
     * convert a string to Map used by Google Guava Splitter
     */
    public static Map<String, String> stringToMap(String value, String splitter, String kvSplitter) {
        if (StringUtils.isBlank(value)) {
            return Collections.emptyMap();
        }
        return Splitter
                .on(splitter)
                .trimResults()
                .omitEmptyStrings()
                .withKeyValueSeparator(kvSplitter)
                .split(value);
    }
}