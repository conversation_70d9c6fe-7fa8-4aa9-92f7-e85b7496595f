package com.qiyi.vip.utils;

import com.google.common.base.Joiner;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: TODO
 * @date 2021/3/12 2:18 PM
 */
public class RequestUtil {

    /**
     * 将http请求参数转为Map结构, 值为数组类型的参数值以','分隔
     *
     * @param request
     * @return {@link Map}
     */
    public static Map<String, String> buildRequestParamMap(HttpServletRequest request) {
        Map<String, String[]> originParamMap = request.getParameterMap();
        Set<Map.Entry<String, String[]>> entrys = originParamMap.entrySet();
        Map<String, String> paramMap = new HashMap<>();
        for (Map.Entry<String, String[]> entry : entrys) {
            paramMap.put(entry.getKey(), buildCommaSplitValues(entry.getValue()));
        }
        return paramMap;
    }

    private static String buildCommaSplitValues(String[] paramValue) {
        if (null == paramValue || 0 == paramValue.length) {
            return "";
        }
        if (1 == paramValue.length) {
            return paramValue[0];
        }
        return Joiner.on(",").join(paramValue);
    }
}
