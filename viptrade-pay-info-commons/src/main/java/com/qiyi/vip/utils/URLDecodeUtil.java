package com.qiyi.vip.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/7/30 15:04
 */
@Slf4j
public class URLDecodeUtil {

    // 检测字符串是否包含URL编码的字符
    private static boolean isURLEncoded(String value) {
        // 简单检查是否包含 '%' 字符
        return value.contains("%");
    }

    // 解码URL编码的字符串
    public static String decodeIfEncoded(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }

        // 先检测是否可能是URL编码过的
        if (isURLEncoded(value)) {
            try {
                // 尝试解码
                String decoded = URLDecoder.decode(value, StandardCharsets.UTF_8.name());

                // 检查是否真的解码成功（即解码前后不一样）
                if (!value.equals(decoded)) {
                    return decoded;
                }
            } catch (Exception e) {
                log.error("URL decode error", e);
            }
        }

        // 如果没有编码或者解码失败，返回原值
        return value;
    }

}
