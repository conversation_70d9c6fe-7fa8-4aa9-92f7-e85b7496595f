package com.qiyi.vip.utils;

import org.apache.commons.lang3.StringUtils;

import com.qiyi.vip.vo.ClientVersion;

/**
 * <AUTHOR> date:  2022/9/8 14:37
 */
public class ClientVersionUtil {

    public static boolean lt(String currentVersion, String commonVersion) {
        if (StringUtils.isEmpty(currentVersion) || StringUtils.isEmpty(commonVersion)) {
            return false;
        }

        ClientVersion currentClientVersion = new ClientVersion(currentVersion);
        ClientVersion commonClientVersion = new ClientVersion(commonVersion);

        return commonClientVersion.compareTo(currentClientVersion) > 0;
    }


    /**
     * 判断当前版本是否在指定的版本区间中，左闭右开区间
     *
     * @param currentVersion 当前版本
     * @param lowerVersion 区间的左边界版本
     * @param upperVersion 区间的右边界版本，如果为空则不限制右边界
     * @return 如果当前版本在版本区间内，则返回true，否则返回false
     */
    public static boolean isInVersionRange(String currentVersion, String lowerVersion, String upperVersion) {
        if (StringUtils.isEmpty(currentVersion) || StringUtils.isEmpty(lowerVersion)) {
            return false;
        }

        ClientVersion currentClientVersion = new ClientVersion(currentVersion);
        ClientVersion lowerClientVersion = new ClientVersion(lowerVersion);

        // 判断当前版本是否大于等于左边界
        if (currentClientVersion.compareTo(lowerClientVersion) < 0) {
            return false;
        }

        if (StringUtils.isEmpty(upperVersion)) {
            return true;  // 不限制右边界
        }

        ClientVersion upperClientVersion = new ClientVersion(upperVersion);

        // 判断当前版本是否小于右边界
        return currentClientVersion.compareTo(upperClientVersion) < 0;
    }
}
