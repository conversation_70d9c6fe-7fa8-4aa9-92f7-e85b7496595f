package com.qiyi.vip.utils;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * @auther: guojing
 * @date: 2023/2/7 3:56 PM
 * @description:
 */
public class DateHelper {

    public static Timestamp getCurrentTime() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static Timestamp calculateEndTime(Timestamp begin, long amountToAdd, ChronoUnit unit) {
        LocalDateTime localDateTime = begin.toLocalDateTime();
        LocalDateTime newLocalDateTime = localDateTime.plus(amountToAdd, unit);
        return Timestamp.valueOf(newLocalDateTime);
    }

    public static String formatYyyyMM(Timestamp timestamp) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        Instant instant = Instant.ofEpochMilli(timestamp.getTime());
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return dateTime.format(formatter);
    }

}
