package com.qiyi.vip.enums;

import java.time.temporal.ChronoUnit;

/**
 * 时长单位 Created at: 2021-06-17
 *
 * <AUTHOR>
 */
public enum PeriodUnitEnum {

    /**
     * 天
     */
    DAY(1, ChronoUnit.DAYS, "天"),
    /**
     * 月
     */
    MONTH(2, ChronoUnit.MONTHS, "月"),
    ;
    private int value;

    private ChronoUnit unit;

    private String unitChar;

    PeriodUnitEnum(int value, ChronoUnit unit, String unitChar) {
        this.value = value;
        this.unit = unit;
        this.unitChar = unitChar;
    }

    public int getValue() {
        return value;
    }

    public ChronoUnit getUnit() {
        return unit;
    }

    public String getUnitChar() {
        return unitChar;
    }

    public static PeriodUnitEnum valueOf(Integer value) {
        if (value == null) {
            return null;
        }
        for (PeriodUnitEnum periodUnit : values()) {
            if (periodUnit.value == value) {
                return periodUnit;
            }
        }
        return null;
    }

}
