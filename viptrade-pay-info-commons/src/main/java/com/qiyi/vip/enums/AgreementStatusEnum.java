package com.qiyi.vip.enums;

/**
 * 协议状态
 * Created at: 2021-06-17
 *
 * <AUTHOR>
 */
public enum AgreementStatusEnum {

    /**
     * 未签约
     */
    UNSIGNED(-1, "未签约"),
    /**
     * 已失效
     */
    INVALID(0, "已失效"),
    /**
     * 生效中
     */
    VALID(1, "生效中"),
    /**
     * 自动续费时长切换
     */
    RENEW_EXCHANGE(2, "自动续费时长切换"),
    /**
     * 结算中
     */
    SETTLING(4, "结算中"),
    /**
     * 履约完成
     */
    FINISHED(5, "履约完成"),
    /**
     * 待解约
     */
    CANCEL_PENDING(6, "待解约"),
    ;

    private int value;
    private String desc;

    AgreementStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AgreementStatusEnum valueOf(Integer value) {
        if (value == null) {
            return null;
        }
        for (AgreementStatusEnum status : values()) {
            if (status.value == value) {
                return status;
            }
        }
        return null;
    }
}
