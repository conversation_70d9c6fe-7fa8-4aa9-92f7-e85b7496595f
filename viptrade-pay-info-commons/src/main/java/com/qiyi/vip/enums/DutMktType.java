package com.qiyi.vip.enums;

/**
 * Created at: 2022-08-02
 *
 * <AUTHOR>
 */
public enum DutMktType {

    FIRST_X_FAVOR(1, "首X期每期Y元"),
    ;

    private Integer value;
    private String desc;

    DutMktType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static DutMktType parseOf(Integer value) {
        if (value == null) {
            return null;
        }
        for (DutMktType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }

}
