package com.qiyi.vip.enums;

/**
 * 协议类型
 * Created at: 2021-06-17
 *
 * <AUTHOR>
 */
public enum AgreementTypeEnum {

    AUTO_RENEW(1, "自动续费", false, 1),
    ZHIMA_GO(2, "芝麻GO", true, 2),
    WECHAT_PAY_SCORE(3, "微信先看后付", false, 2),
    ;

    private int value;
    private String desc;
    /**
     * 是否需要结算
     */
    private boolean needSettle;
    /**
     * 拉起方式
     * 1: 签约支付, 2: 纯签约
     */
    private int pullUp;

    AgreementTypeEnum(int value, String desc, boolean needSettle, int pullUp) {
        this.value = value;
        this.desc = desc;
        this.needSettle = needSettle;
        this.pullUp = pullUp;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public int getPullUp() {
        return pullUp;
    }

    public boolean needSettle() {
        return needSettle;
    }

    public boolean needNotSettle(){
        return !needSettle;
    }

    public static AgreementTypeEnum valueOf(Integer type) {
        if (type == null) {
            return null;
        }
        for (AgreementTypeEnum agreementType : values()) {
            if (agreementType.value == type) {
                return agreementType;
            }
        }
        return null;
    }

}
