package com.qiyi.vip.enums;

/**
 * 定价策略
 * Created at: 2021-06-17
 *
 * <AUTHOR>
 */
public enum PricingStrategyEnum {

    /**
     * 固定单价
     */
    FIXED(1),
    /**
     * 分期定价
     */
    PERIOD(2),
    /**
     * 阶梯定价
     */
    LADDERED(3),
    ;

    private int value;

    PricingStrategyEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PricingStrategyEnum valueOf(Integer value) {
        if (value == null) {
            return null;
        }
        for (PricingStrategyEnum strategy : values()) {
            if (strategy.value == value) {
                return strategy;
            }
        }
        return null;
    }

}
